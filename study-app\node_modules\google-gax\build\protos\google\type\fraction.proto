// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/fraction;fraction";
option java_multiple_files = true;
option java_outer_classname = "FractionProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// Represents a fraction in terms of a numerator divided by a denominator.
message Fraction {
  // The numerator in the fraction, e.g. 2 in 2/3.
  int64 numerator = 1;

  // The value by which the numerator is divided, e.g. 3 in 2/3. Must be
  // positive.
  int64 denominator = 2;
}
