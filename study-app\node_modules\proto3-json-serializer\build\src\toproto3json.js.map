{"version": 3, "file": "toproto3json.js", "sourceRoot": "", "sources": ["../../typescript/src/toproto3json.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;AAsEjC,oCAgIC;AAlMD,+BAAyD;AACzD,mCAA0C;AAC1C,iCAA+D;AAC/D,iCAA0E;AAC1E,mCAOiB;AACjB,yCAAwE;AACxE,2CAA2E;AAC3E,yCAMoB;AACpB,2CAA2E;AAM3E,qFAAqF;AACrF,SAAS,kBAAkB,CAAC,KAAyB;IACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,IAAI,KAAK,EAAE,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAQ,KAAkB,CAAC,QAAQ,EAAE,CAAC;QACxC,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,iDAAiD,KAAK,EAAE,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,iDAAiD;AACjD,SAAS,yBAAyB,CAChC,IAA0C,EAC1C,KAA0C,EAC1C,OAA6B;IAE7B,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QAC7B,OAAO,WAAW,CAAC,IAAI,EAAE,KAAkB,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;IACD,IAAI,IAAI,EAAE,CAAC;QACT,OAAO,YAAY,CAAC,KAAyB,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACnC,CAAC;AAED,oCAAoC;AACpC,SAAS,WAAW,CAClB,IAAmB,EACnB,KAAgB,EAChB,OAA6B;IAE7B,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;QAC1B,OAAO,IAAA,+BAAwB,EAAC,IAAI,EAAE,KAAkB,CAAC,CAAC;IAC5D,CAAC;SAAM,CAAC;QACN,OAAO,IAAA,+BAAwB,EAAC,IAAI,EAAE,KAAkB,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAC1B,GAAqB,EACrB,OAA6B;IAE7B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CACb,2HAA2H,CAC5H,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,UAAU,EAAE,CAAC;IACrB,MAAM,QAAQ,GAAG,IAAA,gCAAyB,EAAC,OAAO,CAAC,CAAC;IAEpD,mDAAmD;IACnD,kEAAkE;IAClE,IAAI,QAAQ,KAAK,sBAAsB,EAAE,CAAC;QACxC,OAAO,IAAA,mCAA6B,EAClC,GAA6B,EAC7B,OAAO,CACR,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,wBAAwB,EAAE,CAAC;QAC1C,OAAO,IAAA,uCAA+B,EAAC,GAA+B,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,QAAQ,KAAK,yBAAyB,EAAE,CAAC;QAC3C,OAAO,IAAA,wCAAgC,EAAC,GAAgC,CAAC,CAAC;IAC5E,CAAC;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE,CAAC;QAC9C,OAAO,IAAA,2CAAmC,EACxC,GAAmC,CACpC,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,2BAA2B,EAAE,CAAC;QAC7C,OAAO,IAAA,6CAAkC,EACvC,GAAkC,CACnC,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE,CAAC;QAC9C,OAAO,IAAA,+CAAmC,EACxC,GAAmC,CACpC,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE,CAAC;QAC9C,OAAO,IAAA,+CAAmC,EACxC,GAAmC,CACpC,CAAC;IACJ,CAAC;IAED,IAAI,mBAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAA,8BAAmB,EACxB,GACsD,CACvD,CAAC;IACJ,CAAC;IAED,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,iBAAiB,GAAG,KAAK,CAAC,YAAY,CAAC;QAC7C,MAAM,2BAA2B,GAAG,iBAAiB;YACnD,CAAC,CAAC,IAAA,gCAAyB,EAAC,iBAAiB,CAAC;YAC9C,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACnB,SAAS;QACX,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,wCAAwC;gBACxC,SAAS;YACX,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBAChC,OAAO,yBAAyB,CAAC,iBAAiB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YACH,SAAS;QACX,CAAC;QACD,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;YACd,MAAM,GAAG,GAAe,EAAE,CAAC;YAC3B,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvD,GAAG,CAAC,MAAM,CAAC,GAAG,yBAAyB,CACrC,iBAAiB,EACjB,QAAqB,EACrB,OAAO,CACR,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;YAClB,SAAS;QACX,CAAC;QACD,IAAI,2BAA2B,KAAK,4BAA4B,EAAE,CAAC;YACjE,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACnB,SAAS;QACX,CAAC;QACD,IAAI,iBAAiB,IAAI,QAAQ,IAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACzE,MAAM,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,iBAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC7D,SAAS;QACX,CAAC;QACD,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC3C,SAAS;QACX,CAAC;QACD,IACE,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,SAAS;YAC1B,KAAK,KAAK,IAAI,EACd,CAAC;YACD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAC/B,SAAS;YACX,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,SAAS;QACX,CAAC;QACD,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,UAAU,EAAE,CAAC;YAC1D,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;YACvC,SAAS;QACX,CAAC;QACD,MAAM,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACxC,SAAS;IACX,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}