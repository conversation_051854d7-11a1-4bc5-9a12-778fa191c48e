const CHUNK_PUBLIC_PATH = "server/app/api/admin/books/[id]/clear-ocr/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__b3772bdf._.js");
runtime.loadChunk("server/chunks/node_modules_next_f45984ad._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_@google-cloud_vision_build_protos_protos_json_cc6fa92d._.js");
runtime.loadChunk("server/chunks/node_modules_@google-cloud_vision_build_protos_protos_8b879436.js");
runtime.loadChunk("server/chunks/node_modules_@google-cloud_vision_build_src_2f59e12b._.js");
runtime.loadChunk("server/chunks/node_modules_@google-cloud_vision_package_json_31721669._.js");
runtime.loadChunk("server/chunks/node_modules_google-gax_1a2937eb._.js");
runtime.loadChunk("server/chunks/node_modules_protobufjs_d063a617._.js");
runtime.loadChunk("server/chunks/node_modules_google-auth-library_6afb2aab._.js");
runtime.loadChunk("server/chunks/node_modules_@grpc_grpc-js_eb53fed0._.js");
runtime.loadChunk("server/chunks/node_modules_8dd147f0._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__61335094._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/books/[id]/clear-ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/[id]/clear-ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/[id]/clear-ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
