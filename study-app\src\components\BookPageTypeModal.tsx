'use client';

import { useState } from 'react';

interface BookImage {
  id: number;
  original_name: string;
  page_number: number;
  page_type: string;
}

interface BookPageTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: BookImage[];
  selectedImageIds: number[];
  onSave: (updates: { imageId: number; pageType: string }[]) => Promise<void>;
}

// Client-side utility function
const getPageTypeColor = (pageType: string): string => {
  switch (pageType) {
    case 'cover':
      return 'bg-purple-100 text-purple-800';
    case 'contents':
      return 'bg-blue-100 text-blue-800';
    case 'unassigned':
      return 'bg-gray-100 text-gray-800';
    default:
      if (pageType.startsWith('chapter-')) {
        return 'bg-green-100 text-green-800';
      }
      return 'bg-gray-100 text-gray-800';
  }
};

export default function BookPageTypeModal({
  isOpen,
  onClose,
  images,
  selectedImageIds,
  onSave
}: BookPageTypeModalProps) {
  const [pageTypeAssignments, setPageTypeAssignments] = useState<{ [key: number]: string }>({});
  const [isSaving, setIsSaving] = useState(false);

  if (!isOpen) return null;

  const selectedImages = images.filter(img => selectedImageIds.includes(img.id));

  const pageTypeOptions = [
    { value: 'cover', label: 'Cover Page', color: 'bg-purple-100 text-purple-800' },
    { value: 'contents', label: 'Contents/Index', color: 'bg-blue-100 text-blue-800' },
    { value: 'unassigned', label: 'Unassigned', color: 'bg-gray-100 text-gray-800' },
    ...Array.from({length: 30}, (_, i) => ({
      value: `chapter-${i + 1}`,
      label: `Chapter ${i + 1}`,
      color: 'bg-green-100 text-green-800'
    }))
  ];

  const handlePageTypeChange = (imageId: number, pageType: string) => {
    setPageTypeAssignments(prev => ({
      ...prev,
      [imageId]: pageType
    }));
  };

  const handleBulkAssign = (pageType: string) => {
    const newAssignments: { [key: number]: string } = {};
    selectedImageIds.forEach(imageId => {
      newAssignments[imageId] = pageType;
    });
    setPageTypeAssignments(newAssignments);
  };

  const handleSequentialChapterAssign = () => {
    const newAssignments: { [key: number]: string } = {};
    let chapterNum = 1;

    selectedImages
      .sort((a, b) => a.page_number - b.page_number)
      .forEach((image, index) => {
        if (index === 0) {
          // First selected image becomes chapter start
          newAssignments[image.id] = `chapter-${chapterNum}`;
        } else {
          // Subsequent images continue the chapter
          newAssignments[image.id] = `chapter-${chapterNum}`;
        }
      });

    setPageTypeAssignments(newAssignments);
  };

  const handleSmartAutoAssign = () => {
    const newAssignments: { [key: number]: string } = {};
    const sortedImages = selectedImages.sort((a, b) => a.page_number - b.page_number);

    sortedImages.forEach((image, index) => {
      if (index === 0 && image.page_number === 1) {
        // First page is likely cover
        newAssignments[image.id] = 'cover';
      } else if (index === 1 && image.page_number === 2) {
        // Second page is likely contents
        newAssignments[image.id] = 'contents';
      } else {
        // Remaining pages start with chapter 1
        newAssignments[image.id] = 'chapter-1';
      }
    });

    setPageTypeAssignments(newAssignments);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const updates = Object.entries(pageTypeAssignments).map(([imageId, pageType]) => ({
        imageId: parseInt(imageId),
        pageType
      }));

      await onSave(updates);
      setPageTypeAssignments({});
      onClose();
    } catch (error) {
      console.error('Failed to save page types:', error);
    } finally {
      setIsSaving(false);
    }
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Assign Page Types ({selectedImageIds.length} images)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">Close</span>
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Bulk Assignment Options */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Assign All Selected:</h3>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleBulkAssign('cover')}
                className="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded hover:bg-purple-200"
              >
                Cover
              </button>
              <button
                onClick={() => handleBulkAssign('contents')}
                className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded hover:bg-blue-200"
              >
                Contents
              </button>
              <button
                onClick={() => handleBulkAssign('chapter-1')}
                className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded hover:bg-green-200"
              >
                Chapter 1
              </button>
              <button
                onClick={handleSequentialChapterAssign}
                className="px-3 py-1 bg-indigo-100 text-indigo-800 text-sm rounded hover:bg-indigo-200"
              >
                Sequential Chapters
              </button>
              <button
                onClick={handleSmartAutoAssign}
                className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded hover:bg-yellow-200"
              >
                Smart Auto-Assign
              </button>
              <button
                onClick={() => handleBulkAssign('unassigned')}
                className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded hover:bg-gray-200"
              >
                Unassigned
              </button>
            </div>
          </div>

          {/* Individual Image Assignments */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-900">Individual Assignments:</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedImages
                .sort((a, b) => a.page_number - b.page_number)
                .map((image) => (
                <div key={image.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">
                    <img
                      src={`/api/admin/images/${image.id}/preview`}
                      alt={image.original_name}
                      className="w-16 h-20 object-cover rounded"
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      Page {image.page_number}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {image.original_name}
                    </div>
                    
                    <div className="mt-2">
                      <select
                        value={pageTypeAssignments[image.id] || image.page_type}
                        onChange={(e) => handlePageTypeChange(image.id, e.target.value)}
                        className="w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                      >
                        {pageTypeOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Current Assignment Display */}
                    <div className="mt-1">
                      <span className={`inline-block text-xs px-2 py-1 rounded ${getPageTypeColor(pageTypeAssignments[image.id] || image.page_type)}`}>
                        Current: {pageTypeAssignments[image.id] || image.page_type}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving || Object.keys(pageTypeAssignments).length === 0}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? 'Saving...' : `Save Changes (${Object.keys(pageTypeAssignments).length})`}
          </button>
        </div>
      </div>
    </div>
  );
}
