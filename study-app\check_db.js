const db = require('better-sqlite3')('data/study_app.db');

console.log('All tables:');
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log(tables);

console.log('\nChecking for OCR-related tables:');
const ocrTables = tables.filter(t => t.name.toLowerCase().includes('ocr'));
console.log(ocrTables);

if (ocrTables.length > 0) {
  console.log('\nSample OCR data with image info:');
  const sample = db.prepare(`
    SELECT
      ocr.id,
      ocr.image_id,
      ocr.content,
      ocr.processed,
      i.original_name,
      i.page_type,
      i.upload_order,
      LENGTH(ocr.content) as content_length
    FROM ocr_text ocr
    JOIN images i ON ocr.image_id = i.id
    ORDER BY i.upload_order
    LIMIT 5
  `).all();
  console.log(sample);

  console.log('\nTotal OCR records:');
  const count = db.prepare(`SELECT COUNT(*) as count FROM ${ocrTables[0].name}`).get();
  console.log(count);

  console.log('\nOCR content length distribution:');
  const lengths = db.prepare(`
    SELECT
      CASE
        WHEN LENGTH(content) < 50 THEN 'Very Short (<50 chars)'
        WHEN LENGTH(content) < 200 THEN 'Short (50-200 chars)'
        WHEN LENGTH(content) < 1000 THEN 'Medium (200-1000 chars)'
        ELSE 'Long (>1000 chars)'
      END as length_category,
      COUNT(*) as count
    FROM ocr_text
    GROUP BY length_category
  `).all();
  console.log(lengths);
}

console.log('\nChecking images table:');
const images = db.prepare("SELECT COUNT(*) as count FROM images").get();
console.log('Total images:', images);

console.log('\nDeleting OCR data for image 379 to test real OCR...');
const deleteResult = db.prepare("DELETE FROM ocr_text WHERE image_id = 379").run();
console.log('Deleted OCR records:', deleteResult.changes);

console.log('\nVerifying deletion:');
const checkDeleted = db.prepare("SELECT * FROM ocr_text WHERE image_id = 379").get();
console.log('OCR data for image 379:', checkDeleted);

db.close();
