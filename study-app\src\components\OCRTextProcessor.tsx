'use client';

import { useState } from 'react';
import { processOCRText, getProcessingPreview, TextProcessingOptions, DEFAULT_PROCESSING_OPTIONS } from '@/lib/text-processing';

interface OCRTextProcessorProps {
  originalText: string;
  imageId: number;
  onTextUpdate?: (newText: string) => void;
}

export default function OCRTextProcessor({ originalText, imageId, onTextUpdate }: OCRTextProcessorProps) {
  const [showProcessor, setShowProcessor] = useState(false);
  const [processingOptions, setProcessingOptions] = useState<TextProcessingOptions>(DEFAULT_PROCESSING_OPTIONS);
  const [processedText, setProcessedText] = useState(originalText);
  const [isEditing, setIsEditing] = useState(false);
  const [editedText, setEditedText] = useState(originalText);

  const handleProcessText = () => {
    const processed = processOCRText(originalText, processingOptions);
    setProcessedText(processed);
    setEditedText(processed);
    if (onTextUpdate) {
      onTextUpdate(processed);
    }
  };

  const handleSaveEdit = async () => {
    try {
      const response = await fetch(`/api/admin/images/${imageId}/ocr`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: editedText }),
      });

      if (response.ok) {
        setProcessedText(editedText);
        setIsEditing(false);
        if (onTextUpdate) {
          onTextUpdate(editedText);
        }
      } else {
        alert('Failed to save text changes');
      }
    } catch (error) {
      console.error('Error saving text:', error);
      alert('Failed to save text changes');
    }
  };

  const preview = getProcessingPreview(originalText, processingOptions);

  return (
    <div className="border rounded-lg p-4 bg-gray-50">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Text Processing & Editing</h3>
        <button
          onClick={() => setShowProcessor(!showProcessor)}
          className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
        >
          {showProcessor ? 'Hide' : 'Show'} Options
        </button>
      </div>

      {showProcessor && (
        <div className="space-y-4">
          {/* Processing Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Processing Options</h4>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={processingOptions.fixLineBreaks}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, fixLineBreaks: e.target.checked }))}
                />
                <span className="text-sm text-gray-900">Fix line breaks & word continuity</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={processingOptions.mergeParagraphs}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, mergeParagraphs: e.target.checked }))}
                />
                <span className="text-sm text-gray-900">Merge broken paragraphs</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={processingOptions.removeExtraSpaces}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, removeExtraSpaces: e.target.checked }))}
                />
                <span className="text-sm text-gray-900">Remove extra spaces</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={processingOptions.fixCapitalization}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, fixCapitalization: e.target.checked }))}
                />
                <span className="text-sm text-gray-900">Fix capitalization</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={processingOptions.preserveFormatting}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, preserveFormatting: e.target.checked }))}
                />
                <span className="text-sm text-gray-900">Preserve original formatting</span>
              </label>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Processing Stats</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div>Original: {preview.changes.originalWords} words, {preview.changes.originalLines} lines</div>
                <div>Processed: {preview.changes.processedWords} words, {preview.changes.processedLines} lines</div>
                <div>Characters: {preview.changes.originalLength} → {preview.changes.processedLength}</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              onClick={handleProcessText}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Apply Processing
            </button>
            
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              {isEditing ? 'Cancel Edit' : 'Manual Edit'}
            </button>

            <button
              onClick={() => {
                setProcessingOptions(DEFAULT_PROCESSING_OPTIONS);
                setProcessedText(originalText);
                setEditedText(originalText);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Reset
            </button>
          </div>
        </div>
      )}

      {/* Text Display/Edit Area */}
      <div className="mt-4">
        {isEditing ? (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="font-medium text-gray-900">Edit Text</h4>
              <div className="space-x-2">
                <button
                  onClick={handleSaveEdit}
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                >
                  Save Changes
                </button>
                <button
                  onClick={() => {
                    setEditedText(processedText);
                    setIsEditing(false);
                  }}
                  className="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                >
                  Cancel
                </button>
              </div>
            </div>
            <textarea
              value={editedText}
              onChange={(e) => setEditedText(e.target.value)}
              className="w-full h-64 p-3 border rounded font-mono text-sm text-gray-900 bg-white"
              placeholder="Edit the OCR text here..."
            />
          </div>
        ) : (
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Processed Text</h4>
            <div className="bg-white p-3 border rounded max-h-64 overflow-y-auto">
              <pre className="whitespace-pre-wrap text-sm font-mono text-gray-900">{processedText}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
