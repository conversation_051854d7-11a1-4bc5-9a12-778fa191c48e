{"nested": {"google": {"nested": {"cloud": {"nested": {"compute": {"nested": {"v1": {"nested": {"Operation": {"oneofs": {"_clientOperationId": {"oneof": ["clientOperationId"]}, "_creationTimestamp": {"oneof": ["creationTimestamp"]}, "_description": {"oneof": ["description"]}, "_endTime": {"oneof": ["endTime"]}, "_error": {"oneof": ["error"]}, "_httpErrorMessage": {"oneof": ["httpErrorMessage"]}, "_httpErrorStatusCode": {"oneof": ["httpErrorStatusCode"]}, "_id": {"oneof": ["id"]}, "_insertTime": {"oneof": ["insertTime"]}, "_kind": {"oneof": ["kind"]}, "_name": {"oneof": ["name"]}, "_operationType": {"oneof": ["operationType"]}, "_progress": {"oneof": ["progress"]}, "_region": {"oneof": ["region"]}, "_selfLink": {"oneof": ["selfLink"]}, "_startTime": {"oneof": ["startTime"]}, "_status": {"oneof": ["status"]}, "_statusMessage": {"oneof": ["statusMessage"]}, "_targetId": {"oneof": ["targetId"]}, "_targetLink": {"oneof": ["targetLink"]}, "_user": {"oneof": ["user"]}, "_zone": {"oneof": ["zone"]}}, "fields": {"clientOperationId": {"type": "string", "id": 297240295, "options": {"proto3_optional": true}}, "creationTimestamp": {"type": "string", "id": 30525366, "options": {"proto3_optional": true}}, "description": {"type": "string", "id": 422937596, "options": {"proto3_optional": true}}, "endTime": {"type": "string", "id": 114938801, "options": {"proto3_optional": true}}, "error": {"type": "Error", "id": 96784904, "options": {"proto3_optional": true}}, "httpErrorMessage": {"type": "string", "id": 202521945, "options": {"(operation_field)": "ERROR_MESSAGE", "proto3_optional": true}}, "httpErrorStatusCode": {"type": "int32", "id": 312345196, "options": {"(operation_field)": "ERROR_CODE", "proto3_optional": true}}, "id": {"type": "string", "id": 3355, "options": {"(operation_field)": "NAME", "proto3_optional": true}}, "insertTime": {"type": "string", "id": 433722515, "options": {"proto3_optional": true}}, "kind": {"type": "string", "id": 3292052, "options": {"proto3_optional": true}}, "name": {"type": "string", "id": 3373707, "options": {"proto3_optional": true}}, "operationType": {"type": "string", "id": 177650450, "options": {"proto3_optional": true}}, "progress": {"type": "int32", "id": 72663597, "options": {"proto3_optional": true}}, "region": {"type": "string", "id": 138946292, "options": {"proto3_optional": true}}, "selfLink": {"type": "string", "id": 456214797, "options": {"proto3_optional": true}}, "startTime": {"type": "string", "id": 37467274, "options": {"proto3_optional": true}}, "status": {"type": "Status", "id": 181260274, "options": {"(operation_field)": "STATUS", "proto3_optional": true}}, "statusMessage": {"type": "string", "id": 297428154, "options": {"proto3_optional": true}}, "targetId": {"type": "string", "id": 258165385, "options": {"proto3_optional": true}}, "targetLink": {"type": "string", "id": 62671336, "options": {"proto3_optional": true}}, "user": {"type": "string", "id": 3599307, "options": {"proto3_optional": true}}, "warnings": {"rule": "repeated", "type": "Warnings", "id": 498091095}, "zone": {"type": "string", "id": 3744684, "options": {"proto3_optional": true}}}, "nested": {"Status": {"values": {"UNDEFINED_STATUS": 0, "DONE": 2104194, "PENDING": 35394935, "RUNNING": 121282975}}}}, "Errors": {"oneofs": {"_code": {"oneof": ["code"]}, "_location": {"oneof": ["location"]}, "_message": {"oneof": ["message"]}}, "fields": {"code": {"type": "string", "id": 3059181, "options": {"proto3_optional": true}}, "location": {"type": "string", "id": 290430901, "options": {"proto3_optional": true}}, "message": {"type": "string", "id": 418054151, "options": {"proto3_optional": true}}}}, "Error": {"fields": {"errors": {"rule": "repeated", "type": "Errors", "id": 315977579}}}, "Warnings": {"oneofs": {"_code": {"oneof": ["code"]}, "_message": {"oneof": ["message"]}}, "fields": {"code": {"type": "Code", "id": 3059181, "options": {"proto3_optional": true}}, "data": {"rule": "repeated", "type": "Data", "id": 3076010}, "message": {"type": "string", "id": 418054151, "options": {"proto3_optional": true}}}, "nested": {"Code": {"values": {"UNDEFINED_CODE": 0, "CLEANUP_FAILED": 150308440, "DEPRECATED_RESOURCE_USED": 391835586, "DEPRECATED_TYPE_USED": 346526230, "DISK_SIZE_LARGER_THAN_IMAGE_SIZE": 369442967, "EXPERIMENTAL_TYPE_USED": 451954443, "EXTERNAL_API_WARNING": 175546307, "FIELD_VALUE_OVERRIDEN": 329669423, "INJECTED_KERNELS_DEPRECATED": 417377419, "MISSING_TYPE_DEPENDENCY": 344505463, "NEXT_HOP_ADDRESS_NOT_ASSIGNED": 324964999, "NEXT_HOP_CANNOT_IP_FORWARD": 383382887, "NEXT_HOP_INSTANCE_NOT_FOUND": 464250446, "NEXT_HOP_INSTANCE_NOT_ON_NETWORK": 243758146, "NEXT_HOP_NOT_RUNNING": 417081265, "NOT_CRITICAL_ERROR": 105763924, "NO_RESULTS_ON_PAGE": 30036744, "REQUIRED_TOS_AGREEMENT": 3745539, "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING": 496728641, "RESOURCE_NOT_DELETED": 168598460, "SCHEMA_VALIDATION_IGNORED": 275245642, "SINGLE_INSTANCE_PROPERTY_TEMPLATE": 268305617, "UNDECLARED_PROPERTIES": 390513439, "UNREACHABLE": 13328052}}}}, "Warning": {"oneofs": {"_code": {"oneof": ["code"]}, "_message": {"oneof": ["message"]}}, "fields": {"code": {"type": "Code", "id": 3059181, "options": {"proto3_optional": true}}, "data": {"rule": "repeated", "type": "Data", "id": 3076010}, "message": {"type": "string", "id": 418054151, "options": {"proto3_optional": true}}}, "nested": {"Code": {"values": {"UNDEFINED_CODE": 0, "CLEANUP_FAILED": 150308440, "DEPRECATED_RESOURCE_USED": 391835586, "DEPRECATED_TYPE_USED": 346526230, "DISK_SIZE_LARGER_THAN_IMAGE_SIZE": 369442967, "EXPERIMENTAL_TYPE_USED": 451954443, "EXTERNAL_API_WARNING": 175546307, "FIELD_VALUE_OVERRIDEN": 329669423, "INJECTED_KERNELS_DEPRECATED": 417377419, "LARGE_DEPLOYMENT_WARNING": 481440678, "MISSING_TYPE_DEPENDENCY": 344505463, "NEXT_HOP_ADDRESS_NOT_ASSIGNED": 324964999, "NEXT_HOP_CANNOT_IP_FORWARD": 383382887, "NEXT_HOP_INSTANCE_NOT_FOUND": 464250446, "NEXT_HOP_INSTANCE_NOT_ON_NETWORK": 243758146, "NEXT_HOP_NOT_RUNNING": 417081265, "NOT_CRITICAL_ERROR": 105763924, "NO_RESULTS_ON_PAGE": 30036744, "PARTIAL_SUCCESS": 39966469, "REQUIRED_TOS_AGREEMENT": 3745539, "RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING": 496728641, "RESOURCE_NOT_DELETED": 168598460, "SCHEMA_VALIDATION_IGNORED": 275245642, "SINGLE_INSTANCE_PROPERTY_TEMPLATE": 268305617, "UNDECLARED_PROPERTIES": 390513439, "UNREACHABLE": 13328052}}}}, "Data": {"oneofs": {"_key": {"oneof": ["key"]}, "_value": {"oneof": ["value"]}}, "fields": {"key": {"type": "string", "id": 106079, "options": {"proto3_optional": true}}, "value": {"type": "string", "id": 111972721, "options": {"proto3_optional": true}}}}, "OperationsScopedList": {"oneofs": {"_warning": {"oneof": ["warning"]}}, "fields": {"operations": {"rule": "repeated", "type": "Operation", "id": 4184044}, "warning": {"type": "Warning", "id": 50704284, "options": {"proto3_optional": true}}}}, "OperationAggregatedList": {"oneofs": {"_id": {"oneof": ["id"]}, "_kind": {"oneof": ["kind"]}, "_nextPageToken": {"oneof": ["nextPageToken"]}, "_selfLink": {"oneof": ["selfLink"]}, "_warning": {"oneof": ["warning"]}}, "fields": {"id": {"type": "string", "id": 3355, "options": {"proto3_optional": true}}, "items": {"keyType": "string", "type": "OperationsScopedList", "id": 100526016}, "kind": {"type": "string", "id": 3292052, "options": {"proto3_optional": true}}, "nextPageToken": {"type": "string", "id": 79797525, "options": {"proto3_optional": true}}, "selfLink": {"type": "string", "id": 456214797, "options": {"proto3_optional": true}}, "unreachables": {"rule": "repeated", "type": "string", "id": 243372063}, "warning": {"type": "Warning", "id": 50704284, "options": {"proto3_optional": true}}}}, "GetRegionOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED", "(operation_response_field)": "id"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "region": {"type": "string", "id": 138946292, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteRegionOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "region": {"type": "string", "id": 138946292, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteRegionOperationResponse": {"fields": {}}, "ListRegionOperationsRequest": {"oneofs": {"_filter": {"oneof": ["filter"]}, "_maxResults": {"oneof": ["maxResults"]}, "_orderBy": {"oneof": ["orderBy"]}, "_pageToken": {"oneof": ["pageToken"]}, "_returnPartialSuccess": {"oneof": ["returnPartialSuccess"]}}, "fields": {"filter": {"type": "string", "id": 336120696, "options": {"proto3_optional": true}}, "maxResults": {"type": "uint32", "id": 54715419, "options": {"proto3_optional": true}}, "orderBy": {"type": "string", "id": 160562920, "options": {"proto3_optional": true}}, "pageToken": {"type": "string", "id": 19994697, "options": {"proto3_optional": true}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "region": {"type": "string", "id": 138946292, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "returnPartialSuccess": {"type": "bool", "id": 517198390, "options": {"proto3_optional": true}}}}, "OperationList": {"oneofs": {"_id": {"oneof": ["id"]}, "_kind": {"oneof": ["kind"]}, "_nextPageToken": {"oneof": ["nextPageToken"]}, "_selfLink": {"oneof": ["selfLink"]}, "_warning": {"oneof": ["warning"]}}, "fields": {"id": {"type": "string", "id": 3355, "options": {"proto3_optional": true}}, "items": {"rule": "repeated", "type": "Operation", "id": 100526016}, "kind": {"type": "string", "id": 3292052, "options": {"proto3_optional": true}}, "nextPageToken": {"type": "string", "id": 79797525, "options": {"proto3_optional": true}}, "selfLink": {"type": "string", "id": 456214797, "options": {"proto3_optional": true}}, "warning": {"type": "Warning", "id": 50704284, "options": {"proto3_optional": true}}}}, "WaitRegionOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "region": {"type": "string", "id": 138946292, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteZoneOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "zone": {"type": "string", "id": 3744684, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteZoneOperationResponse": {"fields": {}}, "GetZoneOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "zone": {"type": "string", "id": 3744684, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "ListZoneOperationsRequest": {"oneofs": {"_filter": {"oneof": ["filter"]}, "_maxResults": {"oneof": ["maxResults"]}, "_orderBy": {"oneof": ["orderBy"]}, "_pageToken": {"oneof": ["pageToken"]}, "_returnPartialSuccess": {"oneof": ["returnPartialSuccess"]}}, "fields": {"filter": {"type": "string", "id": 336120696, "options": {"proto3_optional": true}}, "maxResults": {"type": "uint32", "id": 54715419, "options": {"proto3_optional": true}}, "orderBy": {"type": "string", "id": 160562920, "options": {"proto3_optional": true}}, "pageToken": {"type": "string", "id": 19994697, "options": {"proto3_optional": true}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "returnPartialSuccess": {"type": "bool", "id": 517198390, "options": {"proto3_optional": true}}, "zone": {"type": "string", "id": 3744684, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "WaitZoneOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "zone": {"type": "string", "id": 3744684, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "AggregatedListGlobalOperationsRequest": {"oneofs": {"_filter": {"oneof": ["filter"]}, "_includeAllScopes": {"oneof": ["includeAllScopes"]}, "_maxResults": {"oneof": ["maxResults"]}, "_orderBy": {"oneof": ["orderBy"]}, "_pageToken": {"oneof": ["pageToken"]}, "_returnPartialSuccess": {"oneof": ["returnPartialSuccess"]}}, "fields": {"filter": {"type": "string", "id": 336120696, "options": {"proto3_optional": true}}, "includeAllScopes": {"type": "bool", "id": 391327988, "options": {"proto3_optional": true}}, "maxResults": {"type": "uint32", "id": 54715419, "options": {"proto3_optional": true}}, "orderBy": {"type": "string", "id": 160562920, "options": {"proto3_optional": true}}, "pageToken": {"type": "string", "id": 19994697, "options": {"proto3_optional": true}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "returnPartialSuccess": {"type": "bool", "id": 517198390, "options": {"proto3_optional": true}}}}, "DeleteGlobalOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteGlobalOperationResponse": {"fields": {}}, "GetGlobalOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "ListGlobalOperationsRequest": {"oneofs": {"_filter": {"oneof": ["filter"]}, "_maxResults": {"oneof": ["maxResults"]}, "_orderBy": {"oneof": ["orderBy"]}, "_pageToken": {"oneof": ["pageToken"]}, "_returnPartialSuccess": {"oneof": ["returnPartialSuccess"]}}, "fields": {"filter": {"type": "string", "id": 336120696, "options": {"proto3_optional": true}}, "maxResults": {"type": "uint32", "id": 54715419, "options": {"proto3_optional": true}}, "orderBy": {"type": "string", "id": 160562920, "options": {"proto3_optional": true}}, "pageToken": {"type": "string", "id": 19994697, "options": {"proto3_optional": true}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "returnPartialSuccess": {"type": "bool", "id": 517198390, "options": {"proto3_optional": true}}}}, "WaitGlobalOperationRequest": {"fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "project": {"type": "string", "id": 227560217, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "DeleteGlobalOrganizationOperationRequest": {"oneofs": {"_parentId": {"oneof": ["parentId"]}}, "fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "parentId": {"type": "string", "id": 459714768, "options": {"proto3_optional": true}}}}, "DeleteGlobalOrganizationOperationResponse": {"fields": {}}, "GetGlobalOrganizationOperationRequest": {"oneofs": {"_parentId": {"oneof": ["parentId"]}}, "fields": {"operation": {"type": "string", "id": 52090215, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "parentId": {"type": "string", "id": 459714768, "options": {"proto3_optional": true}}}}, "ListGlobalOrganizationOperationsRequest": {"oneofs": {"_filter": {"oneof": ["filter"]}, "_maxResults": {"oneof": ["maxResults"]}, "_orderBy": {"oneof": ["orderBy"]}, "_pageToken": {"oneof": ["pageToken"]}, "_parentId": {"oneof": ["parentId"]}, "_returnPartialSuccess": {"oneof": ["returnPartialSuccess"]}}, "fields": {"filter": {"type": "string", "id": 336120696, "options": {"proto3_optional": true}}, "maxResults": {"type": "uint32", "id": 54715419, "options": {"proto3_optional": true}}, "orderBy": {"type": "string", "id": 160562920, "options": {"proto3_optional": true}}, "pageToken": {"type": "string", "id": 19994697, "options": {"proto3_optional": true}}, "parentId": {"type": "string", "id": 459714768, "options": {"proto3_optional": true}}, "returnPartialSuccess": {"type": "bool", "id": 517198390, "options": {"proto3_optional": true}}}}, "RegionOperations": {"options": {"(google.api.default_host)": "compute.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/compute,https://www.googleapis.com/auth/cloud-platform"}, "methods": {"Delete": {"requestType": "DeleteRegionOperationRequest", "responseType": "DeleteRegionOperationResponse", "options": {"(google.api.http).delete": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}", "(google.api.method_signature)": "project,region,operation"}, "parsedOptions": [{"(google.api.http)": {"delete": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}"}}, {"(google.api.method_signature)": "project,region,operation"}]}, "Get": {"requestType": "GetRegionOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}", "(google.api.method_signature)": "project,region,operation"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}"}}, {"(google.api.method_signature)": "project,region,operation"}]}, "List": {"requestType": "ListRegionOperationsRequest", "responseType": "OperationList", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/regions/{region}/operations", "(google.api.method_signature)": "project,region"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/regions/{region}/operations"}}, {"(google.api.method_signature)": "project,region"}]}, "Wait": {"requestType": "WaitRegionOperationRequest", "responseType": "Operation", "options": {"(google.api.http).post": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}/wait", "(google.api.method_signature)": "project,region,operation"}, "parsedOptions": [{"(google.api.http)": {"post": "/compute/v1/projects/{project}/regions/{region}/operations/{operation}/wait"}}, {"(google.api.method_signature)": "project,region,operation"}]}}}, "ZoneOperations": {"options": {"(google.api.default_host)": "compute.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/compute,https://www.googleapis.com/auth/cloud-platform"}, "methods": {"Delete": {"requestType": "DeleteZoneOperationRequest", "responseType": "DeleteZoneOperationResponse", "options": {"(google.api.http).delete": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}", "(google.api.method_signature)": "project,zone,operation"}, "parsedOptions": [{"(google.api.http)": {"delete": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}"}}, {"(google.api.method_signature)": "project,zone,operation"}]}, "Get": {"requestType": "GetZoneOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}", "(google.api.method_signature)": "project,zone,operation"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}"}}, {"(google.api.method_signature)": "project,zone,operation"}]}, "List": {"requestType": "ListZoneOperationsRequest", "responseType": "OperationList", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/zones/{zone}/operations", "(google.api.method_signature)": "project,zone"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/zones/{zone}/operations"}}, {"(google.api.method_signature)": "project,zone"}]}, "Wait": {"requestType": "WaitZoneOperationRequest", "responseType": "Operation", "options": {"(google.api.http).post": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}/wait", "(google.api.method_signature)": "project,zone,operation"}, "parsedOptions": [{"(google.api.http)": {"post": "/compute/v1/projects/{project}/zones/{zone}/operations/{operation}/wait"}}, {"(google.api.method_signature)": "project,zone,operation"}]}}}, "GlobalOperations": {"options": {"(google.api.default_host)": "compute.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/compute,https://www.googleapis.com/auth/cloud-platform"}, "methods": {"AggregatedList": {"requestType": "AggregatedListGlobalOperationsRequest", "responseType": "OperationAggregatedList", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/aggregated/operations", "(google.api.method_signature)": "project"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/aggregated/operations"}}, {"(google.api.method_signature)": "project"}]}, "Delete": {"requestType": "DeleteGlobalOperationRequest", "responseType": "DeleteGlobalOperationResponse", "options": {"(google.api.http).delete": "/compute/v1/projects/{project}/global/operations/{operation}", "(google.api.method_signature)": "project,operation"}, "parsedOptions": [{"(google.api.http)": {"delete": "/compute/v1/projects/{project}/global/operations/{operation}"}}, {"(google.api.method_signature)": "project,operation"}]}, "Get": {"requestType": "GetGlobalOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/global/operations/{operation}", "(google.api.method_signature)": "project,operation"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/global/operations/{operation}"}}, {"(google.api.method_signature)": "project,operation"}]}, "List": {"requestType": "ListGlobalOperationsRequest", "responseType": "OperationList", "options": {"(google.api.http).get": "/compute/v1/projects/{project}/global/operations", "(google.api.method_signature)": "project"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/projects/{project}/global/operations"}}, {"(google.api.method_signature)": "project"}]}, "Wait": {"requestType": "WaitGlobalOperationRequest", "responseType": "Operation", "options": {"(google.api.http).post": "/compute/v1/projects/{project}/global/operations/{operation}/wait", "(google.api.method_signature)": "project,operation"}, "parsedOptions": [{"(google.api.http)": {"post": "/compute/v1/projects/{project}/global/operations/{operation}/wait"}}, {"(google.api.method_signature)": "project,operation"}]}}}, "GlobalOrganizationOperations": {"options": {"(google.api.default_host)": "compute.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/compute,https://www.googleapis.com/auth/cloud-platform"}, "methods": {"Delete": {"requestType": "DeleteGlobalOrganizationOperationRequest", "responseType": "DeleteGlobalOrganizationOperationResponse", "options": {"(google.api.http).delete": "/compute/v1/locations/global/operations/{operation}", "(google.api.method_signature)": "operation"}, "parsedOptions": [{"(google.api.http)": {"delete": "/compute/v1/locations/global/operations/{operation}"}}, {"(google.api.method_signature)": "operation"}]}, "Get": {"requestType": "GetGlobalOrganizationOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/compute/v1/locations/global/operations/{operation}", "(google.api.method_signature)": "operation"}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/locations/global/operations/{operation}"}}, {"(google.api.method_signature)": "operation"}]}, "List": {"requestType": "ListGlobalOrganizationOperationsRequest", "responseType": "OperationList", "options": {"(google.api.http).get": "/compute/v1/locations/global/operations", "(google.api.method_signature)": ""}, "parsedOptions": [{"(google.api.http)": {"get": "/compute/v1/locations/global/operations"}}, {"(google.api.method_signature)": ""}]}}}}}}}}}, "api": {"options": {"go_package": "google.golang.org/genproto/googleapis/api/annotations;annotations", "java_multiple_files": true, "java_outer_classname": "HttpProto", "java_package": "com.google.api", "objc_class_prefix": "GAPI", "cc_enable_arenas": true}, "nested": {"http": {"type": "HttpRule", "id": 72295728, "extend": "google.protobuf.MethodOptions"}, "Http": {"fields": {"rules": {"rule": "repeated", "type": "HttpRule", "id": 1}, "fullyDecodeReservedExpansion": {"type": "bool", "id": 2}}}, "HttpRule": {"oneofs": {"pattern": {"oneof": ["get", "put", "post", "delete", "patch", "custom"]}}, "fields": {"selector": {"type": "string", "id": 1}, "get": {"type": "string", "id": 2}, "put": {"type": "string", "id": 3}, "post": {"type": "string", "id": 4}, "delete": {"type": "string", "id": 5}, "patch": {"type": "string", "id": 6}, "custom": {"type": "CustomHttpPattern", "id": 8}, "body": {"type": "string", "id": 7}, "responseBody": {"type": "string", "id": 12}, "additionalBindings": {"rule": "repeated", "type": "HttpRule", "id": 11}}}, "CustomHttpPattern": {"fields": {"kind": {"type": "string", "id": 1}, "path": {"type": "string", "id": 2}}}}}, "protobuf": {"options": {"go_package": "google.golang.org/protobuf/types/descriptorpb", "java_package": "com.google.protobuf", "java_outer_classname": "DescriptorProtos", "csharp_namespace": "Google.Protobuf.Reflection", "objc_class_prefix": "GPB", "cc_enable_arenas": true, "optimize_for": "SPEED"}, "nested": {"FileDescriptorSet": {"fields": {"file": {"rule": "repeated", "type": "FileDescriptorProto", "id": 1}}}, "FileDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "package": {"type": "string", "id": 2}, "dependency": {"rule": "repeated", "type": "string", "id": 3}, "publicDependency": {"rule": "repeated", "type": "int32", "id": 10, "options": {"packed": false}}, "weakDependency": {"rule": "repeated", "type": "int32", "id": 11, "options": {"packed": false}}, "messageType": {"rule": "repeated", "type": "DescriptorProto", "id": 4}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 5}, "service": {"rule": "repeated", "type": "ServiceDescriptorProto", "id": 6}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 7}, "options": {"type": "FileOptions", "id": 8}, "sourceCodeInfo": {"type": "SourceCodeInfo", "id": 9}, "syntax": {"type": "string", "id": 12}}}, "DescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "field": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 2}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 6}, "nestedType": {"rule": "repeated", "type": "DescriptorProto", "id": 3}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 4}, "extensionRange": {"rule": "repeated", "type": "ExtensionRange", "id": 5}, "oneofDecl": {"rule": "repeated", "type": "OneofDescriptorProto", "id": 8}, "options": {"type": "MessageOptions", "id": 7}, "reservedRange": {"rule": "repeated", "type": "ReservedRange", "id": 9}, "reservedName": {"rule": "repeated", "type": "string", "id": 10}}, "nested": {"ExtensionRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}, "options": {"type": "ExtensionRangeOptions", "id": 3}}}, "ReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "ExtensionRangeOptions": {"fields": {"uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "FieldDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 3}, "label": {"type": "Label", "id": 4}, "type": {"type": "Type", "id": 5}, "typeName": {"type": "string", "id": 6}, "extendee": {"type": "string", "id": 2}, "defaultValue": {"type": "string", "id": 7}, "oneofIndex": {"type": "int32", "id": 9}, "jsonName": {"type": "string", "id": 10}, "options": {"type": "FieldOptions", "id": 8}, "proto3Optional": {"type": "bool", "id": 17}}, "nested": {"Type": {"values": {"TYPE_DOUBLE": 1, "TYPE_FLOAT": 2, "TYPE_INT64": 3, "TYPE_UINT64": 4, "TYPE_INT32": 5, "TYPE_FIXED64": 6, "TYPE_FIXED32": 7, "TYPE_BOOL": 8, "TYPE_STRING": 9, "TYPE_GROUP": 10, "TYPE_MESSAGE": 11, "TYPE_BYTES": 12, "TYPE_UINT32": 13, "TYPE_ENUM": 14, "TYPE_SFIXED32": 15, "TYPE_SFIXED64": 16, "TYPE_SINT32": 17, "TYPE_SINT64": 18}}, "Label": {"values": {"LABEL_OPTIONAL": 1, "LABEL_REQUIRED": 2, "LABEL_REPEATED": 3}}}}, "OneofDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "options": {"type": "OneofOptions", "id": 2}}}, "EnumDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "value": {"rule": "repeated", "type": "EnumValueDescriptorProto", "id": 2}, "options": {"type": "EnumOptions", "id": 3}, "reservedRange": {"rule": "repeated", "type": "EnumReservedRange", "id": 4}, "reservedName": {"rule": "repeated", "type": "string", "id": 5}}, "nested": {"EnumReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "EnumValueDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 2}, "options": {"type": "EnumValueOptions", "id": 3}}}, "ServiceDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "method": {"rule": "repeated", "type": "MethodDescriptorProto", "id": 2}, "options": {"type": "ServiceOptions", "id": 3}}}, "MethodDescriptorProto": {"fields": {"name": {"type": "string", "id": 1}, "inputType": {"type": "string", "id": 2}, "outputType": {"type": "string", "id": 3}, "options": {"type": "MethodOptions", "id": 4}, "clientStreaming": {"type": "bool", "id": 5, "options": {"default": false}}, "serverStreaming": {"type": "bool", "id": 6, "options": {"default": false}}}}, "FileOptions": {"fields": {"javaPackage": {"type": "string", "id": 1}, "javaOuterClassname": {"type": "string", "id": 8}, "javaMultipleFiles": {"type": "bool", "id": 10, "options": {"default": false}}, "javaGenerateEqualsAndHash": {"type": "bool", "id": 20, "options": {"deprecated": true}}, "javaStringCheckUtf8": {"type": "bool", "id": 27, "options": {"default": false}}, "optimizeFor": {"type": "OptimizeMode", "id": 9, "options": {"default": "SPEED"}}, "goPackage": {"type": "string", "id": 11}, "ccGenericServices": {"type": "bool", "id": 16, "options": {"default": false}}, "javaGenericServices": {"type": "bool", "id": 17, "options": {"default": false}}, "pyGenericServices": {"type": "bool", "id": 18, "options": {"default": false}}, "phpGenericServices": {"type": "bool", "id": 42, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 23, "options": {"default": false}}, "ccEnableArenas": {"type": "bool", "id": 31, "options": {"default": true}}, "objcClassPrefix": {"type": "string", "id": 36}, "csharpNamespace": {"type": "string", "id": 37}, "swiftPrefix": {"type": "string", "id": 39}, "phpClassPrefix": {"type": "string", "id": 40}, "phpNamespace": {"type": "string", "id": 41}, "phpMetadataNamespace": {"type": "string", "id": 44}, "rubyPackage": {"type": "string", "id": 45}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[38, 38]], "nested": {"OptimizeMode": {"values": {"SPEED": 1, "CODE_SIZE": 2, "LITE_RUNTIME": 3}}}}, "MessageOptions": {"fields": {"messageSetWireFormat": {"type": "bool", "id": 1, "options": {"default": false}}, "noStandardDescriptorAccessor": {"type": "bool", "id": 2, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "mapEntry": {"type": "bool", "id": 7}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[8, 8], [9, 9]]}, "FieldOptions": {"fields": {"ctype": {"type": "CType", "id": 1, "options": {"default": "STRING"}}, "packed": {"type": "bool", "id": 2}, "jstype": {"type": "JSType", "id": 6, "options": {"default": "JS_NORMAL"}}, "lazy": {"type": "bool", "id": 5, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "weak": {"type": "bool", "id": 10, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[4, 4]], "nested": {"CType": {"values": {"STRING": 0, "CORD": 1, "STRING_PIECE": 2}}, "JSType": {"values": {"JS_NORMAL": 0, "JS_STRING": 1, "JS_NUMBER": 2}}}}, "OneofOptions": {"fields": {"uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "EnumOptions": {"fields": {"allowAlias": {"type": "bool", "id": 2}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "reserved": [[5, 5]]}, "EnumValueOptions": {"fields": {"deprecated": {"type": "bool", "id": 1, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "ServiceOptions": {"fields": {"deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]]}, "MethodOptions": {"fields": {"deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "idempotencyLevel": {"type": "IdempotencyLevel", "id": 34, "options": {"default": "IDEMPOTENCY_UNKNOWN"}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000.0, 536870911]], "nested": {"IdempotencyLevel": {"values": {"IDEMPOTENCY_UNKNOWN": 0, "NO_SIDE_EFFECTS": 1, "IDEMPOTENT": 2}}}}, "UninterpretedOption": {"fields": {"name": {"rule": "repeated", "type": "NamePart", "id": 2}, "identifierValue": {"type": "string", "id": 3}, "positiveIntValue": {"type": "uint64", "id": 4}, "negativeIntValue": {"type": "int64", "id": 5}, "doubleValue": {"type": "double", "id": 6}, "stringValue": {"type": "bytes", "id": 7}, "aggregateValue": {"type": "string", "id": 8}}, "nested": {"NamePart": {"fields": {"namePart": {"rule": "required", "type": "string", "id": 1}, "isExtension": {"rule": "required", "type": "bool", "id": 2}}}}}, "SourceCodeInfo": {"fields": {"location": {"rule": "repeated", "type": "Location", "id": 1}}, "nested": {"Location": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1}, "span": {"rule": "repeated", "type": "int32", "id": 2}, "leadingComments": {"type": "string", "id": 3}, "trailingComments": {"type": "string", "id": 4}, "leadingDetachedComments": {"rule": "repeated", "type": "string", "id": 6}}}}}, "GeneratedCodeInfo": {"fields": {"annotation": {"rule": "repeated", "type": "Annotation", "id": 1}}, "nested": {"Annotation": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1}, "sourceFile": {"type": "string", "id": 2}, "begin": {"type": "int32", "id": 3}, "end": {"type": "int32", "id": 4}}}}}}}}}}}