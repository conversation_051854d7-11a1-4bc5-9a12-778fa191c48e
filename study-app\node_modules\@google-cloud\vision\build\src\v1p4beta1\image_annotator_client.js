"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageAnnotatorClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1p4beta1/image_annotator_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./image_annotator_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Service that performs Google Cloud Vision API detection tasks over client
 *  images, such as face, landmark, logo, label, and text detection. The
 *  ImageAnnotator service returns detected entities from the images.
 * @class
 * @memberof v1p4beta1
 */
class ImageAnnotatorClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('vision');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    operationsClient;
    imageAnnotatorStub;
    /**
     * Construct an instance of ImageAnnotatorClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new ImageAnnotatorClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'vision.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            productPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/products/{product}'),
            productSetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/productSets/{product_set}'),
            referenceImagePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}'),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const asyncBatchAnnotateImagesResponse = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.AsyncBatchAnnotateImagesResponse');
        const asyncBatchAnnotateImagesMetadata = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.OperationMetadata');
        const asyncBatchAnnotateFilesResponse = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.AsyncBatchAnnotateFilesResponse');
        const asyncBatchAnnotateFilesMetadata = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.OperationMetadata');
        this.descriptors.longrunning = {
            asyncBatchAnnotateImages: new this._gaxModule.LongrunningDescriptor(this.operationsClient, asyncBatchAnnotateImagesResponse.decode.bind(asyncBatchAnnotateImagesResponse), asyncBatchAnnotateImagesMetadata.decode.bind(asyncBatchAnnotateImagesMetadata)),
            asyncBatchAnnotateFiles: new this._gaxModule.LongrunningDescriptor(this.operationsClient, asyncBatchAnnotateFilesResponse.decode.bind(asyncBatchAnnotateFilesResponse), asyncBatchAnnotateFilesMetadata.decode.bind(asyncBatchAnnotateFilesMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.vision.v1p4beta1.ImageAnnotator', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.imageAnnotatorStub) {
            return this.imageAnnotatorStub;
        }
        // Put together the "service stub" for
        // google.cloud.vision.v1p4beta1.ImageAnnotator.
        this.imageAnnotatorStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.vision.v1p4beta1.ImageAnnotator')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.vision.v1p4beta1.ImageAnnotator, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const imageAnnotatorStubMethods = [
            'batchAnnotateImages',
            'batchAnnotateFiles',
            'asyncBatchAnnotateImages',
            'asyncBatchAnnotateFiles',
        ];
        for (const methodName of imageAnnotatorStubMethods) {
            const callPromise = this.imageAnnotatorStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.longrunning[methodName] || undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.imageAnnotatorStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'vision.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'vision.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/cloud-vision',
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    batchAnnotateImages(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('batchAnnotateImages request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('batchAnnotateImages response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .batchAnnotateImages(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('batchAnnotateImages response %j', response);
            return [response, options, rawResponse];
        });
    }
    batchAnnotateFiles(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('batchAnnotateFiles request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('batchAnnotateFiles response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .batchAnnotateFiles(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('batchAnnotateFiles response %j', response);
            return [response, options, rawResponse];
        });
    }
    asyncBatchAnnotateImages(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('asyncBatchAnnotateImages response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('asyncBatchAnnotateImages request %j', request);
        return this.innerApiCalls
            .asyncBatchAnnotateImages(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('asyncBatchAnnotateImages response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `asyncBatchAnnotateImages()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/image_annotator.async_batch_annotate_images.js</caption>
     * region_tag:vision_v1p4beta1_generated_ImageAnnotator_AsyncBatchAnnotateImages_async
     */
    async checkAsyncBatchAnnotateImagesProgress(name) {
        this._log.info('asyncBatchAnnotateImages long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.asyncBatchAnnotateImages, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    asyncBatchAnnotateFiles(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('asyncBatchAnnotateFiles response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('asyncBatchAnnotateFiles request %j', request);
        return this.innerApiCalls
            .asyncBatchAnnotateFiles(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('asyncBatchAnnotateFiles response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `asyncBatchAnnotateFiles()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/image_annotator.async_batch_annotate_files.js</caption>
     * region_tag:vision_v1p4beta1_generated_ImageAnnotator_AsyncBatchAnnotateFiles_async
     */
    async checkAsyncBatchAnnotateFilesProgress(name) {
        this._log.info('asyncBatchAnnotateFiles long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.asyncBatchAnnotateFiles, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified product resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @returns {string} Resource name string.
     */
    productPath(project, location, product) {
        return this.pathTemplates.productPathTemplate.render({
            project: project,
            location: location,
            product: product,
        });
    }
    /**
     * Parse the project from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).project;
    }
    /**
     * Parse the location from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).location;
    }
    /**
     * Parse the product from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).product;
    }
    /**
     * Return a fully-qualified productSet resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product_set
     * @returns {string} Resource name string.
     */
    productSetPath(project, location, productSet) {
        return this.pathTemplates.productSetPathTemplate.render({
            project: project,
            location: location,
            product_set: productSet,
        });
    }
    /**
     * Parse the project from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .project;
    }
    /**
     * Parse the location from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .location;
    }
    /**
     * Parse the product_set from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the product_set.
     */
    matchProductSetFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .product_set;
    }
    /**
     * Return a fully-qualified referenceImage resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @param {string} reference_image
     * @returns {string} Resource name string.
     */
    referenceImagePath(project, location, product, referenceImage) {
        return this.pathTemplates.referenceImagePathTemplate.render({
            project: project,
            location: location,
            product: product,
            reference_image: referenceImage,
        });
    }
    /**
     * Parse the project from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).project;
    }
    /**
     * Parse the location from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).location;
    }
    /**
     * Parse the product from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).product;
    }
    /**
     * Parse the reference_image from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the reference_image.
     */
    matchReferenceImageFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).reference_image;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.imageAnnotatorStub && !this._terminated) {
            return this.imageAnnotatorStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
                void this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.ImageAnnotatorClient = ImageAnnotatorClient;
//# sourceMappingURL=image_annotator_client.js.map