module.exports = {

"[project]/.next-internal/server/app/api/admin/books/[id]/clear-ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/better-sqlite3 [external] (better-sqlite3, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("better-sqlite3", () => require("better-sqlite3"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearOCRData": (()=>clearOCRData),
    "createDefaultAdmin": (()=>createDefaultAdmin),
    "default": (()=>__TURBOPACK__default__export__),
    "initializeDatabase": (()=>initializeDatabase),
    "migrateDatabase": (()=>migrateDatabase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/better-sqlite3 [external] (better-sqlite3, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
;
;
;
// Database file path
const dbPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data', 'study_app.db');
// Ensure data directory exists
const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(dbPath);
if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(dataDir)) {
    __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(dataDir, {
        recursive: true
    });
}
// Initialize database
const db = new __TURBOPACK__imported__module__$5b$externals$5d2f$better$2d$sqlite3__$5b$external$5d$__$28$better$2d$sqlite3$2c$__cjs$29$__["default"](dbPath);
// Enable foreign keys
db.pragma('foreign_keys = ON');
function migrateDatabase() {
    try {
        // Check if new columns exist, if not add them
        const tableInfo = db.prepare("PRAGMA table_info(images)").all();
        const hasPageType = tableInfo.some((col)=>col.name === 'page_type');
        const hasUploadOrder = tableInfo.some((col)=>col.name === 'upload_order');
        if (!hasPageType) {
            db.exec("ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'");
            console.log('Added page_type column to images table');
        }
        if (!hasUploadOrder) {
            db.exec("ALTER TABLE images ADD COLUMN upload_order INTEGER");
            // Set upload order for existing images based on uploaded_at
            db.exec(`
        UPDATE images
        SET upload_order = (
          SELECT COUNT(*) + 1
          FROM images i2
          WHERE i2.class_id = images.class_id
          AND i2.subject_id = images.subject_id
          AND i2.uploaded_at < images.uploaded_at
        )
      `);
            console.log('Added upload_order column to images table');
        }
        // Check if processed column exists in ocr_text table
        const ocrTableInfo = db.prepare("PRAGMA table_info(ocr_text)").all();
        const hasProcessed = ocrTableInfo.some((col)=>col.name === 'processed');
        if (!hasProcessed) {
            db.exec("ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1");
            console.log('Added processed column to ocr_text table');
        }
        // Check if books table exists
        const tablesResult = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
        if (!tablesResult) {
            console.log('Creating books table...');
            db.exec(`
        CREATE TABLE books (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          class_id INTEGER NOT NULL,
          subject_id INTEGER NOT NULL,
          description TEXT,
          cover_image_path TEXT,
          total_pages INTEGER DEFAULT 0,
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
        )
      `);
            console.log('Books table created successfully');
        }
        // Check if images table has book_id column
        const hasBookId = tableInfo.some((col)=>col.name === 'book_id');
        if (!hasBookId) {
            console.log('Adding book_id column to images table...');
            db.exec("ALTER TABLE images ADD COLUMN book_id INTEGER");
            console.log('Added book_id column to images table');
        }
        // Check if images table has page_number column
        const hasPageNumber = tableInfo.some((col)=>col.name === 'page_number');
        if (!hasPageNumber) {
            console.log('Adding page_number column to images table...');
            db.exec("ALTER TABLE images ADD COLUMN page_number INTEGER");
            console.log('Added page_number column to images table');
        }
    } catch (error) {
        console.error('Migration error:', error);
    }
}
function initializeDatabase() {
    // Users table
    db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),
      password_hash TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Classes table
    db.exec(`
    CREATE TABLE IF NOT EXISTS classes (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
    // Subjects table
    db.exec(`
    CREATE TABLE IF NOT EXISTS subjects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      UNIQUE(class_id, name)
    )
  `);
    // Books table - containers for textbook content
    db.exec(`
    CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      description TEXT,
      cover_image_path TEXT,
      total_pages INTEGER DEFAULT 0,
      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);
    // Images table
    db.exec(`
    CREATE TABLE IF NOT EXISTS images (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      file_path TEXT NOT NULL,
      original_name TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      book_id INTEGER,
      page_number INTEGER,
      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),
      upload_order INTEGER,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
    )
  `);
    // OCR text table
    db.exec(`
    CREATE TABLE IF NOT EXISTS ocr_text (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      image_id INTEGER NOT NULL,
      content TEXT NOT NULL,
      processed BOOLEAN DEFAULT FALSE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE
    )
  `);
    // Questions table
    db.exec(`
    CREATE TABLE IF NOT EXISTS questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapter TEXT,
      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),
      content TEXT NOT NULL,
      options TEXT, -- JSON string for MCQ options
      correct_answer TEXT,
      marks INTEGER DEFAULT 1,
      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )
  `);
    // Tests table
    db.exec(`
    CREATE TABLE IF NOT EXISTS tests (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      class_id INTEGER NOT NULL,
      subject_id INTEGER NOT NULL,
      chapters TEXT, -- JSON string of selected chapters
      time_min INTEGER NOT NULL, -- Time limit in minutes
      total_marks INTEGER DEFAULT 0,
      instructions TEXT,
      is_active BOOLEAN DEFAULT TRUE,
      created_by INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `);
    // Test questions junction table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_questions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      question_order INTEGER NOT NULL,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      UNIQUE(test_id, question_id),
      UNIQUE(test_id, question_order)
    )
  `);
    // Test results table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_results (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      test_id INTEGER NOT NULL,
      user_id INTEGER NOT NULL,
      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      submitted_at DATETIME,
      total_score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      time_taken INTEGER, -- Time taken in minutes
      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,
      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(test_id, user_id)
    )
  `);
    // Test answers table
    db.exec(`
    CREATE TABLE IF NOT EXISTS test_answers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      result_id INTEGER NOT NULL,
      question_id INTEGER NOT NULL,
      user_answer TEXT,
      is_correct BOOLEAN,
      score REAL DEFAULT 0,
      max_score REAL DEFAULT 0,
      graded_by INTEGER,
      graded_at DATETIME,
      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,
      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
      FOREIGN KEY (graded_by) REFERENCES users(id),
      UNIQUE(result_id, question_id)
    )
  `);
    // Create indexes for better performance
    db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);
    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);
    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);
    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);
  `);
    console.log('Database initialized successfully');
    // Run migrations after initialization
    migrateDatabase();
}
function createDefaultAdmin() {
    const bcrypt = __turbopack_context__.r("[project]/node_modules/bcryptjs/umd/index.js [app-route] (ecmascript)");
    const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');
    if (adminExists.count === 0) {
        const hashedPassword = bcrypt.hashSync('admin123', 10);
        db.prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);
        console.log('Default admin user created: <EMAIL> / admin123');
    }
}
function clearOCRData() {
    try {
        console.log('Clearing existing OCR data to enable fresh processing...');
        db.exec('DELETE FROM ocr_text');
        db.exec('DELETE FROM questions');
        console.log('OCR data cleared successfully');
    } catch (error) {
        console.error('Error clearing OCR data:', error);
    }
}
// Initialize database on import
initializeDatabase();
createDefaultAdmin();
const __TURBOPACK__default__export__ = db;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clearAuthCookie": (()=>clearAuthCookie),
    "createToken": (()=>createToken),
    "deleteUser": (()=>deleteUser),
    "getAllUsers": (()=>getAllUsers),
    "getCurrentUser": (()=>getCurrentUser),
    "hashPassword": (()=>hashPassword),
    "loginUser": (()=>loginUser),
    "registerUser": (()=>registerUser),
    "requireAdmin": (()=>requireAdmin),
    "requireAuth": (()=>requireAuth),
    "setAuthCookie": (()=>setAuthCookie),
    "updateUser": (()=>updateUser),
    "verifyPassword": (()=>verifyPassword),
    "verifyToken": (()=>verifyToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/sign.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jose/dist/webapi/jwt/verify.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key-change-this-in-production');
function hashPassword(password) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hashSync(password, 10);
}
function verifyPassword(password, hash) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compareSync(password, hash);
}
async function createToken(user) {
    return await new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$sign$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SignJWT"]({
        userId: user.id,
        email: user.email,
        role: user.role
    }).setProtectedHeader({
        alg: 'HS256'
    }).setIssuedAt().setExpirationTime('24h').sign(JWT_SECRET);
}
async function verifyToken(token) {
    try {
        const { payload } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jose$2f$dist$2f$webapi$2f$jwt$2f$verify$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["jwtVerify"])(token, JWT_SECRET);
        return payload;
    } catch (error) {
        return null;
    }
}
async function loginUser(email, password) {
    try {
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT * FROM users WHERE email = ?').get(email);
        if (!user) {
            return {
                success: false,
                error: 'Invalid email or password'
            };
        }
        if (!verifyPassword(password, user.password_hash)) {
            return {
                success: false,
                error: 'Invalid email or password'
            };
        }
        const userWithoutPassword = {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            created_at: user.created_at
        };
        return {
            success: true,
            user: userWithoutPassword
        };
    } catch (error) {
        console.error('Login error:', error);
        return {
            success: false,
            error: 'Login failed'
        };
    }
}
async function getCurrentUser() {
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
        const token = cookieStore.get('auth-token')?.value;
        if (!token) {
            return null;
        }
        const payload = await verifyToken(token);
        if (!payload) {
            return null;
        }
        const user = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?').get(payload.userId);
        return user || null;
    } catch (error) {
        console.error('Get current user error:', error);
        return null;
    }
}
async function setAuthCookie(user) {
    const token = await createToken(user);
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.set('auth-token', token, {
        httpOnly: true,
        secure: ("TURBOPACK compile-time value", "development") === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 // 24 hours
    });
}
async function clearAuthCookie() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    cookieStore.delete('auth-token');
}
function registerUser(name, email, password, role) {
    try {
        // Check if user already exists
        const existingUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id FROM users WHERE email = ?').get(email);
        if (existingUser) {
            return {
                success: false,
                error: 'User with this email already exists'
            };
        }
        const hashedPassword = hashPassword(password);
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO users (name, email, role, password_hash)
      VALUES (?, ?, ?, ?)
    `).run(name, email, role, hashedPassword);
        const newUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?').get(result.lastInsertRowid);
        return {
            success: true,
            user: newUser
        };
    } catch (error) {
        console.error('Registration error:', error);
        return {
            success: false,
            error: 'Registration failed'
        };
    }
}
async function requireAuth() {
    const user = await getCurrentUser();
    return user;
}
async function requireAdmin() {
    const user = await getCurrentUser();
    if (!user || user.role !== 'admin') {
        return null;
    }
    return user;
}
function getAllUsers() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all();
}
function updateUser(id, updates) {
    try {
        const setClause = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');
        const values = Object.values(updates);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`).run(...values, id);
        return true;
    } catch (error) {
        console.error('Update user error:', error);
        return false;
    }
}
function deleteUser(id) {
    try {
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM users WHERE id = ?').run(id);
        return true;
    } catch (error) {
        console.error('Delete user error:', error);
        return false;
    }
}
}}),
"[project]/src/lib/models.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BookModel": (()=>BookModel),
    "ClassModel": (()=>ClassModel),
    "QuestionModel": (()=>QuestionModel),
    "SubjectModel": (()=>SubjectModel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
const ClassModel = {
    getAll () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT * FROM classes ORDER BY name').all();
    },
    getById (id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT * FROM classes WHERE id = ?').get(id) || null;
    },
    create (name, description) {
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO classes (name, description)
      VALUES (?, ?)
    `).run(name, description || null);
        return this.getById(result.lastInsertRowid);
    },
    update (id, name, description) {
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        UPDATE classes 
        SET name = ?, description = ?
        WHERE id = ?
      `).run(name, description || null, id);
            return true;
        } catch (error) {
            console.error('Update class error:', error);
            return false;
        }
    },
    delete (id) {
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM classes WHERE id = ?').run(id);
            return true;
        } catch (error) {
            console.error('Delete class error:', error);
            return false;
        }
    }
};
const SubjectModel = {
    getAll () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      ORDER BY c.name, s.name
    `).all();
    },
    getByClassId (classId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      WHERE s.class_id = ?
      ORDER BY s.name
    `).all(classId);
    },
    getById (id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT s.*, c.name as class_name
      FROM subjects s
      JOIN classes c ON s.class_id = c.id
      WHERE s.id = ?
    `).get(id) || null;
    },
    create (classId, name, description) {
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO subjects (class_id, name, description)
      VALUES (?, ?, ?)
    `).run(classId, name, description || null);
        return this.getById(result.lastInsertRowid);
    },
    update (id, name, description) {
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        UPDATE subjects 
        SET name = ?, description = ?
        WHERE id = ?
      `).run(name, description || null, id);
            return true;
        } catch (error) {
            console.error('Update subject error:', error);
            return false;
        }
    },
    delete (id) {
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM subjects WHERE id = ?').run(id);
            return true;
        } catch (error) {
            console.error('Delete subject error:', error);
            return false;
        }
    }
};
const QuestionModel = {
    getAll (filters) {
        let query = `
      SELECT q.*, c.name as class_name, s.name as subject_name
      FROM questions q
      JOIN classes c ON q.class_id = c.id
      JOIN subjects s ON q.subject_id = s.id
    `;
        const conditions = [];
        const params = [];
        if (filters?.classId) {
            conditions.push('q.class_id = ?');
            params.push(filters.classId);
        }
        if (filters?.subjectId) {
            conditions.push('q.subject_id = ?');
            params.push(filters.subjectId);
        }
        if (filters?.type) {
            conditions.push('q.type = ?');
            params.push(filters.type);
        }
        if (filters?.chapter) {
            conditions.push('q.chapter = ?');
            params.push(filters.chapter);
        }
        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }
        query += ' ORDER BY q.created_at DESC';
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(query).all(...params);
    },
    getById (id) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT q.*, c.name as class_name, s.name as subject_name
      FROM questions q
      JOIN classes c ON q.class_id = c.id
      JOIN subjects s ON q.subject_id = s.id
      WHERE q.id = ?
    `).get(id) || null;
    },
    create (questionData) {
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO questions (
        class_id, subject_id, chapter, type, content, options, 
        correct_answer, marks, difficulty
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(questionData.class_id, questionData.subject_id, questionData.chapter || null, questionData.type, questionData.content, questionData.options || null, questionData.correct_answer || null, questionData.marks, questionData.difficulty || null);
        return this.getById(result.lastInsertRowid);
    },
    update (id, questionData) {
        try {
            const fields = Object.keys(questionData).filter((key)=>questionData[key] !== undefined);
            const setClause = fields.map((field)=>`${field} = ?`).join(', ');
            const values = fields.map((field)=>questionData[field]);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        UPDATE questions 
        SET ${setClause}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).run(...values, id);
            return true;
        } catch (error) {
            console.error('Update question error:', error);
            return false;
        }
    },
    delete (id) {
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM questions WHERE id = ?').run(id);
            return true;
        } catch (error) {
            console.error('Delete question error:', error);
            return false;
        }
    },
    getChapters (classId, subjectId) {
        let query = 'SELECT DISTINCT chapter FROM questions WHERE chapter IS NOT NULL';
        const params = [];
        if (classId) {
            query += ' AND class_id = ?';
            params.push(classId);
        }
        if (subjectId) {
            query += ' AND subject_id = ?';
            params.push(subjectId);
        }
        query += ' ORDER BY chapter';
        const results = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(query).all(...params);
        return results.map((r)=>r.chapter);
    },
    // Get available chapters from page types
    getChaptersFromPageTypes (classId, subjectId) {
        let query = `
      SELECT DISTINCT page_type
      FROM images
      WHERE page_type LIKE 'chapter-%'
    `;
        const params = [];
        if (classId) {
            query += ' AND class_id = ?';
            params.push(classId);
        }
        if (subjectId) {
            query += ' AND subject_id = ?';
            params.push(subjectId);
        }
        query += ' ORDER BY page_type';
        const results = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(query).all(...params);
        return results.map((r)=>r.page_type.replace('chapter-', 'Chapter '));
    }
};
const BookModel = {
    getAll () {
        try {
            // Check if books table exists
            const tableExists = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
            if (!tableExists) {
                console.log('Books table does not exist yet, returning empty array');
                return [];
            }
            const books = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        SELECT
          b.*,
          c.name as class_name,
          s.name as subject_name,
          COUNT(DISTINCT i.id) as uploaded_pages,
          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
          0 as total_questions
        FROM books b
        LEFT JOIN classes c ON b.class_id = c.id
        LEFT JOIN subjects s ON b.subject_id = s.id
        LEFT JOIN images i ON b.id = i.book_id
        LEFT JOIN ocr_text ot ON i.id = ot.image_id
        GROUP BY b.id
        ORDER BY b.updated_at DESC
      `).all();
            return books;
        } catch (error) {
            console.error('Error fetching books:', error);
            return [];
        }
    },
    getById (id) {
        try {
            // Check if books table exists
            const tableExists = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='books'").get();
            if (!tableExists) {
                return null;
            }
            const book = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        SELECT
          b.*,
          c.name as class_name,
          s.name as subject_name,
          COUNT(DISTINCT i.id) as uploaded_pages,
          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,
          0 as total_questions
        FROM books b
        LEFT JOIN classes c ON b.class_id = c.id
        LEFT JOIN subjects s ON b.subject_id = s.id
        LEFT JOIN images i ON b.id = i.book_id
        LEFT JOIN ocr_text ot ON i.id = ot.image_id
        WHERE b.id = ?
        GROUP BY b.id
      `).get(id);
            return book || null;
        } catch (error) {
            console.error('Error fetching book by id:', error);
            return null;
        }
    },
    create (data) {
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO books (title, class_id, subject_id, description)
      VALUES (?, ?, ?, ?)
    `).run(data.title, data.class_id, data.subject_id, data.description || null);
        return result.lastInsertRowid;
    },
    update (id, data) {
        const fields = [];
        const values = [];
        if (data.title !== undefined) {
            fields.push('title = ?');
            values.push(data.title);
        }
        if (data.description !== undefined) {
            fields.push('description = ?');
            values.push(data.description);
        }
        if (data.status !== undefined) {
            fields.push('status = ?');
            values.push(data.status);
        }
        if (fields.length === 0) return false;
        fields.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      UPDATE books SET ${fields.join(', ')} WHERE id = ?
    `).run(...values);
        return result.changes > 0;
    },
    delete (id) {
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM books WHERE id = ?').run(id);
        return result.changes > 0;
    },
    getImages (bookId) {
        try {
            // Check if book_id column exists in images table
            const tableInfo = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare("PRAGMA table_info(images)").all();
            const hasBookId = tableInfo.some((col)=>col.name === 'book_id');
            if (!hasBookId) {
                // If book_id column doesn't exist yet, return empty array
                return [];
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        SELECT
          i.*,
          CASE WHEN ot.id IS NOT NULL THEN 1 ELSE 0 END as has_ocr,
          CASE WHEN ot.processed = 1 THEN 1 ELSE 0 END as ocr_processed
        FROM images i
        LEFT JOIN ocr_text ot ON i.id = ot.image_id
        WHERE i.book_id = ?
        ORDER BY i.upload_order ASC, i.id ASC
      `).all(bookId);
        } catch (error) {
            console.error('Error fetching book images:', error);
            return [];
        }
    }
};
}}),
"[externals]/worker_threads [external] (worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("worker_threads", () => require("worker_threads"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/src/lib/upload.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "autoAssignPageTypes": (()=>autoAssignPageTypes),
    "deleteImage": (()=>deleteImage),
    "ensureUploadDir": (()=>ensureUploadDir),
    "getFileSize": (()=>getFileSize),
    "getImages": (()=>getImages),
    "getOCRText": (()=>getOCRText),
    "parseQuestionsFromOCR": (()=>parseQuestionsFromOCR),
    "processOCR": (()=>processOCR),
    "saveUploadedFile": (()=>saveUploadedFile),
    "updateImagePageType": (()=>updateImagePageType)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tesseract$2e$js$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tesseract.js/src/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
// Generate realistic mock OCR content for testing
function generateMockOCRContent(filename, imageId) {
    const pageNumber = filename.match(/(\d+)/)?.[1] || imageId.toString();
    const educationalContent = [
        // Math content
        `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Mathematical Concepts

Problem ${pageNumber}: Solve for x in the equation 2x + 5 = 15

Solution:
2x + 5 = 15
2x = 15 - 5
2x = 10
x = 5

Therefore, x = 5.

Practice Problems:
1. If 3x - 7 = 14, find the value of x.
2. Solve: 4(x + 2) = 20
3. What is the value of y if 2y + 3 = 11?

Key Concepts:
- Linear equations
- Algebraic manipulation
- Variable isolation`,
        // Science content
        `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Scientific Principles

Lesson ${pageNumber}: The Water Cycle

The water cycle is a continuous process that describes how water moves through Earth's systems.

Main Stages:
1. Evaporation - Water from oceans, lakes, and rivers turns into water vapor
2. Condensation - Water vapor cools and forms clouds
3. Precipitation - Water falls as rain, snow, or hail
4. Collection - Water gathers in bodies of water

Important Facts:
• 97% of Earth's water is in the oceans
• Only 3% is fresh water
• The sun provides energy for the water cycle

Questions for Review:
1. What causes water to evaporate?
2. How do clouds form?
3. Name three types of precipitation.`,
        // History content
        `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Historical Events

Section ${pageNumber}: Ancient Civilizations

The ancient civilizations laid the foundation for modern society through their innovations and cultural contributions.

Key Civilizations:
• Mesopotamia (3500-539 BCE)
  - Invented writing (cuneiform)
  - Developed the wheel
  - Created the first cities

• Ancient Egypt (3100-30 BCE)
  - Built pyramids and monuments
  - Developed hieroglyphic writing
  - Advanced medicine and mathematics

• Ancient Greece (800-146 BCE)
  - Democracy and philosophy
  - Olympic Games
  - Scientific method

Timeline:
3500 BCE - First cities in Mesopotamia
3100 BCE - Unification of Egypt
776 BCE - First Olympic Games
509 BCE - Roman Republic established`,
        // Language Arts content
        `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Literature and Writing

Lesson ${pageNumber}: Elements of Poetry

Poetry is a form of literary expression that uses rhythm, rhyme, and imagery to convey emotions and ideas.

Types of Poetry:
1. Haiku - Traditional Japanese form (5-7-5 syllables)
2. Sonnet - 14-line poem with specific rhyme scheme
3. Free Verse - No regular pattern or rhyme
4. Limerick - Humorous 5-line poem

Literary Devices:
• Metaphor - Direct comparison without "like" or "as"
• Simile - Comparison using "like" or "as"
• Alliteration - Repetition of initial consonant sounds
• Personification - Giving human qualities to non-human things

Example Haiku:
Cherry blossoms fall
Gentle breeze carries petals
Spring's beauty fades fast

Writing Exercise:
Create your own haiku about nature.`
    ];
    // Select content based on page number
    const contentIndex = (parseInt(pageNumber) - 1) % educationalContent.length;
    return educationalContent[contentIndex];
}
function ensureUploadDir(classId, subjectId) {
    const className = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT name FROM classes WHERE id = ?').get(classId);
    const subjectName = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT name FROM subjects WHERE id = ?').get(subjectId);
    if (!className || !subjectName) {
        throw new Error('Invalid class or subject ID');
    }
    const uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'uploads', className.name, subjectName.name);
    if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(uploadDir)) {
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].mkdirSync(uploadDir, {
            recursive: true
        });
    }
    return uploadDir;
}
async function saveUploadedFile(file, classId, subjectId, pageType = 'unassigned') {
    try {
        const uploadDir = ensureUploadDir(classId, subjectId);
        // Generate unique filename
        const timestamp = Date.now();
        const extension = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(file.name);
        const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
        const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(uploadDir, filename);
        // Save file to disk
        const buffer = Buffer.from(await file.arrayBuffer());
        __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].writeFileSync(filePath, buffer);
        // Get the next upload order for this class/subject
        const maxOrder = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT MAX(upload_order) as max_order
      FROM images
      WHERE class_id = ? AND subject_id = ?
    `).get(classId, subjectId);
        const uploadOrder = (maxOrder?.max_order || 0) + 1;
        // Save file info to database
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      INSERT INTO images (file_path, original_name, class_id, subject_id, page_type, upload_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `).run(filePath, file.name, classId, subjectId, pageType, uploadOrder);
        return {
            id: result.lastInsertRowid,
            filePath: filePath
        };
    } catch (error) {
        console.error('File upload error:', error);
        throw new Error('Failed to save uploaded file');
    }
}
async function processOCR(imageId, forceRegenerate = false) {
    try {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT file_path, original_name FROM images WHERE id = ?').get(imageId);
        if (!image) {
            throw new Error('Image not found');
        }
        // Check if OCR already exists (unless force regenerate is requested)
        if (!forceRegenerate) {
            const existingOCR = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT content FROM ocr_text WHERE image_id = ?').get(imageId);
            if (existingOCR) {
                console.log(`OCR already exists for image ID: ${imageId}`);
                return existingOCR.content;
            }
        } else {
            console.log(`Force regenerating OCR for image ID: ${imageId}`);
            // Clear existing OCR data
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM ocr_text WHERE image_id = ?').run(imageId);
        }
        // Check if file exists
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(image.file_path)) {
            throw new Error('Image file not found on disk');
        }
        console.log(`Starting OCR processing for image ID: ${imageId}, file: ${image.file_path}`);
        try {
            // Try real OCR with Tesseract.js using simplified configuration
            console.log(`Starting real OCR processing for image ID: ${imageId}`);
            console.log(`Image path: ${image.file_path}`);
            // Create worker with proper configuration for Next.js
            const worker = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tesseract$2e$js$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createWorker"])('eng', 1, {
                workerPath: '/tesseract/worker.js',
                corePath: '/tesseract/tesseract-core.wasm.js'
            });
            console.log(`Tesseract worker created, processing image: ${image.file_path}`);
            // Perform OCR recognition
            const { data: { text, confidence } } = await worker.recognize(image.file_path);
            await worker.terminate();
            console.log(`Real OCR completed for image ID: ${imageId}`);
            console.log(`Extracted text length: ${text.length} characters`);
            console.log(`OCR confidence: ${confidence}%`);
            // Clean up the extracted text
            const cleanedText = text.trim();
            if (cleanedText.length === 0) {
                throw new Error('No text extracted from image');
            }
            // Save OCR text to database
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 1)
      `).run(imageId, cleanedText);
            console.log(`Real OCR completed and saved for image ID: ${imageId}`);
            return cleanedText;
        } catch (tesseractError) {
            console.error(`Real OCR failed for image ${imageId}:`, tesseractError);
            console.error(`Error details:`, {
                message: tesseractError instanceof Error ? tesseractError.message : 'Unknown error',
                stack: tesseractError instanceof Error ? tesseractError.stack : 'No stack trace',
                name: tesseractError instanceof Error ? tesseractError.name : 'Unknown error type'
            });
            // Fallback to mock OCR if real OCR fails
            console.log(`Falling back to mock OCR for image ID: ${imageId}`);
            const mockOCRContent = generateMockOCRContent(image.original_name, imageId);
            console.log(`Fallback mock OCR generated for image ID: ${imageId}, length: ${mockOCRContent.length} characters`);
            // Save OCR text to database with a note about fallback
            const fallbackContent = `[FALLBACK OCR - Real OCR Failed]\n\n${mockOCRContent}\n\n[Original Error: ${tesseractError instanceof Error ? tesseractError.message : 'Unknown error'}]`;
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 1)
      `).run(imageId, fallbackContent);
            console.log(`Fallback OCR completed and saved for image ID: ${imageId}`);
            return fallbackContent;
        }
    } catch (error) {
        console.error(`OCR processing error for image ${imageId}:`, error);
        // Save error state to database
        try {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 0)
      `).run(imageId, `OCR Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        } catch (dbError) {
            console.error('Failed to save OCR error to database:', dbError);
        }
        throw new Error(`Failed to process OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
function getOCRText(imageId) {
    const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT content FROM ocr_text WHERE image_id = ? ORDER BY created_at DESC LIMIT 1').get(imageId);
    return result ? result.content : null;
}
function getImages(classId, subjectId) {
    let query = `
    SELECT i.*, c.name as class_name, s.name as subject_name,
           ocr.content as ocr_content, ocr.processed
    FROM images i
    JOIN classes c ON i.class_id = c.id
    JOIN subjects s ON i.subject_id = s.id
    LEFT JOIN ocr_text ocr ON i.id = ocr.image_id
  `;
    const params = [];
    const conditions = [];
    if (classId) {
        conditions.push('i.class_id = ?');
        params.push(classId);
    }
    if (subjectId) {
        conditions.push('i.subject_id = ?');
        params.push(subjectId);
    }
    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }
    query += ' ORDER BY COALESCE(i.upload_order, 999999) ASC, i.uploaded_at ASC';
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(query).all(...params);
}
function deleteImage(imageId) {
    try {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT file_path FROM images WHERE id = ?').get(imageId);
        if (image && __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].existsSync(image.file_path)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].unlinkSync(image.file_path);
        }
        // Delete from database (OCR text will be deleted by foreign key cascade)
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('DELETE FROM images WHERE id = ?').run(imageId);
        return true;
    } catch (error) {
        console.error('Delete image error:', error);
        return false;
    }
}
function parseQuestionsFromOCR(ocrText, imageId) {
    const questions = [];
    // Get chapter context from image if provided
    let chapterContext = '';
    if (imageId) {
        const image = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT page_type FROM images WHERE id = ?').get(imageId);
        if (image && image.page_type && image.page_type.startsWith('chapter-')) {
            chapterContext = image.page_type.replace('chapter-', 'Chapter ');
        }
    }
    // Split text into lines and clean up
    const lines = ocrText.split('\n').map((line)=>line.trim()).filter((line)=>line.length > 0);
    let currentQuestion = '';
    let currentOptions = [];
    let questionType = 'short_answer'; // default
    for(let i = 0; i < lines.length; i++){
        const line = lines[i];
        // Detect question patterns
        if (line.match(/^\d+[\.\)]\s*/) || line.match(/^Q\d*[\.\)]\s*/i)) {
            // Save previous question if exists
            if (currentQuestion) {
                questions.push({
                    type: questionType,
                    content: currentQuestion,
                    options: currentOptions.length > 0 ? currentOptions : undefined,
                    chapter: chapterContext
                });
            }
            // Start new question
            currentQuestion = line.replace(/^\d+[\.\)]\s*/, '').replace(/^Q\d*[\.\)]\s*/i, '');
            currentOptions = [];
            // Detect question type
            if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {
                questionType = 'true_false';
            } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {
                questionType = 'fill_blank';
            } else {
                questionType = 'short_answer';
            }
        } else if (line.match(/^[a-d][\.\)]\s*/i) || line.match(/^\([a-d]\)\s*/i)) {
            if (currentQuestion) {
                questionType = 'mcq';
                const option = line.replace(/^[a-d][\.\)]\s*/i, '').replace(/^\([a-d]\)\s*/i, '');
                currentOptions.push(option);
            }
        } else if (currentQuestion && !line.match(/^[a-d][\.\)]\s*/i)) {
            currentQuestion += ' ' + line;
        }
    }
    // Save last question
    if (currentQuestion) {
        questions.push({
            type: questionType,
            content: currentQuestion,
            options: currentOptions.length > 0 ? currentOptions : undefined,
            chapter: chapterContext
        });
    }
    return questions;
}
function updateImagePageType(imageId, pageType) {
    try {
        const validPageTypes = [
            'cover',
            'contents',
            'unassigned',
            ...Array.from({
                length: 30
            }, (_, i)=>`chapter-${i + 1}`)
        ];
        if (!validPageTypes.includes(pageType)) {
            throw new Error('Invalid page type');
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('UPDATE images SET page_type = ? WHERE id = ?').run(pageType, imageId);
        return true;
    } catch (error) {
        console.error('Update page type error:', error);
        return false;
    }
}
function autoAssignPageTypes(classId, subjectId) {
    try {
        const images = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      SELECT id, upload_order, page_type
      FROM images
      WHERE class_id = ? AND subject_id = ?
      ORDER BY upload_order ASC
    `).all(classId, subjectId);
        let currentChapter = 1;
        let foundFirstChapter = false;
        for (const image of images){
            if (image.page_type !== 'unassigned') {
                // If this is a chapter marker, update current chapter
                const chapterMatch = image.page_type.match(/^chapter-(\d+)$/);
                if (chapterMatch) {
                    currentChapter = parseInt(chapterMatch[1]);
                    foundFirstChapter = true;
                }
                continue;
            }
            // Auto-assign based on position
            if (!foundFirstChapter) {
                // Before any chapter is found, assume it's cover or contents
                if (image.upload_order === 1) {
                    updateImagePageType(image.id, 'cover');
                } else {
                    updateImagePageType(image.id, 'contents');
                }
            } else {
                // After a chapter is found, assign to current chapter
                updateImagePageType(image.id, `chapter-${currentChapter}`);
            }
        }
    } catch (error) {
        console.error('Auto-assign page types error:', error);
    }
}
function getFileSize(filePath) {
    try {
        const stats = __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["default"].statSync(filePath);
        const bytes = stats.size;
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = [
            'Bytes',
            'KB',
            'MB',
            'GB'
        ];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    } catch (error) {
        return 'Unknown';
    }
}
}}),
"[project]/src/app/api/admin/books/[id]/clear-ocr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DELETE": (()=>DELETE)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/models.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$upload$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/upload.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
;
;
;
async function DELETE(request, { params }) {
    const user = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAdmin"])();
    if (!user) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Unauthorized'
        }, {
            status: 401
        });
    }
    try {
        const { id } = await params;
        const bookId = parseInt(id);
        if (isNaN(bookId)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid book ID'
            }, {
                status: 400
            });
        }
        // Verify book exists
        const book = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$models$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BookModel"].getById(bookId);
        if (!book) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Book not found'
            }, {
                status: 404
            });
        }
        // Get all images for this book
        const images = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare('SELECT id FROM images WHERE book_id = ?').all(bookId);
        // Clear all OCR data for this book
        const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].prepare(`
      DELETE FROM ocr_text
      WHERE image_id IN (
        SELECT id FROM images WHERE book_id = ?
      )
    `).run(bookId);
        // Regenerate OCR with new mock system for all images
        let regeneratedCount = 0;
        for (const image of images){
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$upload$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["processOCR"])(image.id, true); // Force regenerate
                regeneratedCount++;
            } catch (error) {
                console.error(`Failed to regenerate OCR for image ${image.id}:`, error);
            }
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: `Cleared OCR data for ${result.changes} images and regenerated ${regeneratedCount} with new mock system`,
            cleared_count: result.changes,
            regenerated_count: regeneratedCount
        });
    } catch (error) {
        console.error('Error clearing OCR data:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to clear OCR data'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__10f7c649._.js.map