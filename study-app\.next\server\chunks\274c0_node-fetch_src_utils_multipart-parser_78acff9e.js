module.exports = {

"[project]/node_modules/gaxios/node_modules/node-fetch/src/utils/multipart-parser.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/274c0_node-fetch_src_utils_multipart-parser_a78a4705.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/gaxios/node_modules/node-fetch/src/utils/multipart-parser.js [app-route] (ecmascript)");
    });
});
}}),

};