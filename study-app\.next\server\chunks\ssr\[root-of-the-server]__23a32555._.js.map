{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Books', href: '/admin/books', icon: '📖' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/ClearPagesButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface ClearPagesButtonProps {\n  bookId: number;\n}\n\nexport default function ClearPagesButton({ bookId }: ClearPagesButtonProps) {\n  const router = useRouter();\n  const [isClearing, setIsClearing] = useState(false);\n  const [showConfirm, setShowConfirm] = useState(false);\n\n  const handleClear = async () => {\n    if (!showConfirm) {\n      setShowConfirm(true);\n      return;\n    }\n\n    setIsClearing(true);\n    try {\n      const response = await fetch(`/api/admin/books/${bookId}/clear-pages`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        router.refresh();\n        setShowConfirm(false);\n      } else {\n        alert('Failed to clear pages');\n      }\n    } catch (error) {\n      alert('Error clearing pages');\n    } finally {\n      setIsClearing(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setShowConfirm(false);\n  };\n\n  if (showConfirm) {\n    return (\n      <div className=\"flex space-x-2\">\n        <button\n          onClick={handleClear}\n          disabled={isClearing}\n          className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50\"\n        >\n          {isClearing ? 'Clearing...' : 'Confirm Clear'}\n        </button>\n        <button\n          onClick={handleCancel}\n          disabled={isClearing}\n          className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n        >\n          Cancel\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <button\n      onClick={handleClear}\n      className=\"inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md shadow-sm text-red-700 bg-white hover:bg-red-50\"\n    >\n      Clear All Pages\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,iBAAiB,EAAE,MAAM,EAAyB;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa;YAChB,eAAe;YACf;QACF;QAEA,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,OAAO,YAAY,CAAC,EAAE;gBACrE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,OAAO;gBACd,eAAe;YACjB,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,aAAa,gBAAgB;;;;;;8BAEhC,8OAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;kBACX;;;;;;AAIL", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/BookPageTypeModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface BookImage {\n  id: number;\n  original_name: string;\n  page_number: number;\n  page_type: string;\n}\n\ninterface BookPageTypeModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  images: BookImage[];\n  selectedImageIds: number[];\n  onSave: (updates: { imageId: number; pageType: string }[]) => Promise<void>;\n}\n\n// Client-side utility function\nconst getPageTypeColor = (pageType: string): string => {\n  switch (pageType) {\n    case 'cover':\n      return 'bg-purple-100 text-purple-800';\n    case 'contents':\n      return 'bg-blue-100 text-blue-800';\n    case 'unassigned':\n      return 'bg-gray-100 text-gray-800';\n    default:\n      if (pageType.startsWith('chapter-')) {\n        return 'bg-green-100 text-green-800';\n      }\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport default function BookPageTypeModal({\n  isOpen,\n  onClose,\n  images,\n  selectedImageIds,\n  onSave\n}: BookPageTypeModalProps) {\n  const [pageTypeAssignments, setPageTypeAssignments] = useState<{ [key: number]: string }>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  if (!isOpen) return null;\n\n  const selectedImages = images.filter(img => selectedImageIds.includes(img.id));\n\n  const pageTypeOptions = [\n    { value: 'cover', label: 'Cover Page', color: 'bg-purple-100 text-purple-800' },\n    { value: 'contents', label: 'Contents/Index', color: 'bg-blue-100 text-blue-800' },\n    { value: 'unassigned', label: 'Unassigned', color: 'bg-gray-100 text-gray-800' },\n    ...Array.from({length: 30}, (_, i) => ({\n      value: `chapter-${i + 1}`,\n      label: `Chapter ${i + 1}`,\n      color: 'bg-green-100 text-green-800'\n    }))\n  ];\n\n  const handlePageTypeChange = (imageId: number, pageType: string) => {\n    setPageTypeAssignments(prev => ({\n      ...prev,\n      [imageId]: pageType\n    }));\n  };\n\n  const handleBulkAssign = (pageType: string) => {\n    const newAssignments: { [key: number]: string } = {};\n    selectedImageIds.forEach(imageId => {\n      newAssignments[imageId] = pageType;\n    });\n    setPageTypeAssignments(newAssignments);\n  };\n\n  const handleSequentialChapterAssign = () => {\n    const newAssignments: { [key: number]: string } = {};\n    let chapterNum = 1;\n\n    selectedImages\n      .sort((a, b) => a.page_number - b.page_number)\n      .forEach((image, index) => {\n        if (index === 0) {\n          // First selected image becomes chapter start\n          newAssignments[image.id] = `chapter-${chapterNum}`;\n        } else {\n          // Subsequent images continue the chapter\n          newAssignments[image.id] = `chapter-${chapterNum}`;\n        }\n      });\n\n    setPageTypeAssignments(newAssignments);\n  };\n\n  const handleSmartAutoAssign = () => {\n    const newAssignments: { [key: number]: string } = {};\n    const sortedImages = selectedImages.sort((a, b) => a.page_number - b.page_number);\n\n    sortedImages.forEach((image, index) => {\n      if (index === 0 && image.page_number === 1) {\n        // First page is likely cover\n        newAssignments[image.id] = 'cover';\n      } else if (index === 1 && image.page_number === 2) {\n        // Second page is likely contents\n        newAssignments[image.id] = 'contents';\n      } else {\n        // Remaining pages start with chapter 1\n        newAssignments[image.id] = 'chapter-1';\n      }\n    });\n\n    setPageTypeAssignments(newAssignments);\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      const updates = Object.entries(pageTypeAssignments).map(([imageId, pageType]) => ({\n        imageId: parseInt(imageId),\n        pageType\n      }));\n\n      await onSave(updates);\n      setPageTypeAssignments({});\n      onClose();\n    } catch (error) {\n      console.error('Failed to save page types:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden\">\n        <div className=\"flex justify-between items-center p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            Assign Page Types ({selectedImageIds.length} images)\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <span className=\"sr-only\">Close</span>\n            <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\">\n          {/* Bulk Assignment Options */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"text-sm font-medium text-gray-900 mb-3\">Quick Assign All Selected:</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              <button\n                onClick={() => handleBulkAssign('cover')}\n                className=\"px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded hover:bg-purple-200\"\n              >\n                Cover\n              </button>\n              <button\n                onClick={() => handleBulkAssign('contents')}\n                className=\"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded hover:bg-blue-200\"\n              >\n                Contents\n              </button>\n              <button\n                onClick={() => handleBulkAssign('chapter-1')}\n                className=\"px-3 py-1 bg-green-100 text-green-800 text-sm rounded hover:bg-green-200\"\n              >\n                Chapter 1\n              </button>\n              <button\n                onClick={handleSequentialChapterAssign}\n                className=\"px-3 py-1 bg-indigo-100 text-indigo-800 text-sm rounded hover:bg-indigo-200\"\n              >\n                Sequential Chapters\n              </button>\n              <button\n                onClick={handleSmartAutoAssign}\n                className=\"px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded hover:bg-yellow-200\"\n              >\n                Smart Auto-Assign\n              </button>\n              <button\n                onClick={() => handleBulkAssign('unassigned')}\n                className=\"px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded hover:bg-gray-200\"\n              >\n                Unassigned\n              </button>\n            </div>\n          </div>\n\n          {/* Individual Image Assignments */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-sm font-medium text-gray-900\">Individual Assignments:</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {selectedImages\n                .sort((a, b) => a.page_number - b.page_number)\n                .map((image) => (\n                <div key={image.id} className=\"flex items-center space-x-3 p-3 border rounded-lg\">\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src={`/api/admin/images/${image.id}/preview`}\n                      alt={image.original_name}\n                      className=\"w-16 h-20 object-cover rounded\"\n                    />\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"text-sm font-medium text-gray-900 truncate\">\n                      Page {image.page_number}\n                    </div>\n                    <div className=\"text-xs text-gray-500 truncate\">\n                      {image.original_name}\n                    </div>\n                    \n                    <div className=\"mt-2\">\n                      <select\n                        value={pageTypeAssignments[image.id] || image.page_type}\n                        onChange={(e) => handlePageTypeChange(image.id, e.target.value)}\n                        className=\"w-full text-sm border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500\"\n                      >\n                        {pageTypeOptions.map((option) => (\n                          <option key={option.value} value={option.value}>\n                            {option.label}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    {/* Current Assignment Display */}\n                    <div className=\"mt-1\">\n                      <span className={`inline-block text-xs px-2 py-1 rounded ${getPageTypeColor(pageTypeAssignments[image.id] || image.page_type)}`}>\n                        Current: {pageTypeAssignments[image.id] || image.page_type}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-3 p-6 border-t bg-gray-50\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n          >\n            Cancel\n          </button>\n          <button\n            onClick={handleSave}\n            disabled={isSaving || Object.keys(pageTypeAssignments).length === 0}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSaving ? 'Saving...' : `Save Changes (${Object.keys(pageTypeAssignments).length})`}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,+BAA+B;AAC/B,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,IAAI,SAAS,UAAU,CAAC,aAAa;gBACnC,OAAO;YACT;YACA,OAAO;IACX;AACF;AAEe,SAAS,kBAAkB,EACxC,MAAM,EACN,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,MAAM,EACiB;IACvB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAC3F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,MAAO,iBAAiB,QAAQ,CAAC,IAAI,EAAE;IAE5E,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAS,OAAO;YAAc,OAAO;QAAgC;QAC9E;YAAE,OAAO;YAAY,OAAO;YAAkB,OAAO;QAA4B;QACjF;YAAE,OAAO;YAAc,OAAO;YAAc,OAAO;QAA4B;WAC5E,MAAM,IAAI,CAAC;YAAC,QAAQ;QAAE,GAAG,CAAC,GAAG,IAAM,CAAC;gBACrC,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;gBACzB,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;gBACzB,OAAO;YACT,CAAC;KACF;IAED,MAAM,uBAAuB,CAAC,SAAiB;QAC7C,uBAAuB,CAAA,OAAQ,CAAC;gBAC9B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,iBAA4C,CAAC;QACnD,iBAAiB,OAAO,CAAC,CAAA;YACvB,cAAc,CAAC,QAAQ,GAAG;QAC5B;QACA,uBAAuB;IACzB;IAEA,MAAM,gCAAgC;QACpC,MAAM,iBAA4C,CAAC;QACnD,IAAI,aAAa;QAEjB,eACG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,OAAO,CAAC,CAAC,OAAO;YACf,IAAI,UAAU,GAAG;gBACf,6CAA6C;gBAC7C,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY;YACpD,OAAO;gBACL,yCAAyC;gBACzC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY;YACpD;QACF;QAEF,uBAAuB;IACzB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,iBAA4C,CAAC;QACnD,MAAM,eAAe,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;QAEhF,aAAa,OAAO,CAAC,CAAC,OAAO;YAC3B,IAAI,UAAU,KAAK,MAAM,WAAW,KAAK,GAAG;gBAC1C,6BAA6B;gBAC7B,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG;YAC7B,OAAO,IAAI,UAAU,KAAK,MAAM,WAAW,KAAK,GAAG;gBACjD,iCAAiC;gBACjC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG;YAC7B,OAAO;gBACL,uCAAuC;gBACvC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG;YAC7B;QACF;QAEA,uBAAuB;IACzB;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,UAAU,OAAO,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,SAAS,SAAS,GAAK,CAAC;oBAChF,SAAS,SAAS;oBAClB;gBACF,CAAC;YAED,MAAM,OAAO;YACb,uBAAuB,CAAC;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,YAAY;QACd;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCAC9B,iBAAiB,MAAM;gCAAC;;;;;;;sCAE9C,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC9D,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;8CACZ,eACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,GAAG,CAAC,CAAC,sBACN,8OAAC;4CAAmB,WAAU;;8DAC5B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;wDAC5C,KAAK,MAAM,aAAa;wDACxB,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEAA6C;gEACpD,MAAM,WAAW;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;sEACZ,MAAM,aAAa;;;;;;sEAGtB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,OAAO,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS;gEACvD,UAAU,CAAC,IAAM,qBAAqB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;gEAC9D,WAAU;0EAET,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;wEAA0B,OAAO,OAAO,KAAK;kFAC3C,OAAO,KAAK;uEADF,OAAO,KAAK;;;;;;;;;;;;;;;sEAQ/B,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,uCAAuC,EAAE,iBAAiB,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS,GAAG;;oEAAE;oEACrH,mBAAmB,CAAC,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS;;;;;;;;;;;;;;;;;;;2CAlCxD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;8BA4C1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU,YAAY,OAAO,IAAI,CAAC,qBAAqB,MAAM,KAAK;4BAClE,WAAU;sCAET,WAAW,cAAc,CAAC,cAAc,EAAE,OAAO,IAAI,CAAC,qBAAqB,MAAM,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAMjG", "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/BookReorderModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface BookImage {\n  id: number;\n  original_name: string;\n  page_number: number;\n  page_type: string;\n}\n\ninterface BookReorderModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  images: BookImage[];\n  onSave: (newOrder: BookImage[]) => Promise<void>;\n}\n\nexport default function BookReorderModal({\n  isOpen,\n  onClose,\n  images,\n  onSave\n}: BookReorderModalProps) {\n  const [reorderedImages, setReorderedImages] = useState<BookImage[]>(images);\n  const [isSaving, setIsSaving] = useState(false);\n\n  if (!isOpen) return null;\n\n  const moveImage = (fromIndex: number, toIndex: number) => {\n    if (toIndex < 0 || toIndex >= reorderedImages.length) return;\n    \n    const newImages = [...reorderedImages];\n    const [movedImage] = newImages.splice(fromIndex, 1);\n    newImages.splice(toIndex, 0, movedImage);\n    setReorderedImages(newImages);\n  };\n\n  const handleSave = async () => {\n    setIsSaving(true);\n    try {\n      await onSave(reorderedImages);\n      onClose();\n    } catch (error) {\n      console.error('Failed to save reorder:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleReset = () => {\n    setReorderedImages([...images]);\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden\">\n        <div className=\"flex justify-between items-center p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            Reorder Pages ({reorderedImages.length} images)\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600\"\n          >\n            <span className=\"sr-only\">Close</span>\n            <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        <div className=\"p-6 overflow-y-auto max-h-[calc(90vh-140px)]\">\n          <div className=\"mb-4 text-sm text-gray-600\">\n            <p>Use the arrow buttons to reorder pages. The first page will become page 1, second will become page 2, etc.</p>\n          </div>\n\n          <div className=\"space-y-2\">\n            {reorderedImages.map((image, index) => (\n              <div\n                key={image.id}\n                className=\"flex items-center space-x-3 p-3 border rounded-lg bg-gray-50\"\n              >\n                {/* Position Controls */}\n                <div className=\"flex flex-col space-y-1\">\n                  <button\n                    onClick={() => moveImage(index, index - 1)}\n                    disabled={index === 0}\n                    className=\"w-8 h-8 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                    title=\"Move up\"\n                  >\n                    ↑\n                  </button>\n                  <button\n                    onClick={() => moveImage(index, index + 1)}\n                    disabled={index === reorderedImages.length - 1}\n                    className=\"w-8 h-8 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                    title=\"Move down\"\n                  >\n                    ↓\n                  </button>\n                </div>\n\n                {/* New Position */}\n                <div className=\"flex-shrink-0 w-12 text-center\">\n                  <span className=\"inline-block px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded\">\n                    {index + 1}\n                  </span>\n                </div>\n\n                {/* Image Preview */}\n                <div className=\"flex-shrink-0\">\n                  <img\n                    src={`/api/admin/images/${image.id}/preview`}\n                    alt={image.original_name}\n                    className=\"w-16 h-20 object-cover rounded\"\n                  />\n                </div>\n\n                {/* Image Info */}\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 truncate\">\n                    {image.original_name}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    Original Page: {image.page_number}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    Type: {image.page_type}\n                  </div>\n                </div>\n\n                {/* Quick Move Controls */}\n                <div className=\"flex flex-col space-y-1\">\n                  <button\n                    onClick={() => moveImage(index, 0)}\n                    disabled={index === 0}\n                    className=\"px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    title=\"Move to first\"\n                  >\n                    First\n                  </button>\n                  <button\n                    onClick={() => moveImage(index, reorderedImages.length - 1)}\n                    disabled={index === reorderedImages.length - 1}\n                    className=\"px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n                    title=\"Move to last\"\n                  >\n                    Last\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center p-6 border-t bg-gray-50\">\n          <button\n            onClick={handleReset}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n          >\n            Reset to Original\n          </button>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleSave}\n              disabled={isSaving}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSaving ? 'Saving...' : 'Save New Order'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAkBe,SAAS,iBAAiB,EACvC,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACgB;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,YAAY,CAAC,WAAmB;QACpC,IAAI,UAAU,KAAK,WAAW,gBAAgB,MAAM,EAAE;QAEtD,MAAM,YAAY;eAAI;SAAgB;QACtC,MAAM,CAAC,WAAW,GAAG,UAAU,MAAM,CAAC,WAAW;QACjD,UAAU,MAAM,CAAC,SAAS,GAAG;QAC7B,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,cAAc;QAClB,mBAAmB;eAAI;SAAO;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCAClC,gBAAgB,MAAM;gCAAC;;;;;;;sCAEzC,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC9D,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;8BAK3E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;0CAAE;;;;;;;;;;;sCAGL,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;oCAEC,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,UAAU,OAAO,QAAQ;oDACxC,UAAU,UAAU;oDACpB,WAAU;oDACV,OAAM;8DACP;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,UAAU,OAAO,QAAQ;oDACxC,UAAU,UAAU,gBAAgB,MAAM,GAAG;oDAC7C,WAAU;oDACV,OAAM;8DACP;;;;;;;;;;;;sDAMH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ;;;;;;;;;;;sDAKb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;gDAC5C,KAAK,MAAM,aAAa;gDACxB,WAAU;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,aAAa;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;;wDAAwB;wDACrB,MAAM,WAAW;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;wDAAwB;wDAC9B,MAAM,SAAS;;;;;;;;;;;;;sDAK1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,UAAU,OAAO;oDAChC,UAAU,UAAU;oDACpB,WAAU;oDACV,OAAM;8DACP;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,UAAU,OAAO,gBAAgB,MAAM,GAAG;oDACzD,UAAU,UAAU,gBAAgB,MAAM,GAAG;oDAC7C,WAAU;oDACV,OAAM;8DACP;;;;;;;;;;;;;mCAnEE,MAAM,EAAE;;;;;;;;;;;;;;;;8BA4ErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/BookImageManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Book } from '@/lib/models';\nimport BookPageTypeModal from './BookPageTypeModal';\nimport BookReorderModal from './BookReorderModal';\n\ninterface BookImage {\n  id: number;\n  original_name: string;\n  page_number: number;\n  page_type: string;\n  file_path: string;\n  has_ocr?: boolean;\n  ocr_processed?: boolean;\n}\n\ninterface BookImageManagerProps {\n  book: Book;\n  initialImages?: BookImage[];\n}\n\n// Client-side utility functions\nconst getPageTypeDisplayName = (pageType: string): string => {\n  switch (pageType) {\n    case 'cover':\n      return 'Cover';\n    case 'contents':\n      return 'Contents';\n    case 'unassigned':\n      return 'Unassigned';\n    default:\n      if (pageType.startsWith('chapter-')) {\n        const parts = pageType.split('-');\n        if (parts.length === 2) {\n          return `Chapter ${parts[1]}`;\n        } else if (parts.length === 3 && parts[2].startsWith('P')) {\n          return `Chapter ${parts[1]} - Page ${parts[2].substring(1)}`;\n        }\n      }\n      return pageType;\n  }\n};\n\nconst getPageTypeColor = (pageType: string): string => {\n  switch (pageType) {\n    case 'cover':\n      return 'bg-purple-100 text-purple-800';\n    case 'contents':\n      return 'bg-blue-100 text-blue-800';\n    case 'unassigned':\n      return 'bg-gray-100 text-gray-800';\n    default:\n      if (pageType.startsWith('chapter-')) {\n        return 'bg-green-100 text-green-800';\n      }\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport default function BookImageManager({ book, initialImages = [] }: BookImageManagerProps) {\n  const [images, setImages] = useState<BookImage[]>(initialImages);\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [selectedImages, setSelectedImages] = useState<Set<number>>(new Set());\n  const [showPageTypeModal, setShowPageTypeModal] = useState(false);\n  const [showReorderModal, setShowReorderModal] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Image preview modal state\n  const [previewModal, setPreviewModal] = useState({\n    isOpen: false,\n    imageUrl: '',\n    imageName: ''\n  });\n\n  useEffect(() => {\n    loadImages();\n  }, [book.id]);\n\n  const loadImages = async () => {\n    try {\n      const response = await fetch(`/api/admin/books/${book.id}/images`);\n      if (response.ok) {\n        const data = await response.json();\n        setImages(data.images || []);\n      }\n    } catch (error) {\n      console.error('Failed to load images:', error);\n    }\n  };\n\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    setUploading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const formData = new FormData();\n      \n      // Add all files to FormData\n      Array.from(files).forEach(file => {\n        formData.append('files', file);\n      });\n\n      const response = await fetch(`/api/admin/books/${book.id}/upload-images`, {\n        method: 'POST',\n        body: formData,\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        let successMessage = `Successfully uploaded ${result.uploaded_count} images`;\n\n        // Add analysis information\n        if (result.analysis) {\n          if (result.analysis.sequence_detected) {\n            successMessage += ' (Sequential order detected)';\n          }\n\n          if (result.analysis.recommendations && result.analysis.recommendations.length > 0) {\n            setSuccess(successMessage + '\\n\\nRecommendations:\\n' + result.analysis.recommendations.join('\\n'));\n          } else {\n            setSuccess(successMessage);\n          }\n        } else {\n          setSuccess(successMessage);\n        }\n\n        if (result.errors && result.errors.length > 0) {\n          setError(`Some files had issues: ${result.errors.join(', ')}`);\n        }\n        await loadImages(); // Reload images\n      } else {\n        setError(result.error || 'Upload failed');\n      }\n    } catch (error) {\n      setError('Upload failed. Please try again.');\n    } finally {\n      setUploading(false);\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    }\n  };\n\n  const triggerFileInput = () => {\n    fileInputRef.current?.click();\n  };\n\n  const openImagePreview = (imageId: number, imageName: string) => {\n    setPreviewModal({\n      isOpen: true,\n      imageUrl: `/api/admin/images/${imageId}/preview`,\n      imageName\n    });\n  };\n\n  const closeImagePreview = () => {\n    setPreviewModal({\n      isOpen: false,\n      imageUrl: '',\n      imageName: ''\n    });\n  };\n\n  const toggleImageSelection = (imageId: number) => {\n    const newSelected = new Set(selectedImages);\n    if (newSelected.has(imageId)) {\n      newSelected.delete(imageId);\n    } else {\n      newSelected.add(imageId);\n    }\n    setSelectedImages(newSelected);\n  };\n\n  const selectAllImages = () => {\n    setSelectedImages(new Set(images.map(img => img.id)));\n  };\n\n  const clearSelection = () => {\n    setSelectedImages(new Set());\n  };\n\n\n\n  const handlePageTypeAssignments = async (updates: { imageId: number; pageType: string }[]) => {\n    try {\n      const response = await fetch(`/api/admin/books/${book.id}/images/page-types`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          pageTypeUpdates: updates\n        }),\n      });\n\n      if (response.ok) {\n        setSuccess('Page types updated successfully');\n        await loadImages(); // Reload images to show updated page types\n        setSelectedImages(new Set()); // Clear selection\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to update page types');\n      }\n    } catch (error) {\n      setError('Failed to update page types');\n    }\n  };\n\n  const moveImageUp = async (imageId: number) => {\n    const currentIndex = images.findIndex(img => img.id === imageId);\n    if (currentIndex <= 0) return;\n\n    const newImages = [...images];\n    [newImages[currentIndex], newImages[currentIndex - 1]] = [newImages[currentIndex - 1], newImages[currentIndex]];\n\n    await reorderImages(newImages);\n  };\n\n  const moveImageDown = async (imageId: number) => {\n    const currentIndex = images.findIndex(img => img.id === imageId);\n    if (currentIndex >= images.length - 1) return;\n\n    const newImages = [...images];\n    [newImages[currentIndex], newImages[currentIndex + 1]] = [newImages[currentIndex + 1], newImages[currentIndex]];\n\n    await reorderImages(newImages);\n  };\n\n  const reorderImages = async (newImageOrder: BookImage[]) => {\n    try {\n      const imageOrders = newImageOrder.map((image, index) => ({\n        imageId: image.id,\n        newPosition: index + 1\n      }));\n\n      const response = await fetch(`/api/admin/books/${book.id}/images/reorder`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ imageOrders }),\n      });\n\n      if (response.ok) {\n        setSuccess('Images reordered successfully');\n        await loadImages(); // Reload images to show new order\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to reorder images');\n      }\n    } catch (error) {\n      setError('Failed to reorder images');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Section */}\n      <div className=\"bg-white shadow rounded-lg p-4\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h3 className=\"text-lg font-medium text-gray-900\">Upload Images</h3>\n          <button\n            onClick={triggerFileInput}\n            disabled={uploading}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\n          >\n            {uploading ? 'Uploading...' : '📁 Select Images'}\n          </button>\n        </div>\n\n        <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-4 text-center\">\n          <div className=\"text-sm text-gray-600\">\n            <p className=\"font-medium\">Drag and drop images here or use the button above</p>\n            <p className=\"mt-1\">Supported: JPG, PNG, GIF, WebP • Max 10MB each • Sequential filenames auto-sorted</p>\n          </div>\n        </div>\n\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\"image/*\"\n          onChange={handleFileSelect}\n          className=\"hidden\"\n        />\n\n        {error && (\n          <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <p className=\"text-sm text-red-600\">{error}</p>\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mt-4 p-3 bg-green-50 border border-green-200 rounded-md\">\n            <p className=\"text-sm text-green-600\">{success}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Images Management Section */}\n      {images.length > 0 && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              Uploaded Images ({images.length})\n            </h3>\n            \n            <div className=\"flex space-x-2\">\n              <button\n                onClick={selectAllImages}\n                className=\"text-sm text-indigo-600 hover:text-indigo-800\"\n              >\n                Select All\n              </button>\n              <button\n                onClick={clearSelection}\n                className=\"text-sm text-gray-600 hover:text-gray-800\"\n              >\n                Clear Selection\n              </button>\n              <button\n                onClick={() => setShowPageTypeModal(true)}\n                disabled={selectedImages.size === 0}\n                className=\"px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Assign Page Types ({selectedImages.size})\n              </button>\n              <button\n                onClick={() => setShowReorderModal(true)}\n                className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\"\n              >\n                Reorder Pages\n              </button>\n            </div>\n          </div>\n\n          {/* Images Grid */}\n          <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n            {images.map((image) => (\n              <div\n                key={image.id}\n                className={`relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${\n                  selectedImages.has(image.id)\n                    ? 'border-indigo-500 ring-2 ring-indigo-200'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => toggleImageSelection(image.id)}\n              >\n                <div className=\"aspect-w-3 aspect-h-4\">\n                  <img\n                    src={`/api/admin/images/${image.id}/preview`}\n                    alt={image.original_name}\n                    className=\"w-full h-32 object-cover\"\n                    onDoubleClick={(e) => {\n                      e.stopPropagation();\n                      openImagePreview(image.id, image.original_name);\n                    }}\n                  />\n                </div>\n                \n                {/* Page Number Badge */}\n                <div className=\"absolute top-1 left-1 bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded\">\n                  P{image.page_number}\n                </div>\n\n                {/* Selection Checkbox */}\n                <div className=\"absolute top-1 right-1\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedImages.has(image.id)}\n                    onChange={() => toggleImageSelection(image.id)}\n                    className=\"w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500\"\n                    onClick={(e) => e.stopPropagation()}\n                  />\n                </div>\n\n                {/* Reorder Controls */}\n                <div className=\"absolute top-1 left-1 flex flex-col space-y-1\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      moveImageUp(image.id);\n                    }}\n                    disabled={images.findIndex(img => img.id === image.id) === 0}\n                    className=\"w-6 h-6 bg-black bg-opacity-75 text-white text-xs rounded hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                    title=\"Move up\"\n                  >\n                    ↑\n                  </button>\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      moveImageDown(image.id);\n                    }}\n                    disabled={images.findIndex(img => img.id === image.id) === images.length - 1}\n                    className=\"w-6 h-6 bg-black bg-opacity-75 text-white text-xs rounded hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                    title=\"Move down\"\n                  >\n                    ↓\n                  </button>\n                </div>\n\n                {/* Page Type Badge */}\n                <div className=\"absolute bottom-1 left-1 right-1\">\n                  <span className={`inline-block w-full text-center text-xs px-1 py-0.5 rounded ${getPageTypeColor(image.page_type)}`}>\n                    {getPageTypeDisplayName(image.page_type)}\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Image Preview Modal */}\n      {previewModal.isOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\">\n          <div className=\"max-w-4xl max-h-full p-4\">\n            <div className=\"bg-white rounded-lg overflow-hidden\">\n              <div className=\"flex justify-between items-center p-4 border-b\">\n                <h3 className=\"text-lg font-medium\">{previewModal.imageName}</h3>\n                <button\n                  onClick={closeImagePreview}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <span className=\"sr-only\">Close</span>\n                  <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <div className=\"p-4\">\n                <img\n                  src={previewModal.imageUrl}\n                  alt={previewModal.imageName}\n                  className=\"max-w-full max-h-96 mx-auto\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Page Type Assignment Modal */}\n      <BookPageTypeModal\n        isOpen={showPageTypeModal}\n        onClose={() => setShowPageTypeModal(false)}\n        images={images}\n        selectedImageIds={Array.from(selectedImages)}\n        onSave={handlePageTypeAssignments}\n      />\n\n      {/* Reorder Modal */}\n      <BookReorderModal\n        isOpen={showReorderModal}\n        onClose={() => setShowReorderModal(false)}\n        images={images}\n        onSave={reorderImages}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAsBA,gCAAgC;AAChC,MAAM,yBAAyB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,IAAI,SAAS,UAAU,CAAC,aAAa;gBACnC,MAAM,QAAQ,SAAS,KAAK,CAAC;gBAC7B,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE;gBAC9B,OAAO,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM;oBACzD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI;gBAC9D;YACF;YACA,OAAO;IACX;AACF;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,IAAI,SAAS,UAAU,CAAC,aAAa;gBACnC,OAAO;YACT;YACA,OAAO;IACX;AACF;AAEe,SAAS,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAyB;IAC1F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,4BAA4B;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,KAAK,EAAE;KAAC;IAEZ,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC;YACjE,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,MAAM,IAAI,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,IAAI;YAErB,4BAA4B;YAC5B,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;gBACxB,SAAS,MAAM,CAAC,SAAS;YAC3B;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,cAAc,CAAC,EAAE;gBACxE,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,IAAI,iBAAiB,CAAC,sBAAsB,EAAE,OAAO,cAAc,CAAC,OAAO,CAAC;gBAE5E,2BAA2B;gBAC3B,IAAI,OAAO,QAAQ,EAAE;oBACnB,IAAI,OAAO,QAAQ,CAAC,iBAAiB,EAAE;wBACrC,kBAAkB;oBACpB;oBAEA,IAAI,OAAO,QAAQ,CAAC,eAAe,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,GAAG;wBACjF,WAAW,iBAAiB,2BAA2B,OAAO,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;oBAC9F,OAAO;wBACL,WAAW;oBACb;gBACF,OAAO;oBACL,WAAW;gBACb;gBAEA,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,GAAG;oBAC7C,SAAS,CAAC,uBAAuB,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO;gBAC/D;gBACA,MAAM,cAAc,gBAAgB;YACtC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;YACb,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,mBAAmB,CAAC,SAAiB;QACzC,gBAAgB;YACd,QAAQ;YACR,UAAU,CAAC,kBAAkB,EAAE,QAAQ,QAAQ,CAAC;YAChD;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,gBAAgB;YACd,QAAQ;YACR,UAAU;YACV,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,UAAU;YAC5B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;IACpD;IAEA,MAAM,iBAAiB;QACrB,kBAAkB,IAAI;IACxB;IAIA,MAAM,4BAA4B,OAAO;QACvC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,kBAAkB,CAAC,EAAE;gBAC5E,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,iBAAiB;gBACnB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,MAAM,cAAc,2CAA2C;gBAC/D,kBAAkB,IAAI,QAAQ,kBAAkB;YAClD,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,MAAM,eAAe,OAAO,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACxD,IAAI,gBAAgB,GAAG;QAEvB,MAAM,YAAY;eAAI;SAAO;QAC7B,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,CAAC,GAAG;YAAC,SAAS,CAAC,eAAe,EAAE;YAAE,SAAS,CAAC,aAAa;SAAC;QAE/G,MAAM,cAAc;IACtB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,eAAe,OAAO,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACxD,IAAI,gBAAgB,OAAO,MAAM,GAAG,GAAG;QAEvC,MAAM,YAAY;eAAI;SAAO;QAC7B,CAAC,SAAS,CAAC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE,CAAC,GAAG;YAAC,SAAS,CAAC,eAAe,EAAE;YAAE,SAAS,CAAC,aAAa;SAAC;QAE/G,MAAM,cAAc;IACtB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,cAAc,cAAc,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;oBACvD,SAAS,MAAM,EAAE;oBACjB,aAAa,QAAQ;gBACvB,CAAC;YAED,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC,eAAe,CAAC,EAAE;gBACzE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,MAAM,cAAc,kCAAkC;YACxD,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;kCAIlC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,8OAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;kCAIxB,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,WAAU;;;;;;oBAGX,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;oBAIxC,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;;;;;;;;;;;;YAM5C,OAAO,MAAM,GAAG,mBACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoC;oCAC9B,OAAO,MAAM;oCAAC;;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,UAAU,eAAe,IAAI,KAAK;wCAClC,WAAU;;4CACX;4CACqB,eAAe,IAAI;4CAAC;;;;;;;kDAE1C,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gCAEC,WAAW,CAAC,2EAA2E,EACrF,eAAe,GAAG,CAAC,MAAM,EAAE,IACvB,6CACA,yCACJ;gCACF,SAAS,IAAM,qBAAqB,MAAM,EAAE;;kDAE5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;4CAC5C,KAAK,MAAM,aAAa;4CACxB,WAAU;4CACV,eAAe,CAAC;gDACd,EAAE,eAAe;gDACjB,iBAAiB,MAAM,EAAE,EAAE,MAAM,aAAa;4CAChD;;;;;;;;;;;kDAKJ,8OAAC;wCAAI,WAAU;;4CAAsF;4CACjG,MAAM,WAAW;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,eAAe,GAAG,CAAC,MAAM,EAAE;4CACpC,UAAU,IAAM,qBAAqB,MAAM,EAAE;4CAC7C,WAAU;4CACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,YAAY,MAAM,EAAE;gDACtB;gDACA,UAAU,OAAO,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE,MAAM;gDAC3D,WAAU;gDACV,OAAM;0DACP;;;;;;0DAGD,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,cAAc,MAAM,EAAE;gDACxB;gDACA,UAAU,OAAO,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE,MAAM,OAAO,MAAM,GAAG;gDAC3E,WAAU;gDACV,OAAM;0DACP;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAW,CAAC,4DAA4D,EAAE,iBAAiB,MAAM,SAAS,GAAG;sDAChH,uBAAuB,MAAM,SAAS;;;;;;;;;;;;+BAjEtC,MAAM,EAAE;;;;;;;;;;;;;;;;YA2EtB,aAAa,MAAM,kBAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuB,aAAa,SAAS;;;;;;kDAC3D,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;0CAI3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK,aAAa,QAAQ;oCAC1B,KAAK,aAAa,SAAS;oCAC3B,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,QAAQ;gBACR,kBAAkB,MAAM,IAAI,CAAC;gBAC7B,QAAQ;;;;;;0BAIV,8OAAC,sIAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,QAAQ;gBACR,QAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}]}