const CHUNK_PUBLIC_PATH = "server/app/api/admin/books/[id]/clear-ocr/route.js";
const runtime = require("../../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_f45984ad._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_4efc9a3e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__10f7c649._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/books/[id]/clear-ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/[id]/clear-ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/books/[id]/clear-ocr/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
