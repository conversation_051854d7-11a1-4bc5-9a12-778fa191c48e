{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/text-processing.ts"], "sourcesContent": ["/**\n * Text post-processing utilities for OCR output\n * Cleans up and formats extracted text for better readability\n */\n\nexport interface TextProcessingOptions {\n  fixLineBreaks: boolean;\n  mergeParagraphs: boolean;\n  removeExtraSpaces: boolean;\n  fixCapitalization: boolean;\n  preserveFormatting: boolean;\n}\n\nexport const DEFAULT_PROCESSING_OPTIONS: TextProcessingOptions = {\n  fixLineBreaks: true,\n  mergeParagraphs: true,\n  removeExtraSpaces: true,\n  fixCapitalization: true,\n  preserveFormatting: false\n};\n\n/**\n * Main text processing function\n */\nexport function processOCRText(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS): string {\n  if (!rawText || rawText.trim().length === 0) {\n    return rawText;\n  }\n\n  let processedText = rawText;\n\n  // Step 1: Remove OCR artifacts and random characters\n  processedText = removeOCRArtifacts(processedText);\n\n  // Step 2: Basic cleanup\n  if (options.removeExtraSpaces) {\n    processedText = removeExtraSpaces(processedText);\n  }\n\n  // Step 3: Fix question numbering and formatting\n  processedText = fixQuestionNumbering(processedText);\n\n  // Step 4: Fix line breaks and continuity\n  if (options.fixLineBreaks) {\n    processedText = fixLineBreaks(processedText);\n  }\n\n  // Step 5: Merge broken paragraphs\n  if (options.mergeParagraphs) {\n    processedText = mergeBrokenParagraphs(processedText);\n  }\n\n  // Step 6: Fix capitalization issues\n  if (options.fixCapitalization) {\n    processedText = fixCapitalization(processedText);\n  }\n\n  // Step 7: Final cleanup\n  processedText = finalCleanup(processedText);\n\n  return processedText;\n}\n\n/**\n * Remove OCR artifacts and random characters\n */\nfunction removeOCRArtifacts(text: string): string {\n  return text\n    // Remove common OCR artifacts and random characters\n    .replace(/^[ル\\u3000-\\u303F\\u3040-\\u309F\\u30A0-\\u30FF\\uFF00-\\uFFEF]+/gm, '') // Japanese characters at start of lines\n    .replace(/[^\\x00-\\x7F\\u00A0-\\u024F\\u1E00-\\u1EFF\\u2000-\\u206F\\u2070-\\u209F\\u20A0-\\u20CF\\u2100-\\u214F\\u2190-\\u21FF\\u2200-\\u22FF]/g, '') // Remove non-Latin characters except common symbols\n    // Remove isolated single characters that are likely OCR errors\n    .replace(/^\\s*[^\\w\\s]\\s*$/gm, '')\n    // Remove lines with only symbols or numbers\n    .replace(/^\\s*[^\\w\\s]{1,3}\\s*$/gm, '')\n    // Clean up any resulting empty lines\n    .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n');\n}\n\n/**\n * Fix question numbering and formatting\n */\nfunction fixQuestionNumbering(text: string): string {\n  return text\n    // Fix separated question numbers/letters (e.g., \"a.\" on one line, question on next)\n    .replace(/^([a-z]\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    .replace(/^([A-Z]\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    .replace(/^(\\d+\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    // Fix numbered questions with letters (e.g., \"1. a.\" becomes \"1a.\")\n    .replace(/(\\d+)\\.\\s+([a-z])\\./g, '$1$2.')\n    // Fix spacing around question numbers\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s+/gm, '$1 ')\n    // Fix questions that start with lowercase after number\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s+([a-z])/gm, (match, num, letter) => num + ' ' + letter.toUpperCase())\n    // Join question parts that were split\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s*\\n\\s*([a-z])/gm, '$1 ' + '$2'.toUpperCase());\n}\n\n/**\n * Remove extra spaces and normalize whitespace\n */\nfunction removeExtraSpaces(text: string): string {\n  return text\n    // Replace multiple spaces with single space\n    .replace(/[ \\t]+/g, ' ')\n    // Remove spaces at beginning and end of lines\n    .replace(/^[ \\t]+|[ \\t]+$/gm, '')\n    // Remove multiple consecutive newlines (keep max 2)\n    .replace(/\\n{3,}/g, '\\n\\n');\n}\n\n/**\n * Fix line breaks and word continuity\n */\nfunction fixLineBreaks(text: string): string {\n  return text\n    // Fix hyphenated words split across lines\n    .replace(/(\\w+)-\\s*\\n\\s*(\\w+)/g, '$1$2')\n    // Join lines that end with lowercase and start with lowercase (likely continuation)\n    .replace(/([a-z,])\\s*\\n\\s*([a-z])/g, '$1 $2')\n    // Join lines where previous line doesn't end with punctuation and next starts with lowercase\n    .replace(/([^.!?:;\\n])\\s*\\n\\s*([a-z])/g, '$1 $2')\n    // Handle educational content - join lines that are clearly continuations\n    .replace(/([a-z])\\s*\\n\\s*([a-z][^.!?]*[a-z])\\s*\\n/g, '$1 $2 ')\n    // Fix broken sentences in educational content\n    .replace(/([a-z])\\s*\\n\\s*([a-z][^A-Z]*[.!?])/g, '$1 $2')\n    // Preserve intentional line breaks (after punctuation)\n    .replace(/([.!?:;])\\s*\\n\\s*([A-Z])/g, '$1\\n\\n$2')\n    // Preserve question formatting\n    .replace(/([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s*\\n\\s*/g, '$1 ');\n}\n\n/**\n * Merge broken paragraphs back together\n */\nfunction mergeBrokenParagraphs(text: string): string {\n  const lines = text.split('\\n');\n  const mergedLines: string[] = [];\n  let currentParagraph = '';\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    \n    if (line === '') {\n      // Empty line - end current paragraph\n      if (currentParagraph.trim()) {\n        mergedLines.push(currentParagraph.trim());\n        currentParagraph = '';\n      }\n      mergedLines.push('');\n    } else if (isNewParagraph(line, lines[i - 1])) {\n      // Start of new paragraph\n      if (currentParagraph.trim()) {\n        mergedLines.push(currentParagraph.trim());\n      }\n      currentParagraph = line;\n    } else {\n      // Continue current paragraph\n      if (currentParagraph) {\n        currentParagraph += ' ' + line;\n      } else {\n        currentParagraph = line;\n      }\n    }\n  }\n\n  // Add final paragraph\n  if (currentParagraph.trim()) {\n    mergedLines.push(currentParagraph.trim());\n  }\n\n  return mergedLines.join('\\n');\n}\n\n/**\n * Determine if a line starts a new paragraph\n */\nfunction isNewParagraph(currentLine: string, previousLine?: string): boolean {\n  if (!currentLine || !previousLine) return true;\n\n  const current = currentLine.trim();\n  const previous = previousLine.trim();\n\n  // New paragraph indicators\n  const newParagraphPatterns = [\n    /^\\d+\\./, // Numbered list\n    /^[a-z]\\./, // Lettered list (a., b., c.)\n    /^[A-Z]\\./, // Capital lettered list (A., B., C.)\n    /^[A-Z][a-z]*:/, // Section headers (Word:)\n    /^Chapter \\d+/i, // Chapter headers\n    /^Section \\d+/i, // Section headers\n    /^[A-Z]{2,}/, // ALL CAPS headers\n    /^ABOUT|^WORDS|^COMPREHENSION/i, // Common textbook sections\n    /^\\*/, // Bullet points\n    /^-/, // Dash points\n    /^[ivx]+\\./, // Roman numerals\n  ];\n\n  // Check if current line matches new paragraph patterns\n  for (const pattern of newParagraphPatterns) {\n    if (pattern.test(current)) {\n      return true;\n    }\n  }\n\n  // New paragraph if previous line ended with punctuation and current starts with capital\n  if (/[.!?]$/.test(previous) && /^[A-Z]/.test(current)) {\n    return true;\n  }\n\n  // Educational content: new paragraph for questions\n  if (/^[a-z]\\.\\s+[A-Z]/.test(current) || /^[A-Z]\\.\\s+[A-Z]/.test(current)) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Fix common capitalization issues\n */\nfunction fixCapitalization(text: string): string {\n  return text\n    // Capitalize first letter of sentences\n    .replace(/(^|[.!?]\\s+)([a-z])/g, (match, prefix, letter) => prefix + letter.toUpperCase())\n    // Fix common OCR mistakes with 'I'\n    .replace(/\\bi\\b/g, 'I')\n    // Fix 'i' at start of sentences\n    .replace(/(^|[.!?]\\s+)i\\b/g, '$1I');\n}\n\n/**\n * Final cleanup and formatting\n */\nfunction finalCleanup(text: string): string {\n  return text\n    // Remove extra whitespace\n    .trim()\n    // Ensure proper spacing after punctuation\n    .replace(/([.!?])([A-Z])/g, '$1 $2')\n    // Fix spacing around common punctuation\n    .replace(/\\s+([,.!?;:])/g, '$1')\n    .replace(/([.!?;:])\\s*([A-Z])/g, '$1 $2')\n    // Remove trailing spaces from lines\n    .replace(/[ \\t]+$/gm, '')\n    // Normalize line endings\n    .replace(/\\r\\n/g, '\\n')\n    .replace(/\\r/g, '\\n');\n}\n\n/**\n * Get a preview of text processing changes\n */\nexport function getProcessingPreview(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS) {\n  const processed = processOCRText(rawText, options);\n  \n  return {\n    original: rawText,\n    processed: processed,\n    changes: {\n      originalLength: rawText.length,\n      processedLength: processed.length,\n      originalLines: rawText.split('\\n').length,\n      processedLines: processed.split('\\n').length,\n      originalWords: rawText.split(/\\s+/).length,\n      processedWords: processed.split(/\\s+/).length\n    }\n  };\n}\n\n/**\n * Detect the type of content for specialized processing\n */\nexport function detectContentType(text: string): 'textbook' | 'math' | 'code' | 'table' | 'general' {\n  const mathPatterns = [\n    /\\d+\\s*[+\\-*/=]\\s*\\d+/,\n    /[xy]\\s*[=+\\-]/,\n    /\\b(equation|formula|solve|calculate)\\b/i\n  ];\n\n  const codePatterns = [\n    /function\\s+\\w+\\s*\\(/,\n    /\\bif\\s*\\(/,\n    /\\bfor\\s*\\(/,\n    /[{}();]/\n  ];\n\n  const tablePatterns = [\n    /\\|\\s*\\w+\\s*\\|/,\n    /\\t\\w+\\t/,\n    /^\\s*\\w+\\s+\\w+\\s+\\w+\\s*$/m\n  ];\n\n  if (mathPatterns.some(pattern => pattern.test(text))) {\n    return 'math';\n  }\n  \n  if (codePatterns.some(pattern => pattern.test(text))) {\n    return 'code';\n  }\n  \n  if (tablePatterns.some(pattern => pattern.test(text))) {\n    return 'table';\n  }\n\n  if (/chapter|section|exercise|problem/i.test(text)) {\n    return 'textbook';\n  }\n\n  return 'general';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAUM,MAAM,6BAAoD;IAC/D,eAAe;IACf,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;AACtB;AAKO,SAAS,eAAe,OAAe,EAAE,UAAiC,0BAA0B;IACzG,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;QAC3C,OAAO;IACT;IAEA,IAAI,gBAAgB;IAEpB,qDAAqD;IACrD,gBAAgB,mBAAmB;IAEnC,wBAAwB;IACxB,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,gBAAgB,kBAAkB;IACpC;IAEA,gDAAgD;IAChD,gBAAgB,qBAAqB;IAErC,yCAAyC;IACzC,IAAI,QAAQ,aAAa,EAAE;QACzB,gBAAgB,cAAc;IAChC;IAEA,kCAAkC;IAClC,IAAI,QAAQ,eAAe,EAAE;QAC3B,gBAAgB,sBAAsB;IACxC;IAEA,oCAAoC;IACpC,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,gBAAgB,kBAAkB;IACpC;IAEA,wBAAwB;IACxB,gBAAgB,aAAa;IAE7B,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,mBAAmB,IAAY;IACtC,OAAO,IACL,oDAAoD;KACnD,OAAO,CAAC,+DAA+D,IAAI,wCAAwC;KACnH,OAAO,CAAC,yHAAyH,IAAI,oDAAoD;IAC1L,+DAA+D;KAC9D,OAAO,CAAC,qBAAqB,GAC9B,4CAA4C;KAC3C,OAAO,CAAC,0BAA0B,GACnC,qCAAqC;KACpC,OAAO,CAAC,iBAAiB;AAC9B;AAEA;;CAEC,GACD,SAAS,qBAAqB,IAAY;IACxC,OAAO,IACL,oFAAoF;KACnF,OAAO,CAAC,+BAA+B,SACvC,OAAO,CAAC,+BAA+B,SACvC,OAAO,CAAC,6BAA6B,QACtC,oEAAoE;KACnE,OAAO,CAAC,wBAAwB,QACjC,sCAAsC;KACrC,OAAO,CAAC,iCAAiC,MAC1C,uDAAuD;KACtD,OAAO,CAAC,wCAAwC,CAAC,OAAO,KAAK,SAAW,MAAM,MAAM,OAAO,WAAW,GACvG,sCAAsC;KACrC,OAAO,CAAC,6CAA6C,QAAQ,KAAK,WAAW;AAClF;AAEA;;CAEC,GACD,SAAS,kBAAkB,IAAY;IACrC,OAAO,IACL,4CAA4C;KAC3C,OAAO,CAAC,WAAW,IACpB,8CAA8C;KAC7C,OAAO,CAAC,qBAAqB,GAC9B,oDAAoD;KACnD,OAAO,CAAC,WAAW;AACxB;AAEA;;CAEC,GACD,SAAS,cAAc,IAAY;IACjC,OAAO,IACL,0CAA0C;KACzC,OAAO,CAAC,wBAAwB,OACjC,oFAAoF;KACnF,OAAO,CAAC,4BAA4B,QACrC,6FAA6F;KAC5F,OAAO,CAAC,gCAAgC,QACzC,yEAAyE;KACxE,OAAO,CAAC,4CAA4C,SACrD,8CAA8C;KAC7C,OAAO,CAAC,uCAAuC,QAChD,uDAAuD;KACtD,OAAO,CAAC,6BAA6B,WACtC,+BAA+B;KAC9B,OAAO,CAAC,oCAAoC;AACjD;AAEA;;CAEC,GACD,SAAS,sBAAsB,IAAY;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,cAAwB,EAAE;IAChC,IAAI,mBAAmB;IAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAE1B,IAAI,SAAS,IAAI;YACf,qCAAqC;YACrC,IAAI,iBAAiB,IAAI,IAAI;gBAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;gBACtC,mBAAmB;YACrB;YACA,YAAY,IAAI,CAAC;QACnB,OAAO,IAAI,eAAe,MAAM,KAAK,CAAC,IAAI,EAAE,GAAG;YAC7C,yBAAyB;YACzB,IAAI,iBAAiB,IAAI,IAAI;gBAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;YACxC;YACA,mBAAmB;QACrB,OAAO;YACL,6BAA6B;YAC7B,IAAI,kBAAkB;gBACpB,oBAAoB,MAAM;YAC5B,OAAO;gBACL,mBAAmB;YACrB;QACF;IACF;IAEA,sBAAsB;IACtB,IAAI,iBAAiB,IAAI,IAAI;QAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;IACxC;IAEA,OAAO,YAAY,IAAI,CAAC;AAC1B;AAEA;;CAEC,GACD,SAAS,eAAe,WAAmB,EAAE,YAAqB;IAChE,IAAI,CAAC,eAAe,CAAC,cAAc,OAAO;IAE1C,MAAM,UAAU,YAAY,IAAI;IAChC,MAAM,WAAW,aAAa,IAAI;IAElC,2BAA2B;IAC3B,MAAM,uBAAuB;QAC3B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,uDAAuD;IACvD,KAAK,MAAM,WAAW,qBAAsB;QAC1C,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,wFAAwF;IACxF,IAAI,SAAS,IAAI,CAAC,aAAa,SAAS,IAAI,CAAC,UAAU;QACrD,OAAO;IACT;IAEA,mDAAmD;IACnD,IAAI,mBAAmB,IAAI,CAAC,YAAY,mBAAmB,IAAI,CAAC,UAAU;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,kBAAkB,IAAY;IACrC,OAAO,IACL,uCAAuC;KACtC,OAAO,CAAC,wBAAwB,CAAC,OAAO,QAAQ,SAAW,SAAS,OAAO,WAAW,GACvF,mCAAmC;KAClC,OAAO,CAAC,UAAU,IACnB,gCAAgC;KAC/B,OAAO,CAAC,oBAAoB;AACjC;AAEA;;CAEC,GACD,SAAS,aAAa,IAAY;IAChC,OAAO,IACL,0BAA0B;KACzB,IAAI,EACL,0CAA0C;KACzC,OAAO,CAAC,mBAAmB,QAC5B,wCAAwC;KACvC,OAAO,CAAC,kBAAkB,MAC1B,OAAO,CAAC,wBAAwB,QACjC,oCAAoC;KACnC,OAAO,CAAC,aAAa,GACtB,yBAAyB;KACxB,OAAO,CAAC,SAAS,MACjB,OAAO,CAAC,OAAO;AACpB;AAKO,SAAS,qBAAqB,OAAe,EAAE,UAAiC,0BAA0B;IAC/G,MAAM,YAAY,eAAe,SAAS;IAE1C,OAAO;QACL,UAAU;QACV,WAAW;QACX,SAAS;YACP,gBAAgB,QAAQ,MAAM;YAC9B,iBAAiB,UAAU,MAAM;YACjC,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM;YACzC,gBAAgB,UAAU,KAAK,CAAC,MAAM,MAAM;YAC5C,eAAe,QAAQ,KAAK,CAAC,OAAO,MAAM;YAC1C,gBAAgB,UAAU,KAAK,CAAC,OAAO,MAAM;QAC/C;IACF;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,gBAAgB;QACpB;QACA;QACA;KACD;IAED,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACpD,OAAO;IACT;IAEA,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACpD,OAAO;IACT;IAEA,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACrD,OAAO;IACT;IAEA,IAAI,oCAAoC,IAAI,CAAC,OAAO;QAClD,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Books', href: '/admin/books', icon: '📖' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/admin/test-processing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { processOCRText, getProcessingPreview, DEFAULT_PROCESSING_OPTIONS } from '@/lib/text-processing';\nimport AdminLayout from '@/components/AdminLayout';\n\nconst sampleProblematicText = `ル\nABOUT THE AUTHOR\n<PERSON><PERSON><PERSON> (1907-2002) was a Swedish writer. She is best known for her children's book\nseries. The Pippi Longstocking stories are about an unusual girl. <PERSON><PERSON><PERSON> is brave, funny and so\nincredibly strong that she can lift a horse up with one hand!\nWORDS TO KNOW\nangel a spiritual being that is believed to act as an attendant to god (<PERSON><PERSON><PERSON>'s mother died when\nshe was young)\nbraided plaited\ncannibal someone who eats other people!\ncourage bravery, the ability to do something that frightens you\nfreckles small patches of darker colour on skin (often made darker by the sun)\nremarkable someone or something worth noticing\nsidewalk a paved pathway at the side of a road\nstockings very long socks\nCOMPREHENSION\n1. Answer the following questions.\na. What was the name of <PERSON><PERSON><PERSON>'s house?\nb. What were <PERSON> and <PERSON><PERSON> thinking while standing by their gate?\nC.\nd.\nWhich three countries did <PERSON><PERSON><PERSON> say she has been to?\nWhat was unusual about how <PERSON><PERSON><PERSON> went on her morning walk?\ne.\nWhere were <PERSON><PERSON><PERSON>'s parents?\nMoney\nf.\nWhat did <PERSON><PERSON> say to <PERSON><PERSON><PERSON> about lying?\ng.\nWhat did <PERSON> realize while he was speaking to <PERSON><PERSON><PERSON>?\nh. What was the name of <PERSON>ppi's pet monkey?\ni. How do we know that the pet monkey was polite?\nThese questions are more difficult. Discuss them first.\nj. What details from the story show us that Pippi is a remarkable child?\nHow many lies do you think Pippi tells in this extract?\nk.`;\n\nexport default function TestProcessingPage() {\n  const [inputText, setInputText] = useState(sampleProblematicText);\n  const [processedText, setProcessedText] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n\n  const handleProcess = () => {\n    const processed = processOCRText(inputText, DEFAULT_PROCESSING_OPTIONS);\n    setProcessedText(processed);\n    setShowPreview(true);\n  };\n\n  const preview = showPreview ? getProcessingPreview(inputText, DEFAULT_PROCESSING_OPTIONS) : null;\n\n  return (\n    <AdminLayout>\n      <div className=\"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">OCR Text Processing Test</h1>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Input Section */}\n            <div className=\"space-y-4\">\n              <h2 className=\"text-xl font-semibold\">Original OCR Text (with problems)</h2>\n              <textarea\n                value={inputText}\n                onChange={(e) => setInputText(e.target.value)}\n                className=\"w-full h-96 p-3 border rounded font-mono text-sm text-gray-900 bg-white\"\n                placeholder=\"Paste problematic OCR text here...\"\n              />\n              <button\n                onClick={handleProcess}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                Process Text\n              </button>\n            </div>\n\n            {/* Output Section */}\n            <div className=\"space-y-4\">\n              <h2 className=\"text-xl font-semibold\">Processed Text (cleaned)</h2>\n              <div className=\"w-full h-96 p-3 border rounded bg-gray-50 overflow-y-auto\">\n                <pre className=\"whitespace-pre-wrap text-sm font-mono text-gray-900\">{processedText}</pre>\n              </div>\n              \n              {preview && (\n                <div className=\"bg-blue-50 p-4 rounded\">\n                  <h3 className=\"font-medium mb-2\">Processing Statistics</h3>\n                  <div className=\"text-sm space-y-1 text-gray-900\">\n                    <div>Words: {preview.changes.originalWords} → {preview.changes.processedWords}</div>\n                    <div>Lines: {preview.changes.originalLines} → {preview.changes.processedLines}</div>\n                    <div>Characters: {preview.changes.originalLength} → {preview.changes.processedLength}</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Improvements Section */}\n          <div className=\"mt-8 bg-green-50 p-6 rounded-lg\">\n            <h3 className=\"text-lg font-semibold mb-4\">What the Enhanced Processing Fixes:</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <h4 className=\"font-medium text-green-800\">OCR Artifacts</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Removes random characters like \"ル\"</li>\n                  <li>Cleans up non-Latin characters</li>\n                  <li>Removes isolated symbols</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Question Formatting</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Joins separated question numbers (C. + question)</li>\n                  <li>Fixes question lettering (a., b., c.)</li>\n                  <li>Proper spacing around numbers</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Text Continuity</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Merges broken sentences</li>\n                  <li>Fixes paragraph structure</li>\n                  <li>Handles hyphenated words</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Educational Content</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Recognizes textbook sections</li>\n                  <li>Preserves question formatting</li>\n                  <li>Handles numbered lists properly</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmC7B,CAAC;AAEY,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,gIAAA,CAAA,6BAA0B;QACtE,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,UAAU,cAAc,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,gIAAA,CAAA,6BAA0B,IAAI;IAE5F,qBACE,8OAAC,iIAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;oCAGvE,yBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAQ,QAAQ,OAAO,CAAC,aAAa;4DAAC;4DAAI,QAAQ,OAAO,CAAC,cAAc;;;;;;;kEAC7E,8OAAC;;4DAAI;4DAAQ,QAAQ,OAAO,CAAC,aAAa;4DAAC;4DAAI,QAAQ,OAAO,CAAC,cAAc;;;;;;;kEAC7E,8OAAC;;4DAAI;4DAAa,QAAQ,OAAO,CAAC,cAAc;4DAAC;4DAAI,QAAQ,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}]}