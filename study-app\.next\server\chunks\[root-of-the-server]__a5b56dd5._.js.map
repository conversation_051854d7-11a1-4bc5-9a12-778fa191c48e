{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport path from 'path';\nimport fs from 'fs';\n\n// Database file path\nconst dbPath = path.join(process.cwd(), 'data', 'study_app.db');\n\n// Ensure data directory exists\nconst dataDir = path.dirname(dbPath);\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Initialize database\nconst db = new Database(dbPath);\n\n// Enable foreign keys\ndb.pragma('foreign_keys = ON');\n\n// Database migration function\nexport function migrateDatabase() {\n  try {\n    // Check if new columns exist, if not add them\n    const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n    const hasPageType = tableInfo.some(col => col.name === 'page_type');\n    const hasUploadOrder = tableInfo.some(col => col.name === 'upload_order');\n\n    if (!hasPageType) {\n      db.exec(\"ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'\");\n      console.log('Added page_type column to images table');\n    }\n\n    if (!hasUploadOrder) {\n      db.exec(\"ALTER TABLE images ADD COLUMN upload_order INTEGER\");\n      // Set upload order for existing images based on uploaded_at\n      db.exec(`\n        UPDATE images\n        SET upload_order = (\n          SELECT COUNT(*) + 1\n          FROM images i2\n          WHERE i2.class_id = images.class_id\n          AND i2.subject_id = images.subject_id\n          AND i2.uploaded_at < images.uploaded_at\n        )\n      `);\n      console.log('Added upload_order column to images table');\n    }\n\n    // Check if processed column exists in ocr_text table\n    const ocrTableInfo = db.prepare(\"PRAGMA table_info(ocr_text)\").all() as any[];\n    const hasProcessed = ocrTableInfo.some(col => col.name === 'processed');\n\n    if (!hasProcessed) {\n      db.exec(\"ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1\");\n      console.log('Added processed column to ocr_text table');\n    }\n\n    // Check if books table exists\n    const tablesResult = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n    if (!tablesResult) {\n      console.log('Creating books table...');\n      db.exec(`\n        CREATE TABLE books (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          title TEXT NOT NULL,\n          class_id INTEGER NOT NULL,\n          subject_id INTEGER NOT NULL,\n          description TEXT,\n          cover_image_path TEXT,\n          total_pages INTEGER DEFAULT 0,\n          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n        )\n      `);\n      console.log('Books table created successfully');\n    }\n\n    // Check if images table has book_id column\n    const hasBookId = tableInfo.some(col => col.name === 'book_id');\n    if (!hasBookId) {\n      console.log('Adding book_id column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN book_id INTEGER\");\n      console.log('Added book_id column to images table');\n    }\n\n    // Check if images table has page_number column\n    const hasPageNumber = tableInfo.some(col => col.name === 'page_number');\n    if (!hasPageNumber) {\n      console.log('Adding page_number column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN page_number INTEGER\");\n      console.log('Added page_number column to images table');\n    }\n\n  } catch (error) {\n    console.error('Migration error:', error);\n  }\n}\n\n// Database schema initialization\nexport function initializeDatabase() {\n  // Users table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),\n      password_hash TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Classes table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS classes (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Subjects table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS subjects (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      UNIQUE(class_id, name)\n    )\n  `);\n\n  // Books table - containers for textbook content\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS books (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      description TEXT,\n      cover_image_path TEXT,\n      total_pages INTEGER DEFAULT 0,\n      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Images table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS images (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      file_path TEXT NOT NULL,\n      original_name TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      book_id INTEGER,\n      page_number INTEGER,\n      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),\n      upload_order INTEGER,\n      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE\n    )\n  `);\n\n  // OCR text table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS ocr_text (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      image_id INTEGER NOT NULL,\n      content TEXT NOT NULL,\n      processed BOOLEAN DEFAULT FALSE,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Questions table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapter TEXT,\n      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),\n      content TEXT NOT NULL,\n      options TEXT, -- JSON string for MCQ options\n      correct_answer TEXT,\n      marks INTEGER DEFAULT 1,\n      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Tests table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS tests (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapters TEXT, -- JSON string of selected chapters\n      time_min INTEGER NOT NULL, -- Time limit in minutes\n      total_marks INTEGER DEFAULT 0,\n      instructions TEXT,\n      is_active BOOLEAN DEFAULT TRUE,\n      created_by INTEGER NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (created_by) REFERENCES users(id)\n    )\n  `);\n\n  // Test questions junction table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      question_order INTEGER NOT NULL,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      UNIQUE(test_id, question_id),\n      UNIQUE(test_id, question_order)\n    )\n  `);\n\n  // Test results table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_results (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      user_id INTEGER NOT NULL,\n      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      submitted_at DATETIME,\n      total_score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      time_taken INTEGER, -- Time taken in minutes\n      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(test_id, user_id)\n    )\n  `);\n\n  // Test answers table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_answers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      result_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      user_answer TEXT,\n      is_correct BOOLEAN,\n      score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(result_id, question_id)\n    )\n  `);\n\n  // Create indexes for better performance\n  db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);\n    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);\n    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);\n    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);\n  `);\n\n  console.log('Database initialized successfully');\n\n  // Run migrations after initialization\n  migrateDatabase();\n}\n\n// Create default admin user if none exists\nexport function createDefaultAdmin() {\n  const bcrypt = require('bcryptjs');\n\n  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');\n\n  if (adminExists.count === 0) {\n    const hashedPassword = bcrypt.hashSync('admin123', 10);\n\n    db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);\n\n    console.log('Default admin user created: <EMAIL> / admin123');\n  }\n}\n\n// Clear OCR data to force re-processing with real OCR\nexport function clearOCRData() {\n  try {\n    console.log('Clearing existing OCR data to enable fresh processing...');\n    db.exec('DELETE FROM ocr_text');\n    db.exec('DELETE FROM questions');\n    console.log('OCR data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing OCR data:', error);\n  }\n}\n\n// Initialize database on import\ninitializeDatabase();\ncreateDefaultAdmin();\n\nexport default db;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,qBAAqB;AACrB,MAAM,SAAS,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAEhD,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,sBAAsB;AACtB,MAAM,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;AAExB,sBAAsB;AACtB,GAAG,MAAM,CAAC;AAGH,SAAS;IACd,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,6BAA6B,GAAG;QAC7D,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACvD,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE1D,IAAI,CAAC,aAAa;YAChB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,gBAAgB;YACnB,GAAG,IAAI,CAAC;YACR,4DAA4D;YAC5D,GAAG,IAAI,CAAC,CAAC;;;;;;;;;MAST,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,qDAAqD;QACrD,MAAM,eAAe,GAAG,OAAO,CAAC,+BAA+B,GAAG;QAClE,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE3D,IAAI,CAAC,cAAc;YACjB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,8BAA8B;QAC9B,MAAM,eAAe,GAAG,OAAO,CAAC,sEAAsE,GAAG;QACzG,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,2CAA2C;QAC3C,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACrD,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,+CAA+C;QAC/C,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACzD,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAGO,SAAS;IACd,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gBAAgB;IAChB,GAAG,IAAI,CAAC,CAAC;;;;;;;EAOT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gDAAgD;IAChD,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;EAeT,CAAC;IAED,eAAe;IACf,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;EAST,CAAC;IAED,kBAAkB;IAClB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,gCAAgC;IAChC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkBT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,wCAAwC;IACxC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,QAAQ,GAAG,CAAC;IAEZ,sCAAsC;IACtC;AACF;AAGO,SAAS;IACd,MAAM;IAEN,MAAM,cAAc,GAAG,OAAO,CAAC,sDAAsD,GAAG,CAAC;IAEzF,IAAI,YAAY,KAAK,KAAK,GAAG;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,YAAY;QAEnD,GAAG,OAAO,CAAC,CAAC;;;IAGZ,CAAC,EAAE,GAAG,CAAC,iBAAiB,sBAAsB,SAAS;QAEvD,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,SAAS;IACd,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAEA,gCAAgC;AAChC;AACA;uCAEe", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport db from './database';\n\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'\n);\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: 'admin' | 'student';\n  created_at: string;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  error?: string;\n}\n\n// Hash password\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\n// Verify password\nexport function verifyPassword(password: string, hash: string): boolean {\n  return bcrypt.compareSync(password, hash);\n}\n\n// Create JWT token\nexport async function createToken(user: User): Promise<string> {\n  return await new SignJWT({ \n    userId: user.id, \n    email: user.email, \n    role: user.role \n  })\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('24h')\n    .sign(JWT_SECRET);\n}\n\n// Verify JWT token\nexport async function verifyToken(token: string): Promise<any> {\n  try {\n    const { payload } = await jwtVerify(token, JWT_SECRET);\n    return payload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Login user\nexport async function loginUser(email: string, password: string): Promise<AuthResult> {\n  try {\n    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email) as any;\n    \n    if (!user) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    if (!verifyPassword(password, user.password_hash)) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    const userWithoutPassword = {\n      id: user.id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      created_at: user.created_at\n    };\n\n    return { success: true, user: userWithoutPassword };\n  } catch (error) {\n    console.error('Login error:', error);\n    return { success: false, error: 'Login failed' };\n  }\n}\n\n// Get current user from cookies\nexport async function getCurrentUser(): Promise<User | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get('auth-token')?.value;\n    \n    if (!token) {\n      return null;\n    }\n\n    const payload = await verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    const user = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(payload.userId) as User;\n\n    return user || null;\n  } catch (error) {\n    console.error('Get current user error:', error);\n    return null;\n  }\n}\n\n// Set auth cookie\nexport async function setAuthCookie(user: User) {\n  const token = await createToken(user);\n  const cookieStore = await cookies();\n  \n  cookieStore.set('auth-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 // 24 hours\n  });\n}\n\n// Clear auth cookie\nexport async function clearAuthCookie() {\n  const cookieStore = await cookies();\n  cookieStore.delete('auth-token');\n}\n\n// Register new user (admin only)\nexport function registerUser(name: string, email: string, password: string, role: 'admin' | 'student'): AuthResult {\n  try {\n    // Check if user already exists\n    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);\n    if (existingUser) {\n      return { success: false, error: 'User with this email already exists' };\n    }\n\n    const hashedPassword = hashPassword(password);\n    \n    const result = db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run(name, email, role, hashedPassword);\n\n    const newUser = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(result.lastInsertRowid) as User;\n\n    return { success: true, user: newUser };\n  } catch (error) {\n    console.error('Registration error:', error);\n    return { success: false, error: 'Registration failed' };\n  }\n}\n\n// Middleware to check if user is authenticated\nexport async function requireAuth(): Promise<User | null> {\n  const user = await getCurrentUser();\n  return user;\n}\n\n// Middleware to check if user is admin\nexport async function requireAdmin(): Promise<User | null> {\n  const user = await getCurrentUser();\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return user;\n}\n\n// Get all users (admin only)\nexport function getAllUsers(): User[] {\n  return db.prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all() as User[];\n}\n\n// Update user\nexport function updateUser(id: number, updates: Partial<Pick<User, 'name' | 'email' | 'role'>>): boolean {\n  try {\n    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');\n    const values = Object.values(updates);\n    \n    db.prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)\n      .run(...values, id);\n    \n    return true;\n  } catch (error) {\n    console.error('Update user error:', error);\n    return false;\n  }\n}\n\n// Delete user\nexport function deleteUser(id: number): boolean {\n  try {\n    db.prepare('DELETE FROM users WHERE id = ?').run(id);\n    return true;\n  } catch (error) {\n    console.error('Delete user error:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,UAAU,IAAI;AAkBrB,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAGO,SAAS,eAAe,QAAgB,EAAE,IAAY;IAC3D,OAAO,mIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAGO,eAAe,YAAY,IAAU;IAC1C,OAAO,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC;QACvB,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB,GACG,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,OAClB,IAAI,CAAC;AACV;AAGO,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,UAAU,KAAa,EAAE,QAAgB;IAC7D,IAAI;QACF,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG,CAAC;QAEnE,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,IAAI,CAAC,eAAe,UAAU,KAAK,aAAa,GAAG;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,MAAM,sBAAsB;YAC1B,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAoB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,eAAe;QAE7C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,YAAY;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACrB,GAAG,CAAC,QAAQ,MAAM;QAErB,OAAO,QAAQ;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,eAAe,cAAc,IAAU;IAC5C,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,YAAY,GAAG,CAAC,cAAc,OAAO;QACnC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,KAAK,KAAK,GAAG,WAAW;IAClC;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAGO,SAAS,aAAa,IAAY,EAAE,KAAa,EAAE,QAAgB,EAAE,IAAyB;IACnG,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,wCAAwC,GAAG,CAAC;QAC5E,IAAI,cAAc;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,aAAa;QAEpC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,OAAO,MAAM;QAE1B,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACxB,GAAG,CAAC,OAAO,eAAe;QAE7B,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;AACF;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gFAAgF,GAAG;AACvG;AAGO,SAAS,WAAW,EAAU,EAAE,OAAuD;IAC5F,IAAI;QACF,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC;QACrE,MAAM,SAAS,OAAO,MAAM,CAAC;QAE7B,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,6CAA6C,CAAC,EACpF,GAAG,IAAI,QAAQ;QAElB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,IAAI;QACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/models.ts"], "sourcesContent": ["import db from './database';\n\n// Types\nexport interface Class {\n  id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n}\n\nexport interface Subject {\n  id: number;\n  class_id: number;\n  name: string;\n  description?: string;\n  created_at: string;\n  class_name?: string;\n}\n\nexport interface Book {\n  id: number;\n  title: string;\n  class_id: number;\n  subject_id: number;\n  description?: string;\n  cover_image_path?: string;\n  total_pages: number;\n  status: 'draft' | 'processing' | 'completed';\n  created_at: string;\n  updated_at: string;\n  // Joined fields\n  class_name?: string;\n  subject_name?: string;\n}\n\nexport interface BookWithStats extends Book {\n  uploaded_pages: number;\n  processed_pages: number;\n  total_questions: number;\n}\n\nexport interface Question {\n  id: number;\n  class_id: number;\n  subject_id: number;\n  chapter?: string;\n  type: 'mcq' | 'true_false' | 'fill_blank' | 'short_answer' | 'long_answer';\n  content: string;\n  options?: string; // JSON string\n  correct_answer?: string;\n  marks: number;\n  difficulty?: 'easy' | 'medium' | 'hard';\n  created_at: string;\n  updated_at: string;\n  class_name?: string;\n  subject_name?: string;\n}\n\nexport interface Test {\n  id: number;\n  title: string;\n  class_id: number;\n  subject_id: number;\n  chapters?: string; // JSON string\n  time_min: number;\n  total_marks: number;\n  instructions?: string;\n  is_active: boolean;\n  created_by: number;\n  created_at: string;\n  class_name?: string;\n  subject_name?: string;\n  creator_name?: string;\n}\n\n// Classes CRUD operations\nexport const ClassModel = {\n  getAll(): Class[] {\n    return db.prepare('SELECT * FROM classes ORDER BY name').all() as Class[];\n  },\n\n  getById(id: number): Class | null {\n    return db.prepare('SELECT * FROM classes WHERE id = ?').get(id) as Class || null;\n  },\n\n  create(name: string, description?: string): Class {\n    const result = db.prepare(`\n      INSERT INTO classes (name, description)\n      VALUES (?, ?)\n    `).run(name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE classes \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update class error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM classes WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete class error:', error);\n      return false;\n    }\n  }\n};\n\n// Subjects CRUD operations\nexport const SubjectModel = {\n  getAll(): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      ORDER BY c.name, s.name\n    `).all() as Subject[];\n  },\n\n  getByClassId(classId: number): Subject[] {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.class_id = ?\n      ORDER BY s.name\n    `).all(classId) as Subject[];\n  },\n\n  getById(id: number): Subject | null {\n    return db.prepare(`\n      SELECT s.*, c.name as class_name\n      FROM subjects s\n      JOIN classes c ON s.class_id = c.id\n      WHERE s.id = ?\n    `).get(id) as Subject || null;\n  },\n\n  create(classId: number, name: string, description?: string): Subject {\n    const result = db.prepare(`\n      INSERT INTO subjects (class_id, name, description)\n      VALUES (?, ?, ?)\n    `).run(classId, name, description || null);\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, name: string, description?: string): boolean {\n    try {\n      db.prepare(`\n        UPDATE subjects \n        SET name = ?, description = ?\n        WHERE id = ?\n      `).run(name, description || null, id);\n      return true;\n    } catch (error) {\n      console.error('Update subject error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM subjects WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete subject error:', error);\n      return false;\n    }\n  }\n};\n\n// Questions CRUD operations\nexport const QuestionModel = {\n  getAll(filters?: { classId?: number; subjectId?: number; type?: string; chapter?: string }): Question[] {\n    let query = `\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n    `;\n    \n    const conditions: string[] = [];\n    const params: any[] = [];\n    \n    if (filters?.classId) {\n      conditions.push('q.class_id = ?');\n      params.push(filters.classId);\n    }\n    \n    if (filters?.subjectId) {\n      conditions.push('q.subject_id = ?');\n      params.push(filters.subjectId);\n    }\n    \n    if (filters?.type) {\n      conditions.push('q.type = ?');\n      params.push(filters.type);\n    }\n    \n    if (filters?.chapter) {\n      conditions.push('q.chapter = ?');\n      params.push(filters.chapter);\n    }\n    \n    if (conditions.length > 0) {\n      query += ' WHERE ' + conditions.join(' AND ');\n    }\n    \n    query += ' ORDER BY q.created_at DESC';\n    \n    return db.prepare(query).all(...params) as Question[];\n  },\n\n  getById(id: number): Question | null {\n    return db.prepare(`\n      SELECT q.*, c.name as class_name, s.name as subject_name\n      FROM questions q\n      JOIN classes c ON q.class_id = c.id\n      JOIN subjects s ON q.subject_id = s.id\n      WHERE q.id = ?\n    `).get(id) as Question || null;\n  },\n\n  create(questionData: Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>): Question {\n    const result = db.prepare(`\n      INSERT INTO questions (\n        class_id, subject_id, chapter, type, content, options, \n        correct_answer, marks, difficulty\n      )\n      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)\n    `).run(\n      questionData.class_id,\n      questionData.subject_id,\n      questionData.chapter || null,\n      questionData.type,\n      questionData.content,\n      questionData.options || null,\n      questionData.correct_answer || null,\n      questionData.marks,\n      questionData.difficulty || null\n    );\n\n    return this.getById(result.lastInsertRowid as number)!;\n  },\n\n  update(id: number, questionData: Partial<Omit<Question, 'id' | 'created_at' | 'updated_at' | 'class_name' | 'subject_name'>>): boolean {\n    try {\n      const fields = Object.keys(questionData).filter(key => questionData[key as keyof typeof questionData] !== undefined);\n      const setClause = fields.map(field => `${field} = ?`).join(', ');\n      const values = fields.map(field => questionData[field as keyof typeof questionData]);\n      \n      db.prepare(`\n        UPDATE questions \n        SET ${setClause}, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ?\n      `).run(...values, id);\n      \n      return true;\n    } catch (error) {\n      console.error('Update question error:', error);\n      return false;\n    }\n  },\n\n  delete(id: number): boolean {\n    try {\n      db.prepare('DELETE FROM questions WHERE id = ?').run(id);\n      return true;\n    } catch (error) {\n      console.error('Delete question error:', error);\n      return false;\n    }\n  },\n\n  getChapters(classId?: number, subjectId?: number): string[] {\n    let query = 'SELECT DISTINCT chapter FROM questions WHERE chapter IS NOT NULL';\n    const params: any[] = [];\n\n    if (classId) {\n      query += ' AND class_id = ?';\n      params.push(classId);\n    }\n\n    if (subjectId) {\n      query += ' AND subject_id = ?';\n      params.push(subjectId);\n    }\n\n    query += ' ORDER BY chapter';\n\n    const results = db.prepare(query).all(...params) as { chapter: string }[];\n    return results.map(r => r.chapter);\n  },\n\n  // Get available chapters from page types\n  getChaptersFromPageTypes(classId?: number, subjectId?: number): string[] {\n    let query = `\n      SELECT DISTINCT page_type\n      FROM images\n      WHERE page_type LIKE 'chapter-%'\n    `;\n    const params: any[] = [];\n\n    if (classId) {\n      query += ' AND class_id = ?';\n      params.push(classId);\n    }\n\n    if (subjectId) {\n      query += ' AND subject_id = ?';\n      params.push(subjectId);\n    }\n\n    query += ' ORDER BY page_type';\n\n    const results = db.prepare(query).all(...params) as { page_type: string }[];\n    return results.map(r => r.page_type.replace('chapter-', 'Chapter '));\n  }\n};\n\n// Book Model\nexport const BookModel = {\n  getAll(): BookWithStats[] {\n    try {\n      // Check if books table exists\n      const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n      if (!tableExists) {\n        console.log('Books table does not exist yet, returning empty array');\n        return [];\n      }\n\n      const books = db.prepare(`\n        SELECT\n          b.*,\n          c.name as class_name,\n          s.name as subject_name,\n          COUNT(DISTINCT i.id) as uploaded_pages,\n          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,\n          0 as total_questions\n        FROM books b\n        LEFT JOIN classes c ON b.class_id = c.id\n        LEFT JOIN subjects s ON b.subject_id = s.id\n        LEFT JOIN images i ON b.id = i.book_id\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        GROUP BY b.id\n        ORDER BY b.updated_at DESC\n      `).all() as BookWithStats[];\n\n      return books;\n    } catch (error) {\n      console.error('Error fetching books:', error);\n      return [];\n    }\n  },\n\n  getById(id: number): BookWithStats | null {\n    try {\n      // Check if books table exists\n      const tableExists = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n      if (!tableExists) {\n        return null;\n      }\n\n      const book = db.prepare(`\n        SELECT\n          b.*,\n          c.name as class_name,\n          s.name as subject_name,\n          COUNT(DISTINCT i.id) as uploaded_pages,\n          COUNT(DISTINCT CASE WHEN ot.processed = 1 THEN i.id END) as processed_pages,\n          0 as total_questions\n        FROM books b\n        LEFT JOIN classes c ON b.class_id = c.id\n        LEFT JOIN subjects s ON b.subject_id = s.id\n        LEFT JOIN images i ON b.id = i.book_id\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        WHERE b.id = ?\n        GROUP BY b.id\n      `).get(id) as BookWithStats | undefined;\n\n      return book || null;\n    } catch (error) {\n      console.error('Error fetching book by id:', error);\n      return null;\n    }\n  },\n\n  create(data: {\n    title: string;\n    class_id: number;\n    subject_id: number;\n    description?: string;\n  }): number {\n    const result = db.prepare(`\n      INSERT INTO books (title, class_id, subject_id, description)\n      VALUES (?, ?, ?, ?)\n    `).run(data.title, data.class_id, data.subject_id, data.description || null);\n\n    return result.lastInsertRowid as number;\n  },\n\n  update(id: number, data: Partial<Book>): boolean {\n    const fields = [];\n    const values = [];\n\n    if (data.title !== undefined) {\n      fields.push('title = ?');\n      values.push(data.title);\n    }\n    if (data.description !== undefined) {\n      fields.push('description = ?');\n      values.push(data.description);\n    }\n    if (data.status !== undefined) {\n      fields.push('status = ?');\n      values.push(data.status);\n    }\n\n    if (fields.length === 0) return false;\n\n    fields.push('updated_at = CURRENT_TIMESTAMP');\n    values.push(id);\n\n    const result = db.prepare(`\n      UPDATE books SET ${fields.join(', ')} WHERE id = ?\n    `).run(...values);\n\n    return result.changes > 0;\n  },\n\n  delete(id: number): boolean {\n    const result = db.prepare('DELETE FROM books WHERE id = ?').run(id);\n    return result.changes > 0;\n  },\n\n  getImages(bookId: number) {\n    try {\n      // Check if book_id column exists in images table\n      const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n      const hasBookId = tableInfo.some(col => col.name === 'book_id');\n\n      if (!hasBookId) {\n        // If book_id column doesn't exist yet, return empty array\n        return [];\n      }\n\n      return db.prepare(`\n        SELECT\n          i.*,\n          CASE WHEN ot.id IS NOT NULL THEN 1 ELSE 0 END as has_ocr,\n          CASE WHEN ot.processed = 1 THEN 1 ELSE 0 END as ocr_processed\n        FROM images i\n        LEFT JOIN ocr_text ot ON i.id = ot.image_id\n        WHERE i.book_id = ?\n        ORDER BY i.upload_order ASC, i.id ASC\n      `).all(bookId);\n    } catch (error) {\n      console.error('Error fetching book images:', error);\n      return [];\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AA4EO,MAAM,aAAa;IACxB;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG;IAC9D;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC,OAAgB;IAC9E;IAEA,QAAO,IAAY,EAAE,WAAoB;QACvC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oCAAoC,GAAG,CAAC;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;QACT;IACF;AACF;AAGO,MAAM,eAAe;IAC1B;QACE,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG;IACR;IAEA,cAAa,OAAe;QAC1B,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAKnB,CAAC,EAAE,GAAG,CAAC,OAAkB;IAC3B;IAEA,QAAO,OAAe,EAAE,IAAY,EAAE,WAAoB;QACxD,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,SAAS,MAAM,eAAe;QAErC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,IAAY,EAAE,WAAoB;QACnD,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;MAIZ,CAAC,EAAE,GAAG,CAAC,MAAM,eAAe,MAAM;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,qCAAqC,GAAG,CAAC;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO;QACT;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAO,OAAmF;QACxF,IAAI,QAAQ,CAAC;;;;;IAKb,CAAC;QAED,MAAM,aAAuB,EAAE;QAC/B,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,SAAS,WAAW;YACtB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,SAAS;QAC/B;QAEA,IAAI,SAAS,MAAM;YACjB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,IAAI;QAC1B;QAEA,IAAI,SAAS,SAAS;YACpB,WAAW,IAAI,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,OAAO;QAC7B;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,SAAS,YAAY,WAAW,IAAI,CAAC;QACvC;QAEA,SAAS;QAET,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;IAClC;IAEA,SAAQ,EAAU;QAChB,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAMnB,CAAC,EAAE,GAAG,CAAC,OAAmB;IAC5B;IAEA,QAAO,YAAgG;QACrG,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;IAM3B,CAAC,EAAE,GAAG,CACJ,aAAa,QAAQ,EACrB,aAAa,UAAU,EACvB,aAAa,OAAO,IAAI,MACxB,aAAa,IAAI,EACjB,aAAa,OAAO,EACpB,aAAa,OAAO,IAAI,MACxB,aAAa,cAAc,IAAI,MAC/B,aAAa,KAAK,EAClB,aAAa,UAAU,IAAI;QAG7B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,eAAe;IAC5C;IAEA,QAAO,EAAU,EAAE,YAAyG;QAC1H,IAAI;YACF,MAAM,SAAS,OAAO,IAAI,CAAC,cAAc,MAAM,CAAC,CAAA,MAAO,YAAY,CAAC,IAAiC,KAAK;YAC1G,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,QAAS,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;YAC3D,MAAM,SAAS,OAAO,GAAG,CAAC,CAAA,QAAS,YAAY,CAAC,MAAmC;YAEnF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;YAEN,EAAE,UAAU;;MAElB,CAAC,EAAE,GAAG,IAAI,QAAQ;YAElB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,QAAO,EAAU;QACf,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sCAAsC,GAAG,CAAC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,aAAY,OAAgB,EAAE,SAAkB;QAC9C,IAAI,QAAQ;QACZ,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS;YACX,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,WAAW;YACb,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QAET,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;QACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;IACnC;IAEA,yCAAyC;IACzC,0BAAyB,OAAgB,EAAE,SAAkB;QAC3D,IAAI,QAAQ,CAAC;;;;IAIb,CAAC;QACD,MAAM,SAAgB,EAAE;QAExB,IAAI,SAAS;YACX,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,WAAW;YACb,SAAS;YACT,OAAO,IAAI,CAAC;QACd;QAEA,SAAS;QAET,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;QACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,OAAO,CAAC,YAAY;IAC1D;AACF;AAGO,MAAM,YAAY;IACvB;QACE,IAAI;YACF,8BAA8B;YAC9B,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sEAAsE,GAAG;YACxG,IAAI,CAAC,aAAa;gBAChB,QAAQ,GAAG,CAAC;gBACZ,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;MAe1B,CAAC,EAAE,GAAG;YAEN,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,EAAE;QACX;IACF;IAEA,SAAQ,EAAU;QAChB,IAAI;YACF,8BAA8B;YAC9B,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,sEAAsE,GAAG;YACxG,IAAI,CAAC,aAAa;gBAChB,OAAO;YACT;YAEA,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;MAezB,CAAC,EAAE,GAAG,CAAC;YAEP,OAAO,QAAQ;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,QAAO,IAKN;QACC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,QAAQ,EAAE,KAAK,UAAU,EAAE,KAAK,WAAW,IAAI;QAEvE,OAAO,OAAO,eAAe;IAC/B;IAEA,QAAO,EAAU,EAAE,IAAmB;QACpC,MAAM,SAAS,EAAE;QACjB,MAAM,SAAS,EAAE;QAEjB,IAAI,KAAK,KAAK,KAAK,WAAW;YAC5B,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,KAAK;QACxB;QACA,IAAI,KAAK,WAAW,KAAK,WAAW;YAClC,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,WAAW;QAC9B;QACA,IAAI,KAAK,MAAM,KAAK,WAAW;YAC7B,OAAO,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,KAAK,MAAM;QACzB;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;QAEhC,OAAO,IAAI,CAAC;QACZ,OAAO,IAAI,CAAC;QAEZ,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;uBACR,EAAE,OAAO,IAAI,CAAC,MAAM;IACvC,CAAC,EAAE,GAAG,IAAI;QAEV,OAAO,OAAO,OAAO,GAAG;IAC1B;IAEA,QAAO,EAAU;QACf,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QAChE,OAAO,OAAO,OAAO,GAAG;IAC1B;IAEA,WAAU,MAAc;QACtB,IAAI;YACF,iDAAiD;YACjD,MAAM,YAAY,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6BAA6B,GAAG;YAC7D,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;YAErD,IAAI,CAAC,WAAW;gBACd,0DAA0D;gBAC1D,OAAO,EAAE;YACX;YAEA,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;MASnB,CAAC,EAAE,GAAG,CAAC;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/vision-ocr.ts"], "sourcesContent": ["import { ImageAnnotatorClient } from '@google-cloud/vision';\nimport fs from 'fs';\n\n// Initialize the Google Vision client\n// Note: You'll need to set up authentication via environment variables or service account key\nlet visionClient: ImageAnnotatorClient | null = null;\n\nfunction getVisionClient(): ImageAnnotatorClient {\n  if (!visionClient) {\n    try {\n      // Initialize with default credentials (from environment)\n      visionClient = new ImageAnnotatorClient();\n    } catch (error) {\n      console.error('Failed to initialize Google Vision client:', error);\n      throw new Error('Google Vision API not configured. Please set up authentication.');\n    }\n  }\n  return visionClient;\n}\n\nexport interface OCRResult {\n  text: string;\n  confidence: number;\n  success: boolean;\n  error?: string;\n}\n\n/**\n * Extract text from an image using Google Vision API\n * @param imagePath - Path to the image file\n * @returns OCR result with extracted text and confidence\n */\nexport async function extractTextFromImage(imagePath: string): Promise<OCRResult> {\n  try {\n    // Check if file exists\n    if (!fs.existsSync(imagePath)) {\n      return {\n        text: '',\n        confidence: 0,\n        success: false,\n        error: 'Image file not found'\n      };\n    }\n\n    console.log(`Starting Google Vision OCR for: ${imagePath}`);\n\n    const client = getVisionClient();\n\n    // Read the image file\n    const imageBuffer = fs.readFileSync(imagePath);\n\n    // Perform text detection\n    const [result] = await client.textDetection({\n      image: {\n        content: imageBuffer\n      }\n    });\n\n    const detections = result.textAnnotations;\n\n    if (!detections || detections.length === 0) {\n      console.log('No text detected in image');\n      return {\n        text: '',\n        confidence: 0,\n        success: true,\n        error: 'No text found in image'\n      };\n    }\n\n    // The first annotation contains the full text\n    const fullText = detections[0].description || '';\n    \n    // Calculate average confidence from all detections\n    let totalConfidence = 0;\n    let confidenceCount = 0;\n    \n    detections.forEach(detection => {\n      if (detection.confidence !== undefined && detection.confidence !== null) {\n        totalConfidence += detection.confidence;\n        confidenceCount++;\n      }\n    });\n\n    const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0.9; // Default high confidence\n\n    console.log(`Google Vision OCR completed. Text length: ${fullText.length}, Confidence: ${(averageConfidence * 100).toFixed(1)}%`);\n\n    return {\n      text: fullText.trim(),\n      confidence: averageConfidence,\n      success: true\n    };\n\n  } catch (error) {\n    console.error('Google Vision OCR error:', error);\n    \n    return {\n      text: '',\n      confidence: 0,\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown OCR error'\n    };\n  }\n}\n\n/**\n * Check if Google Vision API is properly configured\n * @returns true if API is available, false otherwise\n */\nexport async function isVisionAPIAvailable(): Promise<boolean> {\n  try {\n    const client = getVisionClient();\n    \n    // Try a simple operation to test connectivity\n    // We'll use a minimal request to check if the API is accessible\n    await client.getProjectId();\n    return true;\n  } catch (error) {\n    console.log('Google Vision API not available:', error);\n    return false;\n  }\n}\n\n/**\n * Get usage information for Google Vision API\n * @returns Information about API usage and limits\n */\nexport function getVisionAPIInfo() {\n  return {\n    provider: 'Google Cloud Vision API',\n    features: [\n      'High-accuracy OCR',\n      'Handwriting recognition',\n      'Multi-language support',\n      'Document structure detection'\n    ],\n    pricing: {\n      free_tier: '1,000 requests per month',\n      paid_tier: '$1.50 per 1,000 requests (after free tier)',\n      currency: 'USD'\n    },\n    setup_required: 'Google Cloud project with Vision API enabled and authentication configured'\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,sCAAsC;AACtC,8FAA8F;AAC9F,IAAI,eAA4C;AAEhD,SAAS;IACP,IAAI,CAAC,cAAc;QACjB,IAAI;YACF,yDAAyD;YACzD,eAAe,IAAI,sKAAA,CAAA,uBAAoB;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM,IAAI,MAAM;QAClB;IACF;IACA,OAAO;AACT;AAcO,eAAe,qBAAqB,SAAiB;IAC1D,IAAI;QACF,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;YAC7B,OAAO;gBACL,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,OAAO;YACT;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,WAAW;QAE1D,MAAM,SAAS;QAEf,sBAAsB;QACtB,MAAM,cAAc,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC;QAEpC,yBAAyB;QACzB,MAAM,CAAC,OAAO,GAAG,MAAM,OAAO,aAAa,CAAC;YAC1C,OAAO;gBACL,SAAS;YACX;QACF;QAEA,MAAM,aAAa,OAAO,eAAe;QAEzC,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;YAC1C,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM;gBACN,YAAY;gBACZ,SAAS;gBACT,OAAO;YACT;QACF;QAEA,8CAA8C;QAC9C,MAAM,WAAW,UAAU,CAAC,EAAE,CAAC,WAAW,IAAI;QAE9C,mDAAmD;QACnD,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QAEtB,WAAW,OAAO,CAAC,CAAA;YACjB,IAAI,UAAU,UAAU,KAAK,aAAa,UAAU,UAAU,KAAK,MAAM;gBACvE,mBAAmB,UAAU,UAAU;gBACvC;YACF;QACF;QAEA,MAAM,oBAAoB,kBAAkB,IAAI,kBAAkB,kBAAkB,KAAK,0BAA0B;QAEnH,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,SAAS,MAAM,CAAC,cAAc,EAAE,CAAC,oBAAoB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEhI,OAAO;YACL,MAAM,SAAS,IAAI;YACnB,YAAY;YACZ,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,OAAO;YACL,MAAM;YACN,YAAY;YACZ,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS;QAEf,8CAA8C;QAC9C,gEAAgE;QAChE,MAAM,OAAO,YAAY;QACzB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;IACT;AACF;AAMO,SAAS;IACd,OAAO;QACL,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,SAAS;YACP,WAAW;YACX,WAAW;YACX,UAAU;QACZ;QACA,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/text-processing.ts"], "sourcesContent": ["/**\n * Text post-processing utilities for OCR output\n * Cleans up and formats extracted text for better readability\n */\n\nexport interface TextProcessingOptions {\n  fixLineBreaks: boolean;\n  mergeParagraphs: boolean;\n  removeExtraSpaces: boolean;\n  fixCapitalization: boolean;\n  preserveFormatting: boolean;\n}\n\nexport const DEFAULT_PROCESSING_OPTIONS: TextProcessingOptions = {\n  fixLineBreaks: true,\n  mergeParagraphs: true,\n  removeExtraSpaces: true,\n  fixCapitalization: true,\n  preserveFormatting: false\n};\n\n/**\n * Main text processing function\n */\nexport function processOCRText(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS): string {\n  if (!rawText || rawText.trim().length === 0) {\n    return rawText;\n  }\n\n  let processedText = rawText;\n\n  // Step 1: Remove OCR artifacts and random characters\n  processedText = removeOCRArtifacts(processedText);\n\n  // Step 2: Basic cleanup\n  if (options.removeExtraSpaces) {\n    processedText = removeExtraSpaces(processedText);\n  }\n\n  // Step 3: Fix question numbering and formatting\n  processedText = fixQuestionNumbering(processedText);\n\n  // Step 4: Fix line breaks and continuity\n  if (options.fixLineBreaks) {\n    processedText = fixLineBreaks(processedText);\n  }\n\n  // Step 5: Merge broken paragraphs\n  if (options.mergeParagraphs) {\n    processedText = mergeBrokenParagraphs(processedText);\n  }\n\n  // Step 6: Fix capitalization issues\n  if (options.fixCapitalization) {\n    processedText = fixCapitalization(processedText);\n  }\n\n  // Step 7: Final cleanup\n  processedText = finalCleanup(processedText);\n\n  return processedText;\n}\n\n/**\n * Remove OCR artifacts and random characters\n */\nfunction removeOCRArtifacts(text: string): string {\n  return text\n    // Remove common OCR artifacts and random characters\n    .replace(/^[ル\\u3000-\\u303F\\u3040-\\u309F\\u30A0-\\u30FF\\uFF00-\\uFFEF]+/gm, '') // Japanese characters at start of lines\n    .replace(/[^\\x00-\\x7F\\u00A0-\\u024F\\u1E00-\\u1EFF\\u2000-\\u206F\\u2070-\\u209F\\u20A0-\\u20CF\\u2100-\\u214F\\u2190-\\u21FF\\u2200-\\u22FF]/g, '') // Remove non-Latin characters except common symbols\n    // Remove isolated single characters that are likely OCR errors\n    .replace(/^\\s*[^\\w\\s]\\s*$/gm, '')\n    // Remove lines with only symbols or numbers\n    .replace(/^\\s*[^\\w\\s]{1,3}\\s*$/gm, '')\n    // Clean up any resulting empty lines\n    .replace(/\\n\\s*\\n\\s*\\n/g, '\\n\\n');\n}\n\n/**\n * Fix question numbering and formatting\n */\nfunction fixQuestionNumbering(text: string): string {\n  return text\n    // Fix separated question numbers/letters (e.g., \"a.\" on one line, question on next)\n    .replace(/^([a-z]\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    .replace(/^([A-Z]\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    .replace(/^(\\d+\\.)\\s*\\n\\s*([A-Z])/gm, '$1 $2')\n    // Fix numbered questions with letters (e.g., \"1. a.\" becomes \"1a.\")\n    .replace(/(\\d+)\\.\\s+([a-z])\\./g, '$1$2.')\n    // Fix spacing around question numbers\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s+/gm, '$1 ')\n    // Fix questions that start with lowercase after number\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s+([a-z])/gm, (match, num, letter) => num + ' ' + letter.toUpperCase())\n    // Join question parts that were split\n    .replace(/^([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s*\\n\\s*([a-z])/gm, '$1 ' + '$2'.toUpperCase());\n}\n\n/**\n * Remove extra spaces and normalize whitespace\n */\nfunction removeExtraSpaces(text: string): string {\n  return text\n    // Replace multiple spaces with single space\n    .replace(/[ \\t]+/g, ' ')\n    // Remove spaces at beginning and end of lines\n    .replace(/^[ \\t]+|[ \\t]+$/gm, '')\n    // Remove multiple consecutive newlines (keep max 2)\n    .replace(/\\n{3,}/g, '\\n\\n');\n}\n\n/**\n * Fix line breaks and word continuity\n */\nfunction fixLineBreaks(text: string): string {\n  return text\n    // Fix hyphenated words split across lines\n    .replace(/(\\w+)-\\s*\\n\\s*(\\w+)/g, '$1$2')\n    // Join lines that end with lowercase and start with lowercase (likely continuation)\n    .replace(/([a-z,])\\s*\\n\\s*([a-z])/g, '$1 $2')\n    // Join lines where previous line doesn't end with punctuation and next starts with lowercase\n    .replace(/([^.!?:;\\n])\\s*\\n\\s*([a-z])/g, '$1 $2')\n    // Handle educational content - join lines that are clearly continuations\n    .replace(/([a-z])\\s*\\n\\s*([a-z][^.!?]*[a-z])\\s*\\n/g, '$1 $2 ')\n    // Fix broken sentences in educational content\n    .replace(/([a-z])\\s*\\n\\s*([a-z][^A-Z]*[.!?])/g, '$1 $2')\n    // Preserve intentional line breaks (after punctuation)\n    .replace(/([.!?:;])\\s*\\n\\s*([A-Z])/g, '$1\\n\\n$2')\n    // Preserve question formatting\n    .replace(/([a-z]\\.|[A-Z]\\.|\\d+\\.)\\s*\\n\\s*/g, '$1 ');\n}\n\n/**\n * Merge broken paragraphs back together\n */\nfunction mergeBrokenParagraphs(text: string): string {\n  const lines = text.split('\\n');\n  const mergedLines: string[] = [];\n  let currentParagraph = '';\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    \n    if (line === '') {\n      // Empty line - end current paragraph\n      if (currentParagraph.trim()) {\n        mergedLines.push(currentParagraph.trim());\n        currentParagraph = '';\n      }\n      mergedLines.push('');\n    } else if (isNewParagraph(line, lines[i - 1])) {\n      // Start of new paragraph\n      if (currentParagraph.trim()) {\n        mergedLines.push(currentParagraph.trim());\n      }\n      currentParagraph = line;\n    } else {\n      // Continue current paragraph\n      if (currentParagraph) {\n        currentParagraph += ' ' + line;\n      } else {\n        currentParagraph = line;\n      }\n    }\n  }\n\n  // Add final paragraph\n  if (currentParagraph.trim()) {\n    mergedLines.push(currentParagraph.trim());\n  }\n\n  return mergedLines.join('\\n');\n}\n\n/**\n * Determine if a line starts a new paragraph\n */\nfunction isNewParagraph(currentLine: string, previousLine?: string): boolean {\n  if (!currentLine || !previousLine) return true;\n\n  const current = currentLine.trim();\n  const previous = previousLine.trim();\n\n  // New paragraph indicators\n  const newParagraphPatterns = [\n    /^\\d+\\./, // Numbered list\n    /^[a-z]\\./, // Lettered list (a., b., c.)\n    /^[A-Z]\\./, // Capital lettered list (A., B., C.)\n    /^[A-Z][a-z]*:/, // Section headers (Word:)\n    /^Chapter \\d+/i, // Chapter headers\n    /^Section \\d+/i, // Section headers\n    /^[A-Z]{2,}/, // ALL CAPS headers\n    /^ABOUT|^WORDS|^COMPREHENSION/i, // Common textbook sections\n    /^\\*/, // Bullet points\n    /^-/, // Dash points\n    /^[ivx]+\\./, // Roman numerals\n  ];\n\n  // Check if current line matches new paragraph patterns\n  for (const pattern of newParagraphPatterns) {\n    if (pattern.test(current)) {\n      return true;\n    }\n  }\n\n  // New paragraph if previous line ended with punctuation and current starts with capital\n  if (/[.!?]$/.test(previous) && /^[A-Z]/.test(current)) {\n    return true;\n  }\n\n  // Educational content: new paragraph for questions\n  if (/^[a-z]\\.\\s+[A-Z]/.test(current) || /^[A-Z]\\.\\s+[A-Z]/.test(current)) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Fix common capitalization issues\n */\nfunction fixCapitalization(text: string): string {\n  return text\n    // Capitalize first letter of sentences\n    .replace(/(^|[.!?]\\s+)([a-z])/g, (match, prefix, letter) => prefix + letter.toUpperCase())\n    // Fix common OCR mistakes with 'I'\n    .replace(/\\bi\\b/g, 'I')\n    // Fix 'i' at start of sentences\n    .replace(/(^|[.!?]\\s+)i\\b/g, '$1I');\n}\n\n/**\n * Final cleanup and formatting\n */\nfunction finalCleanup(text: string): string {\n  return text\n    // Remove extra whitespace\n    .trim()\n    // Ensure proper spacing after punctuation\n    .replace(/([.!?])([A-Z])/g, '$1 $2')\n    // Fix spacing around common punctuation\n    .replace(/\\s+([,.!?;:])/g, '$1')\n    .replace(/([.!?;:])\\s*([A-Z])/g, '$1 $2')\n    // Remove trailing spaces from lines\n    .replace(/[ \\t]+$/gm, '')\n    // Normalize line endings\n    .replace(/\\r\\n/g, '\\n')\n    .replace(/\\r/g, '\\n');\n}\n\n/**\n * Get a preview of text processing changes\n */\nexport function getProcessingPreview(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS) {\n  const processed = processOCRText(rawText, options);\n  \n  return {\n    original: rawText,\n    processed: processed,\n    changes: {\n      originalLength: rawText.length,\n      processedLength: processed.length,\n      originalLines: rawText.split('\\n').length,\n      processedLines: processed.split('\\n').length,\n      originalWords: rawText.split(/\\s+/).length,\n      processedWords: processed.split(/\\s+/).length\n    }\n  };\n}\n\n/**\n * Detect the type of content for specialized processing\n */\nexport function detectContentType(text: string): 'textbook' | 'math' | 'code' | 'table' | 'general' {\n  const mathPatterns = [\n    /\\d+\\s*[+\\-*/=]\\s*\\d+/,\n    /[xy]\\s*[=+\\-]/,\n    /\\b(equation|formula|solve|calculate)\\b/i\n  ];\n\n  const codePatterns = [\n    /function\\s+\\w+\\s*\\(/,\n    /\\bif\\s*\\(/,\n    /\\bfor\\s*\\(/,\n    /[{}();]/\n  ];\n\n  const tablePatterns = [\n    /\\|\\s*\\w+\\s*\\|/,\n    /\\t\\w+\\t/,\n    /^\\s*\\w+\\s+\\w+\\s+\\w+\\s*$/m\n  ];\n\n  if (mathPatterns.some(pattern => pattern.test(text))) {\n    return 'math';\n  }\n  \n  if (codePatterns.some(pattern => pattern.test(text))) {\n    return 'code';\n  }\n  \n  if (tablePatterns.some(pattern => pattern.test(text))) {\n    return 'table';\n  }\n\n  if (/chapter|section|exercise|problem/i.test(text)) {\n    return 'textbook';\n  }\n\n  return 'general';\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAUM,MAAM,6BAAoD;IAC/D,eAAe;IACf,iBAAiB;IACjB,mBAAmB;IACnB,mBAAmB;IACnB,oBAAoB;AACtB;AAKO,SAAS,eAAe,OAAe,EAAE,UAAiC,0BAA0B;IACzG,IAAI,CAAC,WAAW,QAAQ,IAAI,GAAG,MAAM,KAAK,GAAG;QAC3C,OAAO;IACT;IAEA,IAAI,gBAAgB;IAEpB,qDAAqD;IACrD,gBAAgB,mBAAmB;IAEnC,wBAAwB;IACxB,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,gBAAgB,kBAAkB;IACpC;IAEA,gDAAgD;IAChD,gBAAgB,qBAAqB;IAErC,yCAAyC;IACzC,IAAI,QAAQ,aAAa,EAAE;QACzB,gBAAgB,cAAc;IAChC;IAEA,kCAAkC;IAClC,IAAI,QAAQ,eAAe,EAAE;QAC3B,gBAAgB,sBAAsB;IACxC;IAEA,oCAAoC;IACpC,IAAI,QAAQ,iBAAiB,EAAE;QAC7B,gBAAgB,kBAAkB;IACpC;IAEA,wBAAwB;IACxB,gBAAgB,aAAa;IAE7B,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,mBAAmB,IAAY;IACtC,OAAO,IACL,oDAAoD;KACnD,OAAO,CAAC,+DAA+D,IAAI,wCAAwC;KACnH,OAAO,CAAC,yHAAyH,IAAI,oDAAoD;IAC1L,+DAA+D;KAC9D,OAAO,CAAC,qBAAqB,GAC9B,4CAA4C;KAC3C,OAAO,CAAC,0BAA0B,GACnC,qCAAqC;KACpC,OAAO,CAAC,iBAAiB;AAC9B;AAEA;;CAEC,GACD,SAAS,qBAAqB,IAAY;IACxC,OAAO,IACL,oFAAoF;KACnF,OAAO,CAAC,+BAA+B,SACvC,OAAO,CAAC,+BAA+B,SACvC,OAAO,CAAC,6BAA6B,QACtC,oEAAoE;KACnE,OAAO,CAAC,wBAAwB,QACjC,sCAAsC;KACrC,OAAO,CAAC,iCAAiC,MAC1C,uDAAuD;KACtD,OAAO,CAAC,wCAAwC,CAAC,OAAO,KAAK,SAAW,MAAM,MAAM,OAAO,WAAW,GACvG,sCAAsC;KACrC,OAAO,CAAC,6CAA6C,QAAQ,KAAK,WAAW;AAClF;AAEA;;CAEC,GACD,SAAS,kBAAkB,IAAY;IACrC,OAAO,IACL,4CAA4C;KAC3C,OAAO,CAAC,WAAW,IACpB,8CAA8C;KAC7C,OAAO,CAAC,qBAAqB,GAC9B,oDAAoD;KACnD,OAAO,CAAC,WAAW;AACxB;AAEA;;CAEC,GACD,SAAS,cAAc,IAAY;IACjC,OAAO,IACL,0CAA0C;KACzC,OAAO,CAAC,wBAAwB,OACjC,oFAAoF;KACnF,OAAO,CAAC,4BAA4B,QACrC,6FAA6F;KAC5F,OAAO,CAAC,gCAAgC,QACzC,yEAAyE;KACxE,OAAO,CAAC,4CAA4C,SACrD,8CAA8C;KAC7C,OAAO,CAAC,uCAAuC,QAChD,uDAAuD;KACtD,OAAO,CAAC,6BAA6B,WACtC,+BAA+B;KAC9B,OAAO,CAAC,oCAAoC;AACjD;AAEA;;CAEC,GACD,SAAS,sBAAsB,IAAY;IACzC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,cAAwB,EAAE;IAChC,IAAI,mBAAmB;IAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAE1B,IAAI,SAAS,IAAI;YACf,qCAAqC;YACrC,IAAI,iBAAiB,IAAI,IAAI;gBAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;gBACtC,mBAAmB;YACrB;YACA,YAAY,IAAI,CAAC;QACnB,OAAO,IAAI,eAAe,MAAM,KAAK,CAAC,IAAI,EAAE,GAAG;YAC7C,yBAAyB;YACzB,IAAI,iBAAiB,IAAI,IAAI;gBAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;YACxC;YACA,mBAAmB;QACrB,OAAO;YACL,6BAA6B;YAC7B,IAAI,kBAAkB;gBACpB,oBAAoB,MAAM;YAC5B,OAAO;gBACL,mBAAmB;YACrB;QACF;IACF;IAEA,sBAAsB;IACtB,IAAI,iBAAiB,IAAI,IAAI;QAC3B,YAAY,IAAI,CAAC,iBAAiB,IAAI;IACxC;IAEA,OAAO,YAAY,IAAI,CAAC;AAC1B;AAEA;;CAEC,GACD,SAAS,eAAe,WAAmB,EAAE,YAAqB;IAChE,IAAI,CAAC,eAAe,CAAC,cAAc,OAAO;IAE1C,MAAM,UAAU,YAAY,IAAI;IAChC,MAAM,WAAW,aAAa,IAAI;IAElC,2BAA2B;IAC3B,MAAM,uBAAuB;QAC3B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,uDAAuD;IACvD,KAAK,MAAM,WAAW,qBAAsB;QAC1C,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,wFAAwF;IACxF,IAAI,SAAS,IAAI,CAAC,aAAa,SAAS,IAAI,CAAC,UAAU;QACrD,OAAO;IACT;IAEA,mDAAmD;IACnD,IAAI,mBAAmB,IAAI,CAAC,YAAY,mBAAmB,IAAI,CAAC,UAAU;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,kBAAkB,IAAY;IACrC,OAAO,IACL,uCAAuC;KACtC,OAAO,CAAC,wBAAwB,CAAC,OAAO,QAAQ,SAAW,SAAS,OAAO,WAAW,GACvF,mCAAmC;KAClC,OAAO,CAAC,UAAU,IACnB,gCAAgC;KAC/B,OAAO,CAAC,oBAAoB;AACjC;AAEA;;CAEC,GACD,SAAS,aAAa,IAAY;IAChC,OAAO,IACL,0BAA0B;KACzB,IAAI,EACL,0CAA0C;KACzC,OAAO,CAAC,mBAAmB,QAC5B,wCAAwC;KACvC,OAAO,CAAC,kBAAkB,MAC1B,OAAO,CAAC,wBAAwB,QACjC,oCAAoC;KACnC,OAAO,CAAC,aAAa,GACtB,yBAAyB;KACxB,OAAO,CAAC,SAAS,MACjB,OAAO,CAAC,OAAO;AACpB;AAKO,SAAS,qBAAqB,OAAe,EAAE,UAAiC,0BAA0B;IAC/G,MAAM,YAAY,eAAe,SAAS;IAE1C,OAAO;QACL,UAAU;QACV,WAAW;QACX,SAAS;YACP,gBAAgB,QAAQ,MAAM;YAC9B,iBAAiB,UAAU,MAAM;YACjC,eAAe,QAAQ,KAAK,CAAC,MAAM,MAAM;YACzC,gBAAgB,UAAU,KAAK,CAAC,MAAM,MAAM;YAC5C,eAAe,QAAQ,KAAK,CAAC,OAAO,MAAM;YAC1C,gBAAgB,UAAU,KAAK,CAAC,OAAO,MAAM;QAC/C;IACF;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;QACA;QACA;QACA;KACD;IAED,MAAM,gBAAgB;QACpB;QACA;QACA;KACD;IAED,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACpD,OAAO;IACT;IAEA,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACpD,OAAO;IACT;IAEA,IAAI,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,QAAQ;QACrD,OAAO;IACT;IAEA,IAAI,oCAAoC,IAAI,CAAC,OAAO;QAClD,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/upload.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { createWorker } from 'tesseract.js';\nimport { extractTextFromImage, isVisionAPIAvailable } from './vision-ocr';\nimport { processOCRText, detectContentType, DEFAULT_PROCESSING_OPTIONS } from './text-processing';\nimport db from './database';\n\n// Generate realistic mock OCR content for testing\nfunction generateMockOCRContent(filename: string, imageId: number): string {\n  const pageNumber = filename.match(/(\\d+)/)?.[1] || imageId.toString();\n\n  const educationalContent = [\n    // Math content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Mathematical Concepts\n\nProblem ${pageNumber}: Solve for x in the equation 2x + 5 = 15\n\nSolution:\n2x + 5 = 15\n2x = 15 - 5\n2x = 10\nx = 5\n\nTherefore, x = 5.\n\nPractice Problems:\n1. If 3x - 7 = 14, find the value of x.\n2. Solve: 4(x + 2) = 20\n3. What is the value of y if 2y + 3 = 11?\n\nKey Concepts:\n- Linear equations\n- Algebraic manipulation\n- Variable isolation`,\n\n    // Science content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Scientific Principles\n\nLesson ${pageNumber}: The Water Cycle\n\nThe water cycle is a continuous process that describes how water moves through Earth's systems.\n\nMain Stages:\n1. Evaporation - Water from oceans, lakes, and rivers turns into water vapor\n2. Condensation - Water vapor cools and forms clouds\n3. Precipitation - Water falls as rain, snow, or hail\n4. Collection - Water gathers in bodies of water\n\nImportant Facts:\n• 97% of Earth's water is in the oceans\n• Only 3% is fresh water\n• The sun provides energy for the water cycle\n\nQuestions for Review:\n1. What causes water to evaporate?\n2. How do clouds form?\n3. Name three types of precipitation.`,\n\n    // History content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Historical Events\n\nSection ${pageNumber}: Ancient Civilizations\n\nThe ancient civilizations laid the foundation for modern society through their innovations and cultural contributions.\n\nKey Civilizations:\n• Mesopotamia (3500-539 BCE)\n  - Invented writing (cuneiform)\n  - Developed the wheel\n  - Created the first cities\n\n• Ancient Egypt (3100-30 BCE)\n  - Built pyramids and monuments\n  - Developed hieroglyphic writing\n  - Advanced medicine and mathematics\n\n• Ancient Greece (800-146 BCE)\n  - Democracy and philosophy\n  - Olympic Games\n  - Scientific method\n\nTimeline:\n3500 BCE - First cities in Mesopotamia\n3100 BCE - Unification of Egypt\n776 BCE - First Olympic Games\n509 BCE - Roman Republic established`,\n\n    // Language Arts content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Literature and Writing\n\nLesson ${pageNumber}: Elements of Poetry\n\nPoetry is a form of literary expression that uses rhythm, rhyme, and imagery to convey emotions and ideas.\n\nTypes of Poetry:\n1. Haiku - Traditional Japanese form (5-7-5 syllables)\n2. Sonnet - 14-line poem with specific rhyme scheme\n3. Free Verse - No regular pattern or rhyme\n4. Limerick - Humorous 5-line poem\n\nLiterary Devices:\n• Metaphor - Direct comparison without \"like\" or \"as\"\n• Simile - Comparison using \"like\" or \"as\"\n• Alliteration - Repetition of initial consonant sounds\n• Personification - Giving human qualities to non-human things\n\nExample Haiku:\nCherry blossoms fall\nGentle breeze carries petals\nSpring's beauty fades fast\n\nWriting Exercise:\nCreate your own haiku about nature.`\n  ];\n\n  // Select content based on page number\n  const contentIndex = (parseInt(pageNumber) - 1) % educationalContent.length;\n  return educationalContent[contentIndex];\n}\n\n// Ensure uploads directory exists\nexport function ensureUploadDir(classId: number, subjectId: number): string {\n  const className = db.prepare('SELECT name FROM classes WHERE id = ?').get(classId) as any;\n  const subjectName = db.prepare('SELECT name FROM subjects WHERE id = ?').get(subjectId) as any;\n  \n  if (!className || !subjectName) {\n    throw new Error('Invalid class or subject ID');\n  }\n\n  const uploadDir = path.join(process.cwd(), 'uploads', className.name, subjectName.name);\n  \n  if (!fs.existsSync(uploadDir)) {\n    fs.mkdirSync(uploadDir, { recursive: true });\n  }\n  \n  return uploadDir;\n}\n\n// Save uploaded file\nexport async function saveUploadedFile(\n  file: File,\n  classId: number,\n  subjectId: number,\n  pageType: string = 'unassigned'\n): Promise<{ id: number; filePath: string }> {\n  try {\n    const uploadDir = ensureUploadDir(classId, subjectId);\n\n    // Generate unique filename\n    const timestamp = Date.now();\n    const extension = path.extname(file.name);\n    const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\n    const filePath = path.join(uploadDir, filename);\n\n    // Save file to disk\n    const buffer = Buffer.from(await file.arrayBuffer());\n    fs.writeFileSync(filePath, buffer);\n\n    // Get the next upload order for this class/subject\n    const maxOrder = db.prepare(`\n      SELECT MAX(upload_order) as max_order\n      FROM images\n      WHERE class_id = ? AND subject_id = ?\n    `).get(classId, subjectId) as any;\n\n    const uploadOrder = (maxOrder?.max_order || 0) + 1;\n\n    // Save file info to database\n    const result = db.prepare(`\n      INSERT INTO images (file_path, original_name, class_id, subject_id, page_type, upload_order)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `).run(filePath, file.name, classId, subjectId, pageType, uploadOrder);\n\n    return {\n      id: result.lastInsertRowid as number,\n      filePath: filePath\n    };\n  } catch (error) {\n    console.error('File upload error:', error);\n    throw new Error('Failed to save uploaded file');\n  }\n}\n\n// Process OCR on uploaded image\nexport async function processOCR(imageId: number, forceRegenerate: boolean = false): Promise<string> {\n  try {\n    const image = db.prepare('SELECT file_path, original_name FROM images WHERE id = ?').get(imageId) as any;\n\n    if (!image) {\n      throw new Error('Image not found');\n    }\n\n    // Check if OCR already exists (unless force regenerate is requested)\n    if (!forceRegenerate) {\n      const existingOCR = db.prepare('SELECT content FROM ocr_text WHERE image_id = ?').get(imageId) as any;\n      if (existingOCR) {\n        console.log(`OCR already exists for image ID: ${imageId}`);\n        return existingOCR.content;\n      }\n    } else {\n      console.log(`Force regenerating OCR for image ID: ${imageId}`);\n      // Clear existing OCR data\n      db.prepare('DELETE FROM ocr_text WHERE image_id = ?').run(imageId);\n    }\n\n    // Check if file exists\n    if (!fs.existsSync(image.file_path)) {\n      throw new Error('Image file not found on disk');\n    }\n\n    console.log(`Starting OCR processing for image ID: ${imageId}, file: ${image.file_path}`);\n\n    try {\n      // Try Google Vision API first, then fallback to Tesseract.js if needed\n      console.log(`Starting OCR processing for image ID: ${imageId}`);\n      console.log(`Image path: ${image.file_path}`);\n\n      // Check if Google Vision API is available\n      const isVisionAvailable = await isVisionAPIAvailable();\n\n      if (isVisionAvailable) {\n        console.log(`Using Google Vision API for image ID: ${imageId}`);\n\n        // Use Google Vision API for OCR\n        const visionResult = await extractTextFromImage(image.file_path);\n\n        if (visionResult.success && visionResult.text.length > 0) {\n          console.log(`Google Vision OCR completed for image ID: ${imageId}`);\n          console.log(`Extracted text length: ${visionResult.text.length} characters`);\n          console.log(`OCR confidence: ${(visionResult.confidence * 100).toFixed(1)}%`);\n\n          // Post-process the extracted text for better formatting\n          console.log(`Post-processing text for image ID: ${imageId}`);\n          const contentType = detectContentType(visionResult.text);\n          console.log(`Detected content type: ${contentType}`);\n\n          const processedText = processOCRText(visionResult.text, {\n            ...DEFAULT_PROCESSING_OPTIONS,\n            preserveFormatting: contentType === 'code' || contentType === 'table'\n          });\n\n          console.log(`Text processing completed. Original: ${visionResult.text.length} chars, Processed: ${processedText.length} chars`);\n\n          // Save processed OCR text to database\n          db.prepare(`\n            INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n            VALUES (?, ?, 1)\n          `).run(imageId, processedText);\n\n          console.log(`Google Vision OCR completed and saved for image ID: ${imageId}`);\n          return processedText;\n        } else {\n          throw new Error(visionResult.error || 'Google Vision API returned no text');\n        }\n      } else {\n        console.log(`Google Vision API not available, trying Tesseract.js for image ID: ${imageId}`);\n\n        // Fallback to Tesseract.js\n        const worker = await createWorker('eng');\n        console.log(`Tesseract worker created, processing image: ${image.file_path}`);\n\n        const { data: { text, confidence } } = await worker.recognize(image.file_path);\n        await worker.terminate();\n\n        console.log(`Tesseract OCR completed for image ID: ${imageId}`);\n        console.log(`Extracted text length: ${text.length} characters`);\n        console.log(`OCR confidence: ${confidence}%`);\n\n        const cleanedText = text.trim();\n\n        if (cleanedText.length === 0) {\n          throw new Error('No text extracted from image');\n        }\n\n        // Post-process the extracted text for better formatting\n        console.log(`Post-processing Tesseract text for image ID: ${imageId}`);\n        const contentType = detectContentType(cleanedText);\n        console.log(`Detected content type: ${contentType}`);\n\n        const processedText = processOCRText(cleanedText, {\n          ...DEFAULT_PROCESSING_OPTIONS,\n          preserveFormatting: contentType === 'code' || contentType === 'table'\n        });\n\n        console.log(`Text processing completed. Original: ${cleanedText.length} chars, Processed: ${processedText.length} chars`);\n\n        // Save processed OCR text to database\n        db.prepare(`\n          INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n          VALUES (?, ?, 1)\n        `).run(imageId, processedText);\n\n        console.log(`Tesseract OCR completed and saved for image ID: ${imageId}`);\n        return processedText;\n      }\n\n    } catch (ocrError) {\n      console.error(`OCR processing failed for image ${imageId}:`, ocrError);\n      console.error(`Error details:`, {\n        message: ocrError instanceof Error ? ocrError.message : 'Unknown error',\n        stack: ocrError instanceof Error ? ocrError.stack : 'No stack trace',\n        name: ocrError instanceof Error ? ocrError.name : 'Unknown error type'\n      });\n\n      // Fallback to mock OCR if all real OCR methods fail\n      console.log(`Falling back to mock OCR for image ID: ${imageId}`);\n\n      const mockOCRContent = generateMockOCRContent(image.original_name, imageId);\n\n      console.log(`Fallback mock OCR generated for image ID: ${imageId}, length: ${mockOCRContent.length} characters`);\n\n      // Save OCR text to database with a note about fallback\n      const fallbackContent = `[FALLBACK OCR - Real OCR Failed]\\n\\n${mockOCRContent}\\n\\n[Original Error: ${ocrError instanceof Error ? ocrError.message : 'Unknown error'}]\\n\\n[Note: To enable real OCR, configure Google Vision API or fix Tesseract.js setup]`;\n\n      db.prepare(`\n        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n        VALUES (?, ?, 1)\n      `).run(imageId, fallbackContent);\n\n      console.log(`Fallback OCR completed and saved for image ID: ${imageId}`);\n      return fallbackContent;\n    }\n  } catch (error) {\n    console.error(`OCR processing error for image ${imageId}:`, error);\n\n    // Save error state to database\n    try {\n      db.prepare(`\n        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n        VALUES (?, ?, 0)\n      `).run(imageId, `OCR Error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    } catch (dbError) {\n      console.error('Failed to save OCR error to database:', dbError);\n    }\n\n    throw new Error(`Failed to process OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n\n\n// Get OCR text for an image\nexport function getOCRText(imageId: number): string | null {\n  const result = db.prepare('SELECT content FROM ocr_text WHERE image_id = ? ORDER BY created_at DESC LIMIT 1')\n    .get(imageId) as any;\n  \n  return result ? result.content : null;\n}\n\n// Get all images for a class/subject\nexport function getImages(classId?: number, subjectId?: number) {\n  let query = `\n    SELECT i.*, c.name as class_name, s.name as subject_name,\n           ocr.content as ocr_content, ocr.processed\n    FROM images i\n    JOIN classes c ON i.class_id = c.id\n    JOIN subjects s ON i.subject_id = s.id\n    LEFT JOIN ocr_text ocr ON i.id = ocr.image_id\n  `;\n\n  const params: any[] = [];\n  const conditions: string[] = [];\n\n  if (classId) {\n    conditions.push('i.class_id = ?');\n    params.push(classId);\n  }\n\n  if (subjectId) {\n    conditions.push('i.subject_id = ?');\n    params.push(subjectId);\n  }\n\n  if (conditions.length > 0) {\n    query += ' WHERE ' + conditions.join(' AND ');\n  }\n\n  query += ' ORDER BY COALESCE(i.upload_order, 999999) ASC, i.uploaded_at ASC';\n\n  return db.prepare(query).all(...params);\n}\n\n// Delete image and its OCR data\nexport function deleteImage(imageId: number): boolean {\n  try {\n    const image = db.prepare('SELECT file_path FROM images WHERE id = ?').get(imageId) as any;\n    \n    if (image && fs.existsSync(image.file_path)) {\n      fs.unlinkSync(image.file_path);\n    }\n    \n    // Delete from database (OCR text will be deleted by foreign key cascade)\n    db.prepare('DELETE FROM images WHERE id = ?').run(imageId);\n    \n    return true;\n  } catch (error) {\n    console.error('Delete image error:', error);\n    return false;\n  }\n}\n\n// Parse questions from OCR text with chapter context\nexport function parseQuestionsFromOCR(ocrText: string, imageId?: number): Array<{\n  type: string;\n  content: string;\n  options?: string[];\n  correctAnswer?: string;\n  chapter?: string;\n}> {\n  const questions: Array<{\n    type: string;\n    content: string;\n    options?: string[];\n    correctAnswer?: string;\n    chapter?: string;\n  }> = [];\n\n  // Get chapter context from image if provided\n  let chapterContext = '';\n  if (imageId) {\n    const image = db.prepare('SELECT page_type FROM images WHERE id = ?').get(imageId) as any;\n    if (image && image.page_type && image.page_type.startsWith('chapter-')) {\n      chapterContext = image.page_type.replace('chapter-', 'Chapter ');\n    }\n  }\n\n  // Split text into lines and clean up\n  const lines = ocrText.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n  \n  let currentQuestion = '';\n  let currentOptions: string[] = [];\n  let questionType = 'short_answer'; // default\n  \n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    \n    // Detect question patterns\n    if (line.match(/^\\d+[\\.\\)]\\s*/) || line.match(/^Q\\d*[\\.\\)]\\s*/i)) {\n      // Save previous question if exists\n      if (currentQuestion) {\n        questions.push({\n          type: questionType,\n          content: currentQuestion,\n          options: currentOptions.length > 0 ? currentOptions : undefined,\n          chapter: chapterContext\n        });\n      }\n      \n      // Start new question\n      currentQuestion = line.replace(/^\\d+[\\.\\)]\\s*/, '').replace(/^Q\\d*[\\.\\)]\\s*/i, '');\n      currentOptions = [];\n      \n      // Detect question type\n      if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {\n        questionType = 'true_false';\n      } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {\n        questionType = 'fill_blank';\n      } else {\n        questionType = 'short_answer';\n      }\n    }\n    // Detect MCQ options\n    else if (line.match(/^[a-d][\\.\\)]\\s*/i) || line.match(/^\\([a-d]\\)\\s*/i)) {\n      if (currentQuestion) {\n        questionType = 'mcq';\n        const option = line.replace(/^[a-d][\\.\\)]\\s*/i, '').replace(/^\\([a-d]\\)\\s*/i, '');\n        currentOptions.push(option);\n      }\n    }\n    // Continue current question\n    else if (currentQuestion && !line.match(/^[a-d][\\.\\)]\\s*/i)) {\n      currentQuestion += ' ' + line;\n    }\n  }\n  \n  // Save last question\n  if (currentQuestion) {\n    questions.push({\n      type: questionType,\n      content: currentQuestion,\n      options: currentOptions.length > 0 ? currentOptions : undefined,\n      chapter: chapterContext\n    });\n  }\n  \n  return questions;\n}\n\n// Update page type for an image\nexport function updateImagePageType(imageId: number, pageType: string): boolean {\n  try {\n    const validPageTypes = [\n      'cover', 'contents', 'unassigned',\n      ...Array.from({length: 30}, (_, i) => `chapter-${i + 1}`)\n    ];\n\n    if (!validPageTypes.includes(pageType)) {\n      throw new Error('Invalid page type');\n    }\n\n    db.prepare('UPDATE images SET page_type = ? WHERE id = ?').run(pageType, imageId);\n    return true;\n  } catch (error) {\n    console.error('Update page type error:', error);\n    return false;\n  }\n}\n\n// Auto-assign page types based on upload order and existing assignments\nexport function autoAssignPageTypes(classId: number, subjectId: number): void {\n  try {\n    const images = db.prepare(`\n      SELECT id, upload_order, page_type\n      FROM images\n      WHERE class_id = ? AND subject_id = ?\n      ORDER BY upload_order ASC\n    `).all(classId, subjectId) as any[];\n\n    let currentChapter = 1;\n    let foundFirstChapter = false;\n\n    for (const image of images) {\n      if (image.page_type !== 'unassigned') {\n        // If this is a chapter marker, update current chapter\n        const chapterMatch = image.page_type.match(/^chapter-(\\d+)$/);\n        if (chapterMatch) {\n          currentChapter = parseInt(chapterMatch[1]);\n          foundFirstChapter = true;\n        }\n        continue;\n      }\n\n      // Auto-assign based on position\n      if (!foundFirstChapter) {\n        // Before any chapter is found, assume it's cover or contents\n        if (image.upload_order === 1) {\n          updateImagePageType(image.id, 'cover');\n        } else {\n          updateImagePageType(image.id, 'contents');\n        }\n      } else {\n        // After a chapter is found, assign to current chapter\n        updateImagePageType(image.id, `chapter-${currentChapter}`);\n      }\n    }\n  } catch (error) {\n    console.error('Auto-assign page types error:', error);\n  }\n}\n\n// Get file size in a readable format\nexport function getFileSize(filePath: string): string {\n  try {\n    const stats = fs.statSync(filePath);\n    const bytes = stats.size;\n\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  } catch (error) {\n    return 'Unknown';\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,kDAAkD;AAClD,SAAS,uBAAuB,QAAgB,EAAE,OAAe;IAC/D,MAAM,aAAa,SAAS,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,QAAQ,QAAQ;IAEnE,MAAM,qBAAqB;QACzB,eAAe;QACf,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;QAE3C,EAAE,WAAW;;;;;;;;;;;;;;;;;;oBAkBD,CAAC;QAEjB,kBAAkB;QAClB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;OAE5C,EAAE,WAAW;;;;;;;;;;;;;;;;;;qCAkBiB,CAAC;QAElC,kBAAkB;QAClB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;QAE3C,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;oCAwBe,CAAC;QAEjC,wBAAwB;QACxB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;OAE5C,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;mCAsBe,CAAC;KACjC;IAED,sCAAsC;IACtC,MAAM,eAAe,CAAC,SAAS,cAAc,CAAC,IAAI,mBAAmB,MAAM;IAC3E,OAAO,kBAAkB,CAAC,aAAa;AACzC;AAGO,SAAS,gBAAgB,OAAe,EAAE,SAAiB;IAChE,MAAM,YAAY,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,yCAAyC,GAAG,CAAC;IAC1E,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,0CAA0C,GAAG,CAAC;IAE7E,IAAI,CAAC,aAAa,CAAC,aAAa;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,UAAU,IAAI,EAAE,YAAY,IAAI;IAEtF,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;QAC7B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW;YAAE,WAAW;QAAK;IAC5C;IAEA,OAAO;AACT;AAGO,eAAe,iBACpB,IAAU,EACV,OAAe,EACf,SAAiB,EACjB,WAAmB,YAAY;IAE/B,IAAI;QACF,MAAM,YAAY,gBAAgB,SAAS;QAE3C,2BAA2B;QAC3B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,MAAM;QAC5E,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,oBAAoB;QACpB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,mDAAmD;QACnD,MAAM,WAAW,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;IAI7B,CAAC,EAAE,GAAG,CAAC,SAAS;QAEhB,MAAM,cAAc,CAAC,UAAU,aAAa,CAAC,IAAI;QAEjD,6BAA6B;QAC7B,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,UAAU,KAAK,IAAI,EAAE,SAAS,WAAW,UAAU;QAE1D,OAAO;YACL,IAAI,OAAO,eAAe;YAC1B,UAAU;QACZ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,OAAe,EAAE,kBAA2B,KAAK;IAChF,IAAI;QACF,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,4DAA4D,GAAG,CAAC;QAEzF,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qEAAqE;QACrE,IAAI,CAAC,iBAAiB;YACpB,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,mDAAmD,GAAG,CAAC;YACtF,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS;gBACzD,OAAO,YAAY,OAAO;YAC5B;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,SAAS;YAC7D,0BAA0B;YAC1B,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,2CAA2C,GAAG,CAAC;QAC5D;QAEA,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS,GAAG;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,QAAQ,QAAQ,EAAE,MAAM,SAAS,EAAE;QAExF,IAAI;YACF,uEAAuE;YACvE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS;YAC9D,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,MAAM,SAAS,EAAE;YAE5C,0CAA0C;YAC1C,MAAM,oBAAoB,MAAM,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD;YAEnD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS;gBAE9D,gCAAgC;gBAChC,MAAM,eAAe,MAAM,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,SAAS;gBAE/D,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM,GAAG,GAAG;oBACxD,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,SAAS;oBAClE,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC3E,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC,aAAa,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;oBAE5E,wDAAwD;oBACxD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS;oBAC3D,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,IAAI;oBACvD,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa;oBAEnD,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI,EAAE;wBACtD,GAAG,kIAAA,CAAA,6BAA0B;wBAC7B,oBAAoB,gBAAgB,UAAU,gBAAgB;oBAChE;oBAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,cAAc,MAAM,CAAC,MAAM,CAAC;oBAE9H,sCAAsC;oBACtC,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;UAGZ,CAAC,EAAE,GAAG,CAAC,SAAS;oBAEhB,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,SAAS;oBAC5E,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,aAAa,KAAK,IAAI;gBACxC;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,mEAAmE,EAAE,SAAS;gBAE3F,2BAA2B;gBAC3B,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE;gBAClC,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,MAAM,SAAS,EAAE;gBAE5E,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,GAAG,MAAM,OAAO,SAAS,CAAC,MAAM,SAAS;gBAC7E,MAAM,OAAO,SAAS;gBAEtB,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS;gBAC9D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK,MAAM,CAAC,WAAW,CAAC;gBAC9D,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;gBAE5C,MAAM,cAAc,KAAK,IAAI;gBAE7B,IAAI,YAAY,MAAM,KAAK,GAAG;oBAC5B,MAAM,IAAI,MAAM;gBAClB;gBAEA,wDAAwD;gBACxD,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,SAAS;gBACrE,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE;gBACtC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa;gBAEnD,MAAM,gBAAgB,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;oBAChD,GAAG,kIAAA,CAAA,6BAA0B;oBAC7B,oBAAoB,gBAAgB,UAAU,gBAAgB;gBAChE;gBAEA,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,YAAY,MAAM,CAAC,mBAAmB,EAAE,cAAc,MAAM,CAAC,MAAM,CAAC;gBAExH,sCAAsC;gBACtC,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;QAGZ,CAAC,EAAE,GAAG,CAAC,SAAS;gBAEhB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,SAAS;gBACxE,OAAO;YACT;QAEF,EAAE,OAAO,UAAU;YACjB,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC,EAAE;YAC7D,QAAQ,KAAK,CAAC,CAAC,cAAc,CAAC,EAAE;gBAC9B,SAAS,oBAAoB,QAAQ,SAAS,OAAO,GAAG;gBACxD,OAAO,oBAAoB,QAAQ,SAAS,KAAK,GAAG;gBACpD,MAAM,oBAAoB,QAAQ,SAAS,IAAI,GAAG;YACpD;YAEA,oDAAoD;YACpD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,SAAS;YAE/D,MAAM,iBAAiB,uBAAuB,MAAM,aAAa,EAAE;YAEnE,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,QAAQ,UAAU,EAAE,eAAe,MAAM,CAAC,WAAW,CAAC;YAE/G,uDAAuD;YACvD,MAAM,kBAAkB,CAAC,oCAAoC,EAAE,eAAe,qBAAqB,EAAE,oBAAoB,QAAQ,SAAS,OAAO,GAAG,gBAAgB,sFAAsF,CAAC;YAE3P,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGZ,CAAC,EAAE,GAAG,CAAC,SAAS;YAEhB,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,SAAS;YACvE,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAE;QAE5D,+BAA+B;QAC/B,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGZ,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC1F,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,yCAAyC;QACzD;QAEA,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACtG;AACF;AAKO,SAAS,WAAW,OAAe;IACxC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oFACvB,GAAG,CAAC;IAEP,OAAO,SAAS,OAAO,OAAO,GAAG;AACnC;AAGO,SAAS,UAAU,OAAgB,EAAE,SAAkB;IAC5D,IAAI,QAAQ,CAAC;;;;;;;EAOb,CAAC;IAED,MAAM,SAAgB,EAAE;IACxB,MAAM,aAAuB,EAAE;IAE/B,IAAI,SAAS;QACX,WAAW,IAAI,CAAC;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,WAAW,IAAI,CAAC;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,SAAS,YAAY,WAAW,IAAI,CAAC;IACvC;IAEA,SAAS;IAET,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;AAClC;AAGO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6CAA6C,GAAG,CAAC;QAE1E,IAAI,SAAS,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS,GAAG;YAC3C,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS;QAC/B;QAEA,yEAAyE;QACzE,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,mCAAmC,GAAG,CAAC;QAElD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAGO,SAAS,sBAAsB,OAAe,EAAE,OAAgB;IAOrE,MAAM,YAMD,EAAE;IAEP,6CAA6C;IAC7C,IAAI,iBAAiB;IACrB,IAAI,SAAS;QACX,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6CAA6C,GAAG,CAAC;QAC1E,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,UAAU,CAAC,aAAa;YACtE,iBAAiB,MAAM,SAAS,CAAC,OAAO,CAAC,YAAY;QACvD;IACF;IAEA,qCAAqC;IACrC,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAExF,IAAI,kBAAkB;IACtB,IAAI,iBAA2B,EAAE;IACjC,IAAI,eAAe,gBAAgB,UAAU;IAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,2BAA2B;QAC3B,IAAI,KAAK,KAAK,CAAC,oBAAoB,KAAK,KAAK,CAAC,oBAAoB;YAChE,mCAAmC;YACnC,IAAI,iBAAiB;gBACnB,UAAU,IAAI,CAAC;oBACb,MAAM;oBACN,SAAS;oBACT,SAAS,eAAe,MAAM,GAAG,IAAI,iBAAiB;oBACtD,SAAS;gBACX;YACF;YAEA,qBAAqB;YACrB,kBAAkB,KAAK,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,mBAAmB;YAC/E,iBAAiB,EAAE;YAEnB,uBAAuB;YACvB,IAAI,gBAAgB,WAAW,GAAG,QAAQ,CAAC,WAAW,gBAAgB,WAAW,GAAG,QAAQ,CAAC,UAAU;gBACrG,eAAe;YACjB,OAAO,IAAI,gBAAgB,QAAQ,CAAC,YAAY,gBAAgB,QAAQ,CAAC,SAAS;gBAChF,eAAe;YACjB,OAAO;gBACL,eAAe;YACjB;QACF,OAEK,IAAI,KAAK,KAAK,CAAC,uBAAuB,KAAK,KAAK,CAAC,mBAAmB;YACvE,IAAI,iBAAiB;gBACnB,eAAe;gBACf,MAAM,SAAS,KAAK,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,kBAAkB;gBAC9E,eAAe,IAAI,CAAC;YACtB;QACF,OAEK,IAAI,mBAAmB,CAAC,KAAK,KAAK,CAAC,qBAAqB;YAC3D,mBAAmB,MAAM;QAC3B;IACF;IAEA,qBAAqB;IACrB,IAAI,iBAAiB;QACnB,UAAU,IAAI,CAAC;YACb,MAAM;YACN,SAAS;YACT,SAAS,eAAe,MAAM,GAAG,IAAI,iBAAiB;YACtD,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAgB;IACnE,IAAI;QACF,MAAM,iBAAiB;YACrB;YAAS;YAAY;eAClB,MAAM,IAAI,CAAC;gBAAC,QAAQ;YAAE,GAAG,CAAC,GAAG,IAAM,CAAC,QAAQ,EAAE,IAAI,GAAG;SACzD;QAED,IAAI,CAAC,eAAe,QAAQ,CAAC,WAAW;YACtC,MAAM,IAAI,MAAM;QAClB;QAEA,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gDAAgD,GAAG,CAAC,UAAU;QACzE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,SAAiB;IACpE,IAAI;QACF,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAK3B,CAAC,EAAE,GAAG,CAAC,SAAS;QAEhB,IAAI,iBAAiB;QACrB,IAAI,oBAAoB;QAExB,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI,MAAM,SAAS,KAAK,cAAc;gBACpC,sDAAsD;gBACtD,MAAM,eAAe,MAAM,SAAS,CAAC,KAAK,CAAC;gBAC3C,IAAI,cAAc;oBAChB,iBAAiB,SAAS,YAAY,CAAC,EAAE;oBACzC,oBAAoB;gBACtB;gBACA;YACF;YAEA,gCAAgC;YAChC,IAAI,CAAC,mBAAmB;gBACtB,6DAA6D;gBAC7D,IAAI,MAAM,YAAY,KAAK,GAAG;oBAC5B,oBAAoB,MAAM,EAAE,EAAE;gBAChC,OAAO;oBACL,oBAAoB,MAAM,EAAE,EAAE;gBAChC;YACF,OAAO;gBACL,sDAAsD;gBACtD,oBAAoB,MAAM,EAAE,EAAE,CAAC,QAAQ,EAAE,gBAAgB;YAC3D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAGO,SAAS,YAAY,QAAgB;IAC1C,IAAI;QACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAC1B,MAAM,QAAQ,MAAM,IAAI;QAExB,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/api/admin/books/%5Bid%5D/clear-ocr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { requireAdmin } from '@/lib/auth';\nimport { BookModel } from '@/lib/models';\nimport { processOCR } from '@/lib/upload';\nimport db from '@/lib/database';\n\nexport async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {\n  const user = await requireAdmin();\n  \n  if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n\n  try {\n    const { id } = await params;\n    const bookId = parseInt(id);\n    \n    if (isNaN(bookId)) {\n      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });\n    }\n\n    // Verify book exists\n    const book = BookModel.getById(bookId);\n    if (!book) {\n      return NextResponse.json({ error: 'Book not found' }, { status: 404 });\n    }\n\n    // Get all images for this book\n    const images = db.prepare('SELECT id FROM images WHERE book_id = ?').all(bookId) as any[];\n\n    // Clear all OCR data for this book\n    const result = db.prepare(`\n      DELETE FROM ocr_text\n      WHERE image_id IN (\n        SELECT id FROM images WHERE book_id = ?\n      )\n    `).run(bookId);\n\n    // Regenerate OCR with new mock system for all images\n    let regeneratedCount = 0;\n    for (const image of images) {\n      try {\n        await processOCR(image.id, true); // Force regenerate\n        regeneratedCount++;\n      } catch (error) {\n        console.error(`Failed to regenerate OCR for image ${image.id}:`, error);\n      }\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: `Cleared OCR data for ${result.changes} images and regenerated ${regeneratedCount} with new mock system`,\n      cleared_count: result.changes,\n      regenerated_count: regeneratedCount\n    });\n\n  } catch (error) {\n    console.error('Error clearing OCR data:', error);\n    return NextResponse.json(\n      { error: 'Failed to clear OCR data' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,OAAO,OAAoB,EAAE,EAAE,MAAM,EAAuC;IAChG,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,SAAS,SAAS;QAExB,IAAI,MAAM,SAAS;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,qBAAqB;QACrB,MAAM,OAAO,sHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,+BAA+B;QAC/B,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,2CAA2C,GAAG,CAAC;QAEzE,mCAAmC;QACnC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAK3B,CAAC,EAAE,GAAG,CAAC;QAEP,qDAAqD;QACrD,IAAI,mBAAmB;QACvB,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI;gBACF,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,EAAE,EAAE,OAAO,mBAAmB;gBACrD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE;YACnE;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS,CAAC,qBAAqB,EAAE,OAAO,OAAO,CAAC,wBAAwB,EAAE,iBAAiB,qBAAqB,CAAC;YACjH,eAAe,OAAO,OAAO;YAC7B,mBAAmB;QACrB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}