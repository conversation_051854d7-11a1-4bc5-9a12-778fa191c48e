// Copyright 2021 Google LLC.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
import * as $protobuf from "protobufjs";
import Long = require('long');
/** Namespace google. */
export namespace google {

    /** Namespace cloud. */
    namespace cloud {

        /** Namespace compute. */
        namespace compute {

            /** Namespace v1. */
            namespace v1 {

                /** Properties of an Operation. */
                interface IOperation {

                    /** Operation clientOperationId */
                    clientOperationId?: (string|null);

                    /** Operation creationTimestamp */
                    creationTimestamp?: (string|null);

                    /** Operation description */
                    description?: (string|null);

                    /** Operation endTime */
                    endTime?: (string|null);

                    /** Operation error */
                    error?: (google.cloud.compute.v1.IError|null);

                    /** Operation httpErrorMessage */
                    httpErrorMessage?: (string|null);

                    /** Operation httpErrorStatusCode */
                    httpErrorStatusCode?: (number|null);

                    /** Operation id */
                    id?: (string|null);

                    /** Operation insertTime */
                    insertTime?: (string|null);

                    /** Operation kind */
                    kind?: (string|null);

                    /** Operation name */
                    name?: (string|null);

                    /** Operation operationType */
                    operationType?: (string|null);

                    /** Operation progress */
                    progress?: (number|null);

                    /** Operation region */
                    region?: (string|null);

                    /** Operation selfLink */
                    selfLink?: (string|null);

                    /** Operation startTime */
                    startTime?: (string|null);

                    /** Operation status */
                    status?: (google.cloud.compute.v1.Operation.Status|null);

                    /** Operation statusMessage */
                    statusMessage?: (string|null);

                    /** Operation targetId */
                    targetId?: (string|null);

                    /** Operation targetLink */
                    targetLink?: (string|null);

                    /** Operation user */
                    user?: (string|null);

                    /** Operation warnings */
                    warnings?: (google.cloud.compute.v1.IWarnings[]|null);

                    /** Operation zone */
                    zone?: (string|null);
                }

                /** Represents an Operation. */
                class Operation implements IOperation {

                    /**
                     * Constructs a new Operation.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IOperation);

                    /** Operation clientOperationId. */
                    public clientOperationId?: (string|null);

                    /** Operation creationTimestamp. */
                    public creationTimestamp?: (string|null);

                    /** Operation description. */
                    public description?: (string|null);

                    /** Operation endTime. */
                    public endTime?: (string|null);

                    /** Operation error. */
                    public error?: (google.cloud.compute.v1.IError|null);

                    /** Operation httpErrorMessage. */
                    public httpErrorMessage?: (string|null);

                    /** Operation httpErrorStatusCode. */
                    public httpErrorStatusCode?: (number|null);

                    /** Operation id. */
                    public id?: (string|null);

                    /** Operation insertTime. */
                    public insertTime?: (string|null);

                    /** Operation kind. */
                    public kind?: (string|null);

                    /** Operation name. */
                    public name?: (string|null);

                    /** Operation operationType. */
                    public operationType?: (string|null);

                    /** Operation progress. */
                    public progress?: (number|null);

                    /** Operation region. */
                    public region?: (string|null);

                    /** Operation selfLink. */
                    public selfLink?: (string|null);

                    /** Operation startTime. */
                    public startTime?: (string|null);

                    /** Operation status. */
                    public status?: (google.cloud.compute.v1.Operation.Status|null);

                    /** Operation statusMessage. */
                    public statusMessage?: (string|null);

                    /** Operation targetId. */
                    public targetId?: (string|null);

                    /** Operation targetLink. */
                    public targetLink?: (string|null);

                    /** Operation user. */
                    public user?: (string|null);

                    /** Operation warnings. */
                    public warnings: google.cloud.compute.v1.IWarnings[];

                    /** Operation zone. */
                    public zone?: (string|null);

                    /** Operation _clientOperationId. */
                    public _clientOperationId?: "clientOperationId";

                    /** Operation _creationTimestamp. */
                    public _creationTimestamp?: "creationTimestamp";

                    /** Operation _description. */
                    public _description?: "description";

                    /** Operation _endTime. */
                    public _endTime?: "endTime";

                    /** Operation _error. */
                    public _error?: "error";

                    /** Operation _httpErrorMessage. */
                    public _httpErrorMessage?: "httpErrorMessage";

                    /** Operation _httpErrorStatusCode. */
                    public _httpErrorStatusCode?: "httpErrorStatusCode";

                    /** Operation _id. */
                    public _id?: "id";

                    /** Operation _insertTime. */
                    public _insertTime?: "insertTime";

                    /** Operation _kind. */
                    public _kind?: "kind";

                    /** Operation _name. */
                    public _name?: "name";

                    /** Operation _operationType. */
                    public _operationType?: "operationType";

                    /** Operation _progress. */
                    public _progress?: "progress";

                    /** Operation _region. */
                    public _region?: "region";

                    /** Operation _selfLink. */
                    public _selfLink?: "selfLink";

                    /** Operation _startTime. */
                    public _startTime?: "startTime";

                    /** Operation _status. */
                    public _status?: "status";

                    /** Operation _statusMessage. */
                    public _statusMessage?: "statusMessage";

                    /** Operation _targetId. */
                    public _targetId?: "targetId";

                    /** Operation _targetLink. */
                    public _targetLink?: "targetLink";

                    /** Operation _user. */
                    public _user?: "user";

                    /** Operation _zone. */
                    public _zone?: "zone";

                    /**
                     * Creates a new Operation instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Operation instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IOperation): google.cloud.compute.v1.Operation;

                    /**
                     * Encodes the specified Operation message. Does not implicitly {@link google.cloud.compute.v1.Operation.verify|verify} messages.
                     * @param message Operation message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IOperation, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Operation message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Operation.verify|verify} messages.
                     * @param message Operation message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IOperation, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an Operation message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Operation
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Operation;

                    /**
                     * Decodes an Operation message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Operation
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Operation;

                    /**
                     * Verifies an Operation message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an Operation message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Operation
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Operation;

                    /**
                     * Creates a plain object from an Operation message. Also converts values to other types if specified.
                     * @param message Operation
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Operation, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Operation to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                namespace Operation {

                    /** Status enum. */
                    enum Status {
                        UNDEFINED_STATUS = 0,
                        DONE = 2104194,
                        PENDING = 35394935,
                        RUNNING = 121282975
                    }
                }

                /** Properties of an Errors. */
                interface IErrors {

                    /** Errors code */
                    code?: (string|null);

                    /** Errors location */
                    location?: (string|null);

                    /** Errors message */
                    message?: (string|null);
                }

                /** Represents an Errors. */
                class Errors implements IErrors {

                    /**
                     * Constructs a new Errors.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IErrors);

                    /** Errors code. */
                    public code?: (string|null);

                    /** Errors location. */
                    public location?: (string|null);

                    /** Errors message. */
                    public message?: (string|null);

                    /** Errors _code. */
                    public _code?: "code";

                    /** Errors _location. */
                    public _location?: "location";

                    /** Errors _message. */
                    public _message?: "message";

                    /**
                     * Creates a new Errors instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Errors instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IErrors): google.cloud.compute.v1.Errors;

                    /**
                     * Encodes the specified Errors message. Does not implicitly {@link google.cloud.compute.v1.Errors.verify|verify} messages.
                     * @param message Errors message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IErrors, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Errors message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Errors.verify|verify} messages.
                     * @param message Errors message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IErrors, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an Errors message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Errors
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Errors;

                    /**
                     * Decodes an Errors message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Errors
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Errors;

                    /**
                     * Verifies an Errors message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an Errors message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Errors
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Errors;

                    /**
                     * Creates a plain object from an Errors message. Also converts values to other types if specified.
                     * @param message Errors
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Errors, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Errors to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of an Error. */
                interface IError {

                    /** Error errors */
                    errors?: (google.cloud.compute.v1.IErrors[]|null);
                }

                /** Represents an Error. */
                class Error implements IError {

                    /**
                     * Constructs a new Error.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IError);

                    /** Error errors. */
                    public errors: google.cloud.compute.v1.IErrors[];

                    /**
                     * Creates a new Error instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Error instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IError): google.cloud.compute.v1.Error;

                    /**
                     * Encodes the specified Error message. Does not implicitly {@link google.cloud.compute.v1.Error.verify|verify} messages.
                     * @param message Error message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IError, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Error message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Error.verify|verify} messages.
                     * @param message Error message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IError, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an Error message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Error
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Error;

                    /**
                     * Decodes an Error message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Error
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Error;

                    /**
                     * Verifies an Error message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an Error message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Error
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Error;

                    /**
                     * Creates a plain object from an Error message. Also converts values to other types if specified.
                     * @param message Error
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Error, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Error to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a Warnings. */
                interface IWarnings {

                    /** Warnings code */
                    code?: (google.cloud.compute.v1.Warnings.Code|null);

                    /** Warnings data */
                    data?: (google.cloud.compute.v1.IData[]|null);

                    /** Warnings message */
                    message?: (string|null);
                }

                /** Represents a Warnings. */
                class Warnings implements IWarnings {

                    /**
                     * Constructs a new Warnings.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IWarnings);

                    /** Warnings code. */
                    public code?: (google.cloud.compute.v1.Warnings.Code|null);

                    /** Warnings data. */
                    public data: google.cloud.compute.v1.IData[];

                    /** Warnings message. */
                    public message?: (string|null);

                    /** Warnings _code. */
                    public _code?: "code";

                    /** Warnings _message. */
                    public _message?: "message";

                    /**
                     * Creates a new Warnings instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Warnings instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IWarnings): google.cloud.compute.v1.Warnings;

                    /**
                     * Encodes the specified Warnings message. Does not implicitly {@link google.cloud.compute.v1.Warnings.verify|verify} messages.
                     * @param message Warnings message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IWarnings, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Warnings message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Warnings.verify|verify} messages.
                     * @param message Warnings message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IWarnings, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Warnings message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Warnings
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Warnings;

                    /**
                     * Decodes a Warnings message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Warnings
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Warnings;

                    /**
                     * Verifies a Warnings message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Warnings message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Warnings
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Warnings;

                    /**
                     * Creates a plain object from a Warnings message. Also converts values to other types if specified.
                     * @param message Warnings
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Warnings, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Warnings to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                namespace Warnings {

                    /** Code enum. */
                    enum Code {
                        UNDEFINED_CODE = 0,
                        CLEANUP_FAILED = 150308440,
                        DEPRECATED_RESOURCE_USED = 391835586,
                        DEPRECATED_TYPE_USED = 346526230,
                        DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 369442967,
                        EXPERIMENTAL_TYPE_USED = 451954443,
                        EXTERNAL_API_WARNING = 175546307,
                        FIELD_VALUE_OVERRIDEN = 329669423,
                        INJECTED_KERNELS_DEPRECATED = 417377419,
                        MISSING_TYPE_DEPENDENCY = 344505463,
                        NEXT_HOP_ADDRESS_NOT_ASSIGNED = 324964999,
                        NEXT_HOP_CANNOT_IP_FORWARD = 383382887,
                        NEXT_HOP_INSTANCE_NOT_FOUND = 464250446,
                        NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 243758146,
                        NEXT_HOP_NOT_RUNNING = 417081265,
                        NOT_CRITICAL_ERROR = 105763924,
                        NO_RESULTS_ON_PAGE = 30036744,
                        REQUIRED_TOS_AGREEMENT = 3745539,
                        RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 496728641,
                        RESOURCE_NOT_DELETED = 168598460,
                        SCHEMA_VALIDATION_IGNORED = 275245642,
                        SINGLE_INSTANCE_PROPERTY_TEMPLATE = 268305617,
                        UNDECLARED_PROPERTIES = 390513439,
                        UNREACHABLE = 13328052
                    }
                }

                /** Properties of a Warning. */
                interface IWarning {

                    /** Warning code */
                    code?: (google.cloud.compute.v1.Warning.Code|null);

                    /** Warning data */
                    data?: (google.cloud.compute.v1.IData[]|null);

                    /** Warning message */
                    message?: (string|null);
                }

                /** Represents a Warning. */
                class Warning implements IWarning {

                    /**
                     * Constructs a new Warning.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IWarning);

                    /** Warning code. */
                    public code?: (google.cloud.compute.v1.Warning.Code|null);

                    /** Warning data. */
                    public data: google.cloud.compute.v1.IData[];

                    /** Warning message. */
                    public message?: (string|null);

                    /** Warning _code. */
                    public _code?: "code";

                    /** Warning _message. */
                    public _message?: "message";

                    /**
                     * Creates a new Warning instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Warning instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IWarning): google.cloud.compute.v1.Warning;

                    /**
                     * Encodes the specified Warning message. Does not implicitly {@link google.cloud.compute.v1.Warning.verify|verify} messages.
                     * @param message Warning message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IWarning, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Warning message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Warning.verify|verify} messages.
                     * @param message Warning message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IWarning, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Warning message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Warning
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Warning;

                    /**
                     * Decodes a Warning message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Warning
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Warning;

                    /**
                     * Verifies a Warning message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Warning message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Warning
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Warning;

                    /**
                     * Creates a plain object from a Warning message. Also converts values to other types if specified.
                     * @param message Warning
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Warning, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Warning to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                namespace Warning {

                    /** Code enum. */
                    enum Code {
                        UNDEFINED_CODE = 0,
                        CLEANUP_FAILED = 150308440,
                        DEPRECATED_RESOURCE_USED = 391835586,
                        DEPRECATED_TYPE_USED = 346526230,
                        DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 369442967,
                        EXPERIMENTAL_TYPE_USED = 451954443,
                        EXTERNAL_API_WARNING = 175546307,
                        FIELD_VALUE_OVERRIDEN = 329669423,
                        INJECTED_KERNELS_DEPRECATED = 417377419,
                        LARGE_DEPLOYMENT_WARNING = 481440678,
                        MISSING_TYPE_DEPENDENCY = 344505463,
                        NEXT_HOP_ADDRESS_NOT_ASSIGNED = 324964999,
                        NEXT_HOP_CANNOT_IP_FORWARD = 383382887,
                        NEXT_HOP_INSTANCE_NOT_FOUND = 464250446,
                        NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 243758146,
                        NEXT_HOP_NOT_RUNNING = 417081265,
                        NOT_CRITICAL_ERROR = 105763924,
                        NO_RESULTS_ON_PAGE = 30036744,
                        PARTIAL_SUCCESS = 39966469,
                        REQUIRED_TOS_AGREEMENT = 3745539,
                        RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 496728641,
                        RESOURCE_NOT_DELETED = 168598460,
                        SCHEMA_VALIDATION_IGNORED = 275245642,
                        SINGLE_INSTANCE_PROPERTY_TEMPLATE = 268305617,
                        UNDECLARED_PROPERTIES = 390513439,
                        UNREACHABLE = 13328052
                    }
                }

                /** Properties of a Data. */
                interface IData {

                    /** Data key */
                    key?: (string|null);

                    /** Data value */
                    value?: (string|null);
                }

                /** Represents a Data. */
                class Data implements IData {

                    /**
                     * Constructs a new Data.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IData);

                    /** Data key. */
                    public key?: (string|null);

                    /** Data value. */
                    public value?: (string|null);

                    /** Data _key. */
                    public _key?: "key";

                    /** Data _value. */
                    public _value?: "value";

                    /**
                     * Creates a new Data instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns Data instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IData): google.cloud.compute.v1.Data;

                    /**
                     * Encodes the specified Data message. Does not implicitly {@link google.cloud.compute.v1.Data.verify|verify} messages.
                     * @param message Data message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IData, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified Data message, length delimited. Does not implicitly {@link google.cloud.compute.v1.Data.verify|verify} messages.
                     * @param message Data message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IData, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a Data message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns Data
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.Data;

                    /**
                     * Decodes a Data message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns Data
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.Data;

                    /**
                     * Verifies a Data message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a Data message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns Data
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.Data;

                    /**
                     * Creates a plain object from a Data message. Also converts values to other types if specified.
                     * @param message Data
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.Data, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this Data to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of an OperationsScopedList. */
                interface IOperationsScopedList {

                    /** OperationsScopedList operations */
                    operations?: (google.cloud.compute.v1.IOperation[]|null);

                    /** OperationsScopedList warning */
                    warning?: (google.cloud.compute.v1.IWarning|null);
                }

                /** Represents an OperationsScopedList. */
                class OperationsScopedList implements IOperationsScopedList {

                    /**
                     * Constructs a new OperationsScopedList.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IOperationsScopedList);

                    /** OperationsScopedList operations. */
                    public operations: google.cloud.compute.v1.IOperation[];

                    /** OperationsScopedList warning. */
                    public warning?: (google.cloud.compute.v1.IWarning|null);

                    /** OperationsScopedList _warning. */
                    public _warning?: "warning";

                    /**
                     * Creates a new OperationsScopedList instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns OperationsScopedList instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IOperationsScopedList): google.cloud.compute.v1.OperationsScopedList;

                    /**
                     * Encodes the specified OperationsScopedList message. Does not implicitly {@link google.cloud.compute.v1.OperationsScopedList.verify|verify} messages.
                     * @param message OperationsScopedList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IOperationsScopedList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified OperationsScopedList message, length delimited. Does not implicitly {@link google.cloud.compute.v1.OperationsScopedList.verify|verify} messages.
                     * @param message OperationsScopedList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IOperationsScopedList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an OperationsScopedList message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns OperationsScopedList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.OperationsScopedList;

                    /**
                     * Decodes an OperationsScopedList message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns OperationsScopedList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.OperationsScopedList;

                    /**
                     * Verifies an OperationsScopedList message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an OperationsScopedList message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns OperationsScopedList
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.OperationsScopedList;

                    /**
                     * Creates a plain object from an OperationsScopedList message. Also converts values to other types if specified.
                     * @param message OperationsScopedList
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.OperationsScopedList, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this OperationsScopedList to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of an OperationAggregatedList. */
                interface IOperationAggregatedList {

                    /** OperationAggregatedList id */
                    id?: (string|null);

                    /** OperationAggregatedList items */
                    items?: ({ [k: string]: google.cloud.compute.v1.IOperationsScopedList }|null);

                    /** OperationAggregatedList kind */
                    kind?: (string|null);

                    /** OperationAggregatedList nextPageToken */
                    nextPageToken?: (string|null);

                    /** OperationAggregatedList selfLink */
                    selfLink?: (string|null);

                    /** OperationAggregatedList unreachables */
                    unreachables?: (string[]|null);

                    /** OperationAggregatedList warning */
                    warning?: (google.cloud.compute.v1.IWarning|null);
                }

                /** Represents an OperationAggregatedList. */
                class OperationAggregatedList implements IOperationAggregatedList {

                    /**
                     * Constructs a new OperationAggregatedList.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IOperationAggregatedList);

                    /** OperationAggregatedList id. */
                    public id?: (string|null);

                    /** OperationAggregatedList items. */
                    public items: { [k: string]: google.cloud.compute.v1.IOperationsScopedList };

                    /** OperationAggregatedList kind. */
                    public kind?: (string|null);

                    /** OperationAggregatedList nextPageToken. */
                    public nextPageToken?: (string|null);

                    /** OperationAggregatedList selfLink. */
                    public selfLink?: (string|null);

                    /** OperationAggregatedList unreachables. */
                    public unreachables: string[];

                    /** OperationAggregatedList warning. */
                    public warning?: (google.cloud.compute.v1.IWarning|null);

                    /** OperationAggregatedList _id. */
                    public _id?: "id";

                    /** OperationAggregatedList _kind. */
                    public _kind?: "kind";

                    /** OperationAggregatedList _nextPageToken. */
                    public _nextPageToken?: "nextPageToken";

                    /** OperationAggregatedList _selfLink. */
                    public _selfLink?: "selfLink";

                    /** OperationAggregatedList _warning. */
                    public _warning?: "warning";

                    /**
                     * Creates a new OperationAggregatedList instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns OperationAggregatedList instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IOperationAggregatedList): google.cloud.compute.v1.OperationAggregatedList;

                    /**
                     * Encodes the specified OperationAggregatedList message. Does not implicitly {@link google.cloud.compute.v1.OperationAggregatedList.verify|verify} messages.
                     * @param message OperationAggregatedList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IOperationAggregatedList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified OperationAggregatedList message, length delimited. Does not implicitly {@link google.cloud.compute.v1.OperationAggregatedList.verify|verify} messages.
                     * @param message OperationAggregatedList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IOperationAggregatedList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an OperationAggregatedList message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns OperationAggregatedList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.OperationAggregatedList;

                    /**
                     * Decodes an OperationAggregatedList message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns OperationAggregatedList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.OperationAggregatedList;

                    /**
                     * Verifies an OperationAggregatedList message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an OperationAggregatedList message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns OperationAggregatedList
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.OperationAggregatedList;

                    /**
                     * Creates a plain object from an OperationAggregatedList message. Also converts values to other types if specified.
                     * @param message OperationAggregatedList
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.OperationAggregatedList, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this OperationAggregatedList to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a GetRegionOperationRequest. */
                interface IGetRegionOperationRequest {

                    /** GetRegionOperationRequest operation */
                    operation?: (string|null);

                    /** GetRegionOperationRequest project */
                    project?: (string|null);

                    /** GetRegionOperationRequest region */
                    region?: (string|null);
                }

                /** Represents a GetRegionOperationRequest. */
                class GetRegionOperationRequest implements IGetRegionOperationRequest {

                    /**
                     * Constructs a new GetRegionOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IGetRegionOperationRequest);

                    /** GetRegionOperationRequest operation. */
                    public operation: string;

                    /** GetRegionOperationRequest project. */
                    public project: string;

                    /** GetRegionOperationRequest region. */
                    public region: string;

                    /**
                     * Creates a new GetRegionOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GetRegionOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IGetRegionOperationRequest): google.cloud.compute.v1.GetRegionOperationRequest;

                    /**
                     * Encodes the specified GetRegionOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.GetRegionOperationRequest.verify|verify} messages.
                     * @param message GetRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IGetRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GetRegionOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.GetRegionOperationRequest.verify|verify} messages.
                     * @param message GetRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IGetRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GetRegionOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GetRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.GetRegionOperationRequest;

                    /**
                     * Decodes a GetRegionOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GetRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.GetRegionOperationRequest;

                    /**
                     * Verifies a GetRegionOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GetRegionOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GetRegionOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.GetRegionOperationRequest;

                    /**
                     * Creates a plain object from a GetRegionOperationRequest message. Also converts values to other types if specified.
                     * @param message GetRegionOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.GetRegionOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GetRegionOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteRegionOperationRequest. */
                interface IDeleteRegionOperationRequest {

                    /** DeleteRegionOperationRequest operation */
                    operation?: (string|null);

                    /** DeleteRegionOperationRequest project */
                    project?: (string|null);

                    /** DeleteRegionOperationRequest region */
                    region?: (string|null);
                }

                /** Represents a DeleteRegionOperationRequest. */
                class DeleteRegionOperationRequest implements IDeleteRegionOperationRequest {

                    /**
                     * Constructs a new DeleteRegionOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteRegionOperationRequest);

                    /** DeleteRegionOperationRequest operation. */
                    public operation: string;

                    /** DeleteRegionOperationRequest project. */
                    public project: string;

                    /** DeleteRegionOperationRequest region. */
                    public region: string;

                    /**
                     * Creates a new DeleteRegionOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteRegionOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteRegionOperationRequest): google.cloud.compute.v1.DeleteRegionOperationRequest;

                    /**
                     * Encodes the specified DeleteRegionOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.DeleteRegionOperationRequest.verify|verify} messages.
                     * @param message DeleteRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteRegionOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteRegionOperationRequest.verify|verify} messages.
                     * @param message DeleteRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteRegionOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteRegionOperationRequest;

                    /**
                     * Decodes a DeleteRegionOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteRegionOperationRequest;

                    /**
                     * Verifies a DeleteRegionOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteRegionOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteRegionOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteRegionOperationRequest;

                    /**
                     * Creates a plain object from a DeleteRegionOperationRequest message. Also converts values to other types if specified.
                     * @param message DeleteRegionOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteRegionOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteRegionOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteRegionOperationResponse. */
                interface IDeleteRegionOperationResponse {
                }

                /** Represents a DeleteRegionOperationResponse. */
                class DeleteRegionOperationResponse implements IDeleteRegionOperationResponse {

                    /**
                     * Constructs a new DeleteRegionOperationResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteRegionOperationResponse);

                    /**
                     * Creates a new DeleteRegionOperationResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteRegionOperationResponse instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteRegionOperationResponse): google.cloud.compute.v1.DeleteRegionOperationResponse;

                    /**
                     * Encodes the specified DeleteRegionOperationResponse message. Does not implicitly {@link google.cloud.compute.v1.DeleteRegionOperationResponse.verify|verify} messages.
                     * @param message DeleteRegionOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteRegionOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteRegionOperationResponse message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteRegionOperationResponse.verify|verify} messages.
                     * @param message DeleteRegionOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteRegionOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteRegionOperationResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteRegionOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteRegionOperationResponse;

                    /**
                     * Decodes a DeleteRegionOperationResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteRegionOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteRegionOperationResponse;

                    /**
                     * Verifies a DeleteRegionOperationResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteRegionOperationResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteRegionOperationResponse
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteRegionOperationResponse;

                    /**
                     * Creates a plain object from a DeleteRegionOperationResponse message. Also converts values to other types if specified.
                     * @param message DeleteRegionOperationResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteRegionOperationResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteRegionOperationResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a ListRegionOperationsRequest. */
                interface IListRegionOperationsRequest {

                    /** ListRegionOperationsRequest filter */
                    filter?: (string|null);

                    /** ListRegionOperationsRequest maxResults */
                    maxResults?: (number|null);

                    /** ListRegionOperationsRequest orderBy */
                    orderBy?: (string|null);

                    /** ListRegionOperationsRequest pageToken */
                    pageToken?: (string|null);

                    /** ListRegionOperationsRequest project */
                    project?: (string|null);

                    /** ListRegionOperationsRequest region */
                    region?: (string|null);

                    /** ListRegionOperationsRequest returnPartialSuccess */
                    returnPartialSuccess?: (boolean|null);
                }

                /** Represents a ListRegionOperationsRequest. */
                class ListRegionOperationsRequest implements IListRegionOperationsRequest {

                    /**
                     * Constructs a new ListRegionOperationsRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IListRegionOperationsRequest);

                    /** ListRegionOperationsRequest filter. */
                    public filter?: (string|null);

                    /** ListRegionOperationsRequest maxResults. */
                    public maxResults?: (number|null);

                    /** ListRegionOperationsRequest orderBy. */
                    public orderBy?: (string|null);

                    /** ListRegionOperationsRequest pageToken. */
                    public pageToken?: (string|null);

                    /** ListRegionOperationsRequest project. */
                    public project: string;

                    /** ListRegionOperationsRequest region. */
                    public region: string;

                    /** ListRegionOperationsRequest returnPartialSuccess. */
                    public returnPartialSuccess?: (boolean|null);

                    /** ListRegionOperationsRequest _filter. */
                    public _filter?: "filter";

                    /** ListRegionOperationsRequest _maxResults. */
                    public _maxResults?: "maxResults";

                    /** ListRegionOperationsRequest _orderBy. */
                    public _orderBy?: "orderBy";

                    /** ListRegionOperationsRequest _pageToken. */
                    public _pageToken?: "pageToken";

                    /** ListRegionOperationsRequest _returnPartialSuccess. */
                    public _returnPartialSuccess?: "returnPartialSuccess";

                    /**
                     * Creates a new ListRegionOperationsRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ListRegionOperationsRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IListRegionOperationsRequest): google.cloud.compute.v1.ListRegionOperationsRequest;

                    /**
                     * Encodes the specified ListRegionOperationsRequest message. Does not implicitly {@link google.cloud.compute.v1.ListRegionOperationsRequest.verify|verify} messages.
                     * @param message ListRegionOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IListRegionOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ListRegionOperationsRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.ListRegionOperationsRequest.verify|verify} messages.
                     * @param message ListRegionOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IListRegionOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ListRegionOperationsRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ListRegionOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.ListRegionOperationsRequest;

                    /**
                     * Decodes a ListRegionOperationsRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ListRegionOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.ListRegionOperationsRequest;

                    /**
                     * Verifies a ListRegionOperationsRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ListRegionOperationsRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ListRegionOperationsRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.ListRegionOperationsRequest;

                    /**
                     * Creates a plain object from a ListRegionOperationsRequest message. Also converts values to other types if specified.
                     * @param message ListRegionOperationsRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.ListRegionOperationsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ListRegionOperationsRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of an OperationList. */
                interface IOperationList {

                    /** OperationList id */
                    id?: (string|null);

                    /** OperationList items */
                    items?: (google.cloud.compute.v1.IOperation[]|null);

                    /** OperationList kind */
                    kind?: (string|null);

                    /** OperationList nextPageToken */
                    nextPageToken?: (string|null);

                    /** OperationList selfLink */
                    selfLink?: (string|null);

                    /** OperationList warning */
                    warning?: (google.cloud.compute.v1.IWarning|null);
                }

                /** Represents an OperationList. */
                class OperationList implements IOperationList {

                    /**
                     * Constructs a new OperationList.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IOperationList);

                    /** OperationList id. */
                    public id?: (string|null);

                    /** OperationList items. */
                    public items: google.cloud.compute.v1.IOperation[];

                    /** OperationList kind. */
                    public kind?: (string|null);

                    /** OperationList nextPageToken. */
                    public nextPageToken?: (string|null);

                    /** OperationList selfLink. */
                    public selfLink?: (string|null);

                    /** OperationList warning. */
                    public warning?: (google.cloud.compute.v1.IWarning|null);

                    /** OperationList _id. */
                    public _id?: "id";

                    /** OperationList _kind. */
                    public _kind?: "kind";

                    /** OperationList _nextPageToken. */
                    public _nextPageToken?: "nextPageToken";

                    /** OperationList _selfLink. */
                    public _selfLink?: "selfLink";

                    /** OperationList _warning. */
                    public _warning?: "warning";

                    /**
                     * Creates a new OperationList instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns OperationList instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IOperationList): google.cloud.compute.v1.OperationList;

                    /**
                     * Encodes the specified OperationList message. Does not implicitly {@link google.cloud.compute.v1.OperationList.verify|verify} messages.
                     * @param message OperationList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IOperationList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified OperationList message, length delimited. Does not implicitly {@link google.cloud.compute.v1.OperationList.verify|verify} messages.
                     * @param message OperationList message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IOperationList, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an OperationList message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns OperationList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.OperationList;

                    /**
                     * Decodes an OperationList message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns OperationList
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.OperationList;

                    /**
                     * Verifies an OperationList message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an OperationList message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns OperationList
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.OperationList;

                    /**
                     * Creates a plain object from an OperationList message. Also converts values to other types if specified.
                     * @param message OperationList
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.OperationList, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this OperationList to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a WaitRegionOperationRequest. */
                interface IWaitRegionOperationRequest {

                    /** WaitRegionOperationRequest operation */
                    operation?: (string|null);

                    /** WaitRegionOperationRequest project */
                    project?: (string|null);

                    /** WaitRegionOperationRequest region */
                    region?: (string|null);
                }

                /** Represents a WaitRegionOperationRequest. */
                class WaitRegionOperationRequest implements IWaitRegionOperationRequest {

                    /**
                     * Constructs a new WaitRegionOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IWaitRegionOperationRequest);

                    /** WaitRegionOperationRequest operation. */
                    public operation: string;

                    /** WaitRegionOperationRequest project. */
                    public project: string;

                    /** WaitRegionOperationRequest region. */
                    public region: string;

                    /**
                     * Creates a new WaitRegionOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns WaitRegionOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IWaitRegionOperationRequest): google.cloud.compute.v1.WaitRegionOperationRequest;

                    /**
                     * Encodes the specified WaitRegionOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.WaitRegionOperationRequest.verify|verify} messages.
                     * @param message WaitRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IWaitRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified WaitRegionOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.WaitRegionOperationRequest.verify|verify} messages.
                     * @param message WaitRegionOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IWaitRegionOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a WaitRegionOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns WaitRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.WaitRegionOperationRequest;

                    /**
                     * Decodes a WaitRegionOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns WaitRegionOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.WaitRegionOperationRequest;

                    /**
                     * Verifies a WaitRegionOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a WaitRegionOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns WaitRegionOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.WaitRegionOperationRequest;

                    /**
                     * Creates a plain object from a WaitRegionOperationRequest message. Also converts values to other types if specified.
                     * @param message WaitRegionOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.WaitRegionOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this WaitRegionOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteZoneOperationRequest. */
                interface IDeleteZoneOperationRequest {

                    /** DeleteZoneOperationRequest operation */
                    operation?: (string|null);

                    /** DeleteZoneOperationRequest project */
                    project?: (string|null);

                    /** DeleteZoneOperationRequest zone */
                    zone?: (string|null);
                }

                /** Represents a DeleteZoneOperationRequest. */
                class DeleteZoneOperationRequest implements IDeleteZoneOperationRequest {

                    /**
                     * Constructs a new DeleteZoneOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteZoneOperationRequest);

                    /** DeleteZoneOperationRequest operation. */
                    public operation: string;

                    /** DeleteZoneOperationRequest project. */
                    public project: string;

                    /** DeleteZoneOperationRequest zone. */
                    public zone: string;

                    /**
                     * Creates a new DeleteZoneOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteZoneOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteZoneOperationRequest): google.cloud.compute.v1.DeleteZoneOperationRequest;

                    /**
                     * Encodes the specified DeleteZoneOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.DeleteZoneOperationRequest.verify|verify} messages.
                     * @param message DeleteZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteZoneOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteZoneOperationRequest.verify|verify} messages.
                     * @param message DeleteZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteZoneOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteZoneOperationRequest;

                    /**
                     * Decodes a DeleteZoneOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteZoneOperationRequest;

                    /**
                     * Verifies a DeleteZoneOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteZoneOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteZoneOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteZoneOperationRequest;

                    /**
                     * Creates a plain object from a DeleteZoneOperationRequest message. Also converts values to other types if specified.
                     * @param message DeleteZoneOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteZoneOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteZoneOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteZoneOperationResponse. */
                interface IDeleteZoneOperationResponse {
                }

                /** Represents a DeleteZoneOperationResponse. */
                class DeleteZoneOperationResponse implements IDeleteZoneOperationResponse {

                    /**
                     * Constructs a new DeleteZoneOperationResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteZoneOperationResponse);

                    /**
                     * Creates a new DeleteZoneOperationResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteZoneOperationResponse instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteZoneOperationResponse): google.cloud.compute.v1.DeleteZoneOperationResponse;

                    /**
                     * Encodes the specified DeleteZoneOperationResponse message. Does not implicitly {@link google.cloud.compute.v1.DeleteZoneOperationResponse.verify|verify} messages.
                     * @param message DeleteZoneOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteZoneOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteZoneOperationResponse message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteZoneOperationResponse.verify|verify} messages.
                     * @param message DeleteZoneOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteZoneOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteZoneOperationResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteZoneOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteZoneOperationResponse;

                    /**
                     * Decodes a DeleteZoneOperationResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteZoneOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteZoneOperationResponse;

                    /**
                     * Verifies a DeleteZoneOperationResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteZoneOperationResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteZoneOperationResponse
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteZoneOperationResponse;

                    /**
                     * Creates a plain object from a DeleteZoneOperationResponse message. Also converts values to other types if specified.
                     * @param message DeleteZoneOperationResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteZoneOperationResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteZoneOperationResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a GetZoneOperationRequest. */
                interface IGetZoneOperationRequest {

                    /** GetZoneOperationRequest operation */
                    operation?: (string|null);

                    /** GetZoneOperationRequest project */
                    project?: (string|null);

                    /** GetZoneOperationRequest zone */
                    zone?: (string|null);
                }

                /** Represents a GetZoneOperationRequest. */
                class GetZoneOperationRequest implements IGetZoneOperationRequest {

                    /**
                     * Constructs a new GetZoneOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IGetZoneOperationRequest);

                    /** GetZoneOperationRequest operation. */
                    public operation: string;

                    /** GetZoneOperationRequest project. */
                    public project: string;

                    /** GetZoneOperationRequest zone. */
                    public zone: string;

                    /**
                     * Creates a new GetZoneOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GetZoneOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IGetZoneOperationRequest): google.cloud.compute.v1.GetZoneOperationRequest;

                    /**
                     * Encodes the specified GetZoneOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.GetZoneOperationRequest.verify|verify} messages.
                     * @param message GetZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IGetZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GetZoneOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.GetZoneOperationRequest.verify|verify} messages.
                     * @param message GetZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IGetZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GetZoneOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GetZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.GetZoneOperationRequest;

                    /**
                     * Decodes a GetZoneOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GetZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.GetZoneOperationRequest;

                    /**
                     * Verifies a GetZoneOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GetZoneOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GetZoneOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.GetZoneOperationRequest;

                    /**
                     * Creates a plain object from a GetZoneOperationRequest message. Also converts values to other types if specified.
                     * @param message GetZoneOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.GetZoneOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GetZoneOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a ListZoneOperationsRequest. */
                interface IListZoneOperationsRequest {

                    /** ListZoneOperationsRequest filter */
                    filter?: (string|null);

                    /** ListZoneOperationsRequest maxResults */
                    maxResults?: (number|null);

                    /** ListZoneOperationsRequest orderBy */
                    orderBy?: (string|null);

                    /** ListZoneOperationsRequest pageToken */
                    pageToken?: (string|null);

                    /** ListZoneOperationsRequest project */
                    project?: (string|null);

                    /** ListZoneOperationsRequest returnPartialSuccess */
                    returnPartialSuccess?: (boolean|null);

                    /** ListZoneOperationsRequest zone */
                    zone?: (string|null);
                }

                /** Represents a ListZoneOperationsRequest. */
                class ListZoneOperationsRequest implements IListZoneOperationsRequest {

                    /**
                     * Constructs a new ListZoneOperationsRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IListZoneOperationsRequest);

                    /** ListZoneOperationsRequest filter. */
                    public filter?: (string|null);

                    /** ListZoneOperationsRequest maxResults. */
                    public maxResults?: (number|null);

                    /** ListZoneOperationsRequest orderBy. */
                    public orderBy?: (string|null);

                    /** ListZoneOperationsRequest pageToken. */
                    public pageToken?: (string|null);

                    /** ListZoneOperationsRequest project. */
                    public project: string;

                    /** ListZoneOperationsRequest returnPartialSuccess. */
                    public returnPartialSuccess?: (boolean|null);

                    /** ListZoneOperationsRequest zone. */
                    public zone: string;

                    /** ListZoneOperationsRequest _filter. */
                    public _filter?: "filter";

                    /** ListZoneOperationsRequest _maxResults. */
                    public _maxResults?: "maxResults";

                    /** ListZoneOperationsRequest _orderBy. */
                    public _orderBy?: "orderBy";

                    /** ListZoneOperationsRequest _pageToken. */
                    public _pageToken?: "pageToken";

                    /** ListZoneOperationsRequest _returnPartialSuccess. */
                    public _returnPartialSuccess?: "returnPartialSuccess";

                    /**
                     * Creates a new ListZoneOperationsRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ListZoneOperationsRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IListZoneOperationsRequest): google.cloud.compute.v1.ListZoneOperationsRequest;

                    /**
                     * Encodes the specified ListZoneOperationsRequest message. Does not implicitly {@link google.cloud.compute.v1.ListZoneOperationsRequest.verify|verify} messages.
                     * @param message ListZoneOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IListZoneOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ListZoneOperationsRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.ListZoneOperationsRequest.verify|verify} messages.
                     * @param message ListZoneOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IListZoneOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ListZoneOperationsRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ListZoneOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.ListZoneOperationsRequest;

                    /**
                     * Decodes a ListZoneOperationsRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ListZoneOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.ListZoneOperationsRequest;

                    /**
                     * Verifies a ListZoneOperationsRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ListZoneOperationsRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ListZoneOperationsRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.ListZoneOperationsRequest;

                    /**
                     * Creates a plain object from a ListZoneOperationsRequest message. Also converts values to other types if specified.
                     * @param message ListZoneOperationsRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.ListZoneOperationsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ListZoneOperationsRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a WaitZoneOperationRequest. */
                interface IWaitZoneOperationRequest {

                    /** WaitZoneOperationRequest operation */
                    operation?: (string|null);

                    /** WaitZoneOperationRequest project */
                    project?: (string|null);

                    /** WaitZoneOperationRequest zone */
                    zone?: (string|null);
                }

                /** Represents a WaitZoneOperationRequest. */
                class WaitZoneOperationRequest implements IWaitZoneOperationRequest {

                    /**
                     * Constructs a new WaitZoneOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IWaitZoneOperationRequest);

                    /** WaitZoneOperationRequest operation. */
                    public operation: string;

                    /** WaitZoneOperationRequest project. */
                    public project: string;

                    /** WaitZoneOperationRequest zone. */
                    public zone: string;

                    /**
                     * Creates a new WaitZoneOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns WaitZoneOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IWaitZoneOperationRequest): google.cloud.compute.v1.WaitZoneOperationRequest;

                    /**
                     * Encodes the specified WaitZoneOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.WaitZoneOperationRequest.verify|verify} messages.
                     * @param message WaitZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IWaitZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified WaitZoneOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.WaitZoneOperationRequest.verify|verify} messages.
                     * @param message WaitZoneOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IWaitZoneOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a WaitZoneOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns WaitZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.WaitZoneOperationRequest;

                    /**
                     * Decodes a WaitZoneOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns WaitZoneOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.WaitZoneOperationRequest;

                    /**
                     * Verifies a WaitZoneOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a WaitZoneOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns WaitZoneOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.WaitZoneOperationRequest;

                    /**
                     * Creates a plain object from a WaitZoneOperationRequest message. Also converts values to other types if specified.
                     * @param message WaitZoneOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.WaitZoneOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this WaitZoneOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of an AggregatedListGlobalOperationsRequest. */
                interface IAggregatedListGlobalOperationsRequest {

                    /** AggregatedListGlobalOperationsRequest filter */
                    filter?: (string|null);

                    /** AggregatedListGlobalOperationsRequest includeAllScopes */
                    includeAllScopes?: (boolean|null);

                    /** AggregatedListGlobalOperationsRequest maxResults */
                    maxResults?: (number|null);

                    /** AggregatedListGlobalOperationsRequest orderBy */
                    orderBy?: (string|null);

                    /** AggregatedListGlobalOperationsRequest pageToken */
                    pageToken?: (string|null);

                    /** AggregatedListGlobalOperationsRequest project */
                    project?: (string|null);

                    /** AggregatedListGlobalOperationsRequest returnPartialSuccess */
                    returnPartialSuccess?: (boolean|null);
                }

                /** Represents an AggregatedListGlobalOperationsRequest. */
                class AggregatedListGlobalOperationsRequest implements IAggregatedListGlobalOperationsRequest {

                    /**
                     * Constructs a new AggregatedListGlobalOperationsRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest);

                    /** AggregatedListGlobalOperationsRequest filter. */
                    public filter?: (string|null);

                    /** AggregatedListGlobalOperationsRequest includeAllScopes. */
                    public includeAllScopes?: (boolean|null);

                    /** AggregatedListGlobalOperationsRequest maxResults. */
                    public maxResults?: (number|null);

                    /** AggregatedListGlobalOperationsRequest orderBy. */
                    public orderBy?: (string|null);

                    /** AggregatedListGlobalOperationsRequest pageToken. */
                    public pageToken?: (string|null);

                    /** AggregatedListGlobalOperationsRequest project. */
                    public project: string;

                    /** AggregatedListGlobalOperationsRequest returnPartialSuccess. */
                    public returnPartialSuccess?: (boolean|null);

                    /** AggregatedListGlobalOperationsRequest _filter. */
                    public _filter?: "filter";

                    /** AggregatedListGlobalOperationsRequest _includeAllScopes. */
                    public _includeAllScopes?: "includeAllScopes";

                    /** AggregatedListGlobalOperationsRequest _maxResults. */
                    public _maxResults?: "maxResults";

                    /** AggregatedListGlobalOperationsRequest _orderBy. */
                    public _orderBy?: "orderBy";

                    /** AggregatedListGlobalOperationsRequest _pageToken. */
                    public _pageToken?: "pageToken";

                    /** AggregatedListGlobalOperationsRequest _returnPartialSuccess. */
                    public _returnPartialSuccess?: "returnPartialSuccess";

                    /**
                     * Creates a new AggregatedListGlobalOperationsRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns AggregatedListGlobalOperationsRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest): google.cloud.compute.v1.AggregatedListGlobalOperationsRequest;

                    /**
                     * Encodes the specified AggregatedListGlobalOperationsRequest message. Does not implicitly {@link google.cloud.compute.v1.AggregatedListGlobalOperationsRequest.verify|verify} messages.
                     * @param message AggregatedListGlobalOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified AggregatedListGlobalOperationsRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.AggregatedListGlobalOperationsRequest.verify|verify} messages.
                     * @param message AggregatedListGlobalOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes an AggregatedListGlobalOperationsRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns AggregatedListGlobalOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.AggregatedListGlobalOperationsRequest;

                    /**
                     * Decodes an AggregatedListGlobalOperationsRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns AggregatedListGlobalOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.AggregatedListGlobalOperationsRequest;

                    /**
                     * Verifies an AggregatedListGlobalOperationsRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates an AggregatedListGlobalOperationsRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns AggregatedListGlobalOperationsRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.AggregatedListGlobalOperationsRequest;

                    /**
                     * Creates a plain object from an AggregatedListGlobalOperationsRequest message. Also converts values to other types if specified.
                     * @param message AggregatedListGlobalOperationsRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.AggregatedListGlobalOperationsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this AggregatedListGlobalOperationsRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteGlobalOperationRequest. */
                interface IDeleteGlobalOperationRequest {

                    /** DeleteGlobalOperationRequest operation */
                    operation?: (string|null);

                    /** DeleteGlobalOperationRequest project */
                    project?: (string|null);
                }

                /** Represents a DeleteGlobalOperationRequest. */
                class DeleteGlobalOperationRequest implements IDeleteGlobalOperationRequest {

                    /**
                     * Constructs a new DeleteGlobalOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteGlobalOperationRequest);

                    /** DeleteGlobalOperationRequest operation. */
                    public operation: string;

                    /** DeleteGlobalOperationRequest project. */
                    public project: string;

                    /**
                     * Creates a new DeleteGlobalOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteGlobalOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteGlobalOperationRequest): google.cloud.compute.v1.DeleteGlobalOperationRequest;

                    /**
                     * Encodes the specified DeleteGlobalOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOperationRequest.verify|verify} messages.
                     * @param message DeleteGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteGlobalOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOperationRequest.verify|verify} messages.
                     * @param message DeleteGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteGlobalOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteGlobalOperationRequest;

                    /**
                     * Decodes a DeleteGlobalOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteGlobalOperationRequest;

                    /**
                     * Verifies a DeleteGlobalOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteGlobalOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteGlobalOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteGlobalOperationRequest;

                    /**
                     * Creates a plain object from a DeleteGlobalOperationRequest message. Also converts values to other types if specified.
                     * @param message DeleteGlobalOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteGlobalOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteGlobalOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteGlobalOperationResponse. */
                interface IDeleteGlobalOperationResponse {
                }

                /** Represents a DeleteGlobalOperationResponse. */
                class DeleteGlobalOperationResponse implements IDeleteGlobalOperationResponse {

                    /**
                     * Constructs a new DeleteGlobalOperationResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteGlobalOperationResponse);

                    /**
                     * Creates a new DeleteGlobalOperationResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteGlobalOperationResponse instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteGlobalOperationResponse): google.cloud.compute.v1.DeleteGlobalOperationResponse;

                    /**
                     * Encodes the specified DeleteGlobalOperationResponse message. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOperationResponse.verify|verify} messages.
                     * @param message DeleteGlobalOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteGlobalOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteGlobalOperationResponse message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOperationResponse.verify|verify} messages.
                     * @param message DeleteGlobalOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteGlobalOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteGlobalOperationResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteGlobalOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteGlobalOperationResponse;

                    /**
                     * Decodes a DeleteGlobalOperationResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteGlobalOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteGlobalOperationResponse;

                    /**
                     * Verifies a DeleteGlobalOperationResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteGlobalOperationResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteGlobalOperationResponse
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteGlobalOperationResponse;

                    /**
                     * Creates a plain object from a DeleteGlobalOperationResponse message. Also converts values to other types if specified.
                     * @param message DeleteGlobalOperationResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteGlobalOperationResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteGlobalOperationResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a GetGlobalOperationRequest. */
                interface IGetGlobalOperationRequest {

                    /** GetGlobalOperationRequest operation */
                    operation?: (string|null);

                    /** GetGlobalOperationRequest project */
                    project?: (string|null);
                }

                /** Represents a GetGlobalOperationRequest. */
                class GetGlobalOperationRequest implements IGetGlobalOperationRequest {

                    /**
                     * Constructs a new GetGlobalOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IGetGlobalOperationRequest);

                    /** GetGlobalOperationRequest operation. */
                    public operation: string;

                    /** GetGlobalOperationRequest project. */
                    public project: string;

                    /**
                     * Creates a new GetGlobalOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GetGlobalOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IGetGlobalOperationRequest): google.cloud.compute.v1.GetGlobalOperationRequest;

                    /**
                     * Encodes the specified GetGlobalOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.GetGlobalOperationRequest.verify|verify} messages.
                     * @param message GetGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IGetGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GetGlobalOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.GetGlobalOperationRequest.verify|verify} messages.
                     * @param message GetGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IGetGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GetGlobalOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GetGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.GetGlobalOperationRequest;

                    /**
                     * Decodes a GetGlobalOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GetGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.GetGlobalOperationRequest;

                    /**
                     * Verifies a GetGlobalOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GetGlobalOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GetGlobalOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.GetGlobalOperationRequest;

                    /**
                     * Creates a plain object from a GetGlobalOperationRequest message. Also converts values to other types if specified.
                     * @param message GetGlobalOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.GetGlobalOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GetGlobalOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a ListGlobalOperationsRequest. */
                interface IListGlobalOperationsRequest {

                    /** ListGlobalOperationsRequest filter */
                    filter?: (string|null);

                    /** ListGlobalOperationsRequest maxResults */
                    maxResults?: (number|null);

                    /** ListGlobalOperationsRequest orderBy */
                    orderBy?: (string|null);

                    /** ListGlobalOperationsRequest pageToken */
                    pageToken?: (string|null);

                    /** ListGlobalOperationsRequest project */
                    project?: (string|null);

                    /** ListGlobalOperationsRequest returnPartialSuccess */
                    returnPartialSuccess?: (boolean|null);
                }

                /** Represents a ListGlobalOperationsRequest. */
                class ListGlobalOperationsRequest implements IListGlobalOperationsRequest {

                    /**
                     * Constructs a new ListGlobalOperationsRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IListGlobalOperationsRequest);

                    /** ListGlobalOperationsRequest filter. */
                    public filter?: (string|null);

                    /** ListGlobalOperationsRequest maxResults. */
                    public maxResults?: (number|null);

                    /** ListGlobalOperationsRequest orderBy. */
                    public orderBy?: (string|null);

                    /** ListGlobalOperationsRequest pageToken. */
                    public pageToken?: (string|null);

                    /** ListGlobalOperationsRequest project. */
                    public project: string;

                    /** ListGlobalOperationsRequest returnPartialSuccess. */
                    public returnPartialSuccess?: (boolean|null);

                    /** ListGlobalOperationsRequest _filter. */
                    public _filter?: "filter";

                    /** ListGlobalOperationsRequest _maxResults. */
                    public _maxResults?: "maxResults";

                    /** ListGlobalOperationsRequest _orderBy. */
                    public _orderBy?: "orderBy";

                    /** ListGlobalOperationsRequest _pageToken. */
                    public _pageToken?: "pageToken";

                    /** ListGlobalOperationsRequest _returnPartialSuccess. */
                    public _returnPartialSuccess?: "returnPartialSuccess";

                    /**
                     * Creates a new ListGlobalOperationsRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ListGlobalOperationsRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IListGlobalOperationsRequest): google.cloud.compute.v1.ListGlobalOperationsRequest;

                    /**
                     * Encodes the specified ListGlobalOperationsRequest message. Does not implicitly {@link google.cloud.compute.v1.ListGlobalOperationsRequest.verify|verify} messages.
                     * @param message ListGlobalOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IListGlobalOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ListGlobalOperationsRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.ListGlobalOperationsRequest.verify|verify} messages.
                     * @param message ListGlobalOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IListGlobalOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ListGlobalOperationsRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ListGlobalOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.ListGlobalOperationsRequest;

                    /**
                     * Decodes a ListGlobalOperationsRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ListGlobalOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.ListGlobalOperationsRequest;

                    /**
                     * Verifies a ListGlobalOperationsRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ListGlobalOperationsRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ListGlobalOperationsRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.ListGlobalOperationsRequest;

                    /**
                     * Creates a plain object from a ListGlobalOperationsRequest message. Also converts values to other types if specified.
                     * @param message ListGlobalOperationsRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.ListGlobalOperationsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ListGlobalOperationsRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a WaitGlobalOperationRequest. */
                interface IWaitGlobalOperationRequest {

                    /** WaitGlobalOperationRequest operation */
                    operation?: (string|null);

                    /** WaitGlobalOperationRequest project */
                    project?: (string|null);
                }

                /** Represents a WaitGlobalOperationRequest. */
                class WaitGlobalOperationRequest implements IWaitGlobalOperationRequest {

                    /**
                     * Constructs a new WaitGlobalOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IWaitGlobalOperationRequest);

                    /** WaitGlobalOperationRequest operation. */
                    public operation: string;

                    /** WaitGlobalOperationRequest project. */
                    public project: string;

                    /**
                     * Creates a new WaitGlobalOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns WaitGlobalOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IWaitGlobalOperationRequest): google.cloud.compute.v1.WaitGlobalOperationRequest;

                    /**
                     * Encodes the specified WaitGlobalOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.WaitGlobalOperationRequest.verify|verify} messages.
                     * @param message WaitGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IWaitGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified WaitGlobalOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.WaitGlobalOperationRequest.verify|verify} messages.
                     * @param message WaitGlobalOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IWaitGlobalOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a WaitGlobalOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns WaitGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.WaitGlobalOperationRequest;

                    /**
                     * Decodes a WaitGlobalOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns WaitGlobalOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.WaitGlobalOperationRequest;

                    /**
                     * Verifies a WaitGlobalOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a WaitGlobalOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns WaitGlobalOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.WaitGlobalOperationRequest;

                    /**
                     * Creates a plain object from a WaitGlobalOperationRequest message. Also converts values to other types if specified.
                     * @param message WaitGlobalOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.WaitGlobalOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this WaitGlobalOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteGlobalOrganizationOperationRequest. */
                interface IDeleteGlobalOrganizationOperationRequest {

                    /** DeleteGlobalOrganizationOperationRequest operation */
                    operation?: (string|null);

                    /** DeleteGlobalOrganizationOperationRequest parentId */
                    parentId?: (string|null);
                }

                /** Represents a DeleteGlobalOrganizationOperationRequest. */
                class DeleteGlobalOrganizationOperationRequest implements IDeleteGlobalOrganizationOperationRequest {

                    /**
                     * Constructs a new DeleteGlobalOrganizationOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest);

                    /** DeleteGlobalOrganizationOperationRequest operation. */
                    public operation: string;

                    /** DeleteGlobalOrganizationOperationRequest parentId. */
                    public parentId?: (string|null);

                    /** DeleteGlobalOrganizationOperationRequest _parentId. */
                    public _parentId?: "parentId";

                    /**
                     * Creates a new DeleteGlobalOrganizationOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteGlobalOrganizationOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest): google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest;

                    /**
                     * Encodes the specified DeleteGlobalOrganizationOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest.verify|verify} messages.
                     * @param message DeleteGlobalOrganizationOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteGlobalOrganizationOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest.verify|verify} messages.
                     * @param message DeleteGlobalOrganizationOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteGlobalOrganizationOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteGlobalOrganizationOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest;

                    /**
                     * Decodes a DeleteGlobalOrganizationOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteGlobalOrganizationOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest;

                    /**
                     * Verifies a DeleteGlobalOrganizationOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteGlobalOrganizationOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteGlobalOrganizationOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest;

                    /**
                     * Creates a plain object from a DeleteGlobalOrganizationOperationRequest message. Also converts values to other types if specified.
                     * @param message DeleteGlobalOrganizationOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteGlobalOrganizationOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteGlobalOrganizationOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a DeleteGlobalOrganizationOperationResponse. */
                interface IDeleteGlobalOrganizationOperationResponse {
                }

                /** Represents a DeleteGlobalOrganizationOperationResponse. */
                class DeleteGlobalOrganizationOperationResponse implements IDeleteGlobalOrganizationOperationResponse {

                    /**
                     * Constructs a new DeleteGlobalOrganizationOperationResponse.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationResponse);

                    /**
                     * Creates a new DeleteGlobalOrganizationOperationResponse instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns DeleteGlobalOrganizationOperationResponse instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationResponse): google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse;

                    /**
                     * Encodes the specified DeleteGlobalOrganizationOperationResponse message. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse.verify|verify} messages.
                     * @param message DeleteGlobalOrganizationOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified DeleteGlobalOrganizationOperationResponse message, length delimited. Does not implicitly {@link google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse.verify|verify} messages.
                     * @param message DeleteGlobalOrganizationOperationResponse message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a DeleteGlobalOrganizationOperationResponse message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns DeleteGlobalOrganizationOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse;

                    /**
                     * Decodes a DeleteGlobalOrganizationOperationResponse message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns DeleteGlobalOrganizationOperationResponse
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse;

                    /**
                     * Verifies a DeleteGlobalOrganizationOperationResponse message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a DeleteGlobalOrganizationOperationResponse message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns DeleteGlobalOrganizationOperationResponse
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse;

                    /**
                     * Creates a plain object from a DeleteGlobalOrganizationOperationResponse message. Also converts values to other types if specified.
                     * @param message DeleteGlobalOrganizationOperationResponse
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this DeleteGlobalOrganizationOperationResponse to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a GetGlobalOrganizationOperationRequest. */
                interface IGetGlobalOrganizationOperationRequest {

                    /** GetGlobalOrganizationOperationRequest operation */
                    operation?: (string|null);

                    /** GetGlobalOrganizationOperationRequest parentId */
                    parentId?: (string|null);
                }

                /** Represents a GetGlobalOrganizationOperationRequest. */
                class GetGlobalOrganizationOperationRequest implements IGetGlobalOrganizationOperationRequest {

                    /**
                     * Constructs a new GetGlobalOrganizationOperationRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest);

                    /** GetGlobalOrganizationOperationRequest operation. */
                    public operation: string;

                    /** GetGlobalOrganizationOperationRequest parentId. */
                    public parentId?: (string|null);

                    /** GetGlobalOrganizationOperationRequest _parentId. */
                    public _parentId?: "parentId";

                    /**
                     * Creates a new GetGlobalOrganizationOperationRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns GetGlobalOrganizationOperationRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest): google.cloud.compute.v1.GetGlobalOrganizationOperationRequest;

                    /**
                     * Encodes the specified GetGlobalOrganizationOperationRequest message. Does not implicitly {@link google.cloud.compute.v1.GetGlobalOrganizationOperationRequest.verify|verify} messages.
                     * @param message GetGlobalOrganizationOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified GetGlobalOrganizationOperationRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.GetGlobalOrganizationOperationRequest.verify|verify} messages.
                     * @param message GetGlobalOrganizationOperationRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a GetGlobalOrganizationOperationRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns GetGlobalOrganizationOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.GetGlobalOrganizationOperationRequest;

                    /**
                     * Decodes a GetGlobalOrganizationOperationRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns GetGlobalOrganizationOperationRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.GetGlobalOrganizationOperationRequest;

                    /**
                     * Verifies a GetGlobalOrganizationOperationRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a GetGlobalOrganizationOperationRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns GetGlobalOrganizationOperationRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.GetGlobalOrganizationOperationRequest;

                    /**
                     * Creates a plain object from a GetGlobalOrganizationOperationRequest message. Also converts values to other types if specified.
                     * @param message GetGlobalOrganizationOperationRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.GetGlobalOrganizationOperationRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this GetGlobalOrganizationOperationRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Properties of a ListGlobalOrganizationOperationsRequest. */
                interface IListGlobalOrganizationOperationsRequest {

                    /** ListGlobalOrganizationOperationsRequest filter */
                    filter?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest maxResults */
                    maxResults?: (number|null);

                    /** ListGlobalOrganizationOperationsRequest orderBy */
                    orderBy?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest pageToken */
                    pageToken?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest parentId */
                    parentId?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest returnPartialSuccess */
                    returnPartialSuccess?: (boolean|null);
                }

                /** Represents a ListGlobalOrganizationOperationsRequest. */
                class ListGlobalOrganizationOperationsRequest implements IListGlobalOrganizationOperationsRequest {

                    /**
                     * Constructs a new ListGlobalOrganizationOperationsRequest.
                     * @param [properties] Properties to set
                     */
                    constructor(properties?: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest);

                    /** ListGlobalOrganizationOperationsRequest filter. */
                    public filter?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest maxResults. */
                    public maxResults?: (number|null);

                    /** ListGlobalOrganizationOperationsRequest orderBy. */
                    public orderBy?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest pageToken. */
                    public pageToken?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest parentId. */
                    public parentId?: (string|null);

                    /** ListGlobalOrganizationOperationsRequest returnPartialSuccess. */
                    public returnPartialSuccess?: (boolean|null);

                    /** ListGlobalOrganizationOperationsRequest _filter. */
                    public _filter?: "filter";

                    /** ListGlobalOrganizationOperationsRequest _maxResults. */
                    public _maxResults?: "maxResults";

                    /** ListGlobalOrganizationOperationsRequest _orderBy. */
                    public _orderBy?: "orderBy";

                    /** ListGlobalOrganizationOperationsRequest _pageToken. */
                    public _pageToken?: "pageToken";

                    /** ListGlobalOrganizationOperationsRequest _parentId. */
                    public _parentId?: "parentId";

                    /** ListGlobalOrganizationOperationsRequest _returnPartialSuccess. */
                    public _returnPartialSuccess?: "returnPartialSuccess";

                    /**
                     * Creates a new ListGlobalOrganizationOperationsRequest instance using the specified properties.
                     * @param [properties] Properties to set
                     * @returns ListGlobalOrganizationOperationsRequest instance
                     */
                    public static create(properties?: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest): google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest;

                    /**
                     * Encodes the specified ListGlobalOrganizationOperationsRequest message. Does not implicitly {@link google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest.verify|verify} messages.
                     * @param message ListGlobalOrganizationOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encode(message: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Encodes the specified ListGlobalOrganizationOperationsRequest message, length delimited. Does not implicitly {@link google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest.verify|verify} messages.
                     * @param message ListGlobalOrganizationOperationsRequest message or plain object to encode
                     * @param [writer] Writer to encode to
                     * @returns Writer
                     */
                    public static encodeDelimited(message: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                    /**
                     * Decodes a ListGlobalOrganizationOperationsRequest message from the specified reader or buffer.
                     * @param reader Reader or buffer to decode from
                     * @param [length] Message length if known beforehand
                     * @returns ListGlobalOrganizationOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest;

                    /**
                     * Decodes a ListGlobalOrganizationOperationsRequest message from the specified reader or buffer, length delimited.
                     * @param reader Reader or buffer to decode from
                     * @returns ListGlobalOrganizationOperationsRequest
                     * @throws {Error} If the payload is not a reader or valid buffer
                     * @throws {$protobuf.util.ProtocolError} If required fields are missing
                     */
                    public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest;

                    /**
                     * Verifies a ListGlobalOrganizationOperationsRequest message.
                     * @param message Plain object to verify
                     * @returns `null` if valid, otherwise the reason why it is not
                     */
                    public static verify(message: { [k: string]: any }): (string|null);

                    /**
                     * Creates a ListGlobalOrganizationOperationsRequest message from a plain object. Also converts values to their respective internal types.
                     * @param object Plain object
                     * @returns ListGlobalOrganizationOperationsRequest
                     */
                    public static fromObject(object: { [k: string]: any }): google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest;

                    /**
                     * Creates a plain object from a ListGlobalOrganizationOperationsRequest message. Also converts values to other types if specified.
                     * @param message ListGlobalOrganizationOperationsRequest
                     * @param [options] Conversion options
                     * @returns Plain object
                     */
                    public static toObject(message: google.cloud.compute.v1.ListGlobalOrganizationOperationsRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                    /**
                     * Converts this ListGlobalOrganizationOperationsRequest to JSON.
                     * @returns JSON object
                     */
                    public toJSON(): { [k: string]: any };
                }

                /** Represents a RegionOperations */
                class RegionOperations extends $protobuf.rpc.Service {

                    /**
                     * Constructs a new RegionOperations service.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     */
                    constructor(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean);

                    /**
                     * Creates new RegionOperations service using the specified rpc implementation.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     * @returns RPC service. Useful where requests and/or responses are streamed.
                     */
                    public static create(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean): RegionOperations;

                    /**
                     * Calls Delete.
                     * @param request DeleteRegionOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and DeleteRegionOperationResponse
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteRegionOperationRequest, callback: google.cloud.compute.v1.RegionOperations.DeleteCallback): void;

                    /**
                     * Calls Delete.
                     * @param request DeleteRegionOperationRequest message or plain object
                     * @returns Promise
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteRegionOperationRequest): Promise<google.cloud.compute.v1.DeleteRegionOperationResponse>;

                    /**
                     * Calls Get.
                     * @param request GetRegionOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public get(request: google.cloud.compute.v1.IGetRegionOperationRequest, callback: google.cloud.compute.v1.RegionOperations.GetCallback): void;

                    /**
                     * Calls Get.
                     * @param request GetRegionOperationRequest message or plain object
                     * @returns Promise
                     */
                    public get(request: google.cloud.compute.v1.IGetRegionOperationRequest): Promise<google.cloud.compute.v1.Operation>;

                    /**
                     * Calls List.
                     * @param request ListRegionOperationsRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and OperationList
                     */
                    public list(request: google.cloud.compute.v1.IListRegionOperationsRequest, callback: google.cloud.compute.v1.RegionOperations.ListCallback): void;

                    /**
                     * Calls List.
                     * @param request ListRegionOperationsRequest message or plain object
                     * @returns Promise
                     */
                    public list(request: google.cloud.compute.v1.IListRegionOperationsRequest): Promise<google.cloud.compute.v1.OperationList>;

                    /**
                     * Calls Wait.
                     * @param request WaitRegionOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public wait(request: google.cloud.compute.v1.IWaitRegionOperationRequest, callback: google.cloud.compute.v1.RegionOperations.WaitCallback): void;

                    /**
                     * Calls Wait.
                     * @param request WaitRegionOperationRequest message or plain object
                     * @returns Promise
                     */
                    public wait(request: google.cloud.compute.v1.IWaitRegionOperationRequest): Promise<google.cloud.compute.v1.Operation>;
                }

                namespace RegionOperations {

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.RegionOperations#delete_}.
                     * @param error Error, if any
                     * @param [response] DeleteRegionOperationResponse
                     */
                    type DeleteCallback = (error: (Error|null), response?: google.cloud.compute.v1.DeleteRegionOperationResponse) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.RegionOperations#get}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type GetCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.RegionOperations#list}.
                     * @param error Error, if any
                     * @param [response] OperationList
                     */
                    type ListCallback = (error: (Error|null), response?: google.cloud.compute.v1.OperationList) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.RegionOperations#wait}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type WaitCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;
                }

                /** Represents a ZoneOperations */
                class ZoneOperations extends $protobuf.rpc.Service {

                    /**
                     * Constructs a new ZoneOperations service.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     */
                    constructor(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean);

                    /**
                     * Creates new ZoneOperations service using the specified rpc implementation.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     * @returns RPC service. Useful where requests and/or responses are streamed.
                     */
                    public static create(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean): ZoneOperations;

                    /**
                     * Calls Delete.
                     * @param request DeleteZoneOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and DeleteZoneOperationResponse
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteZoneOperationRequest, callback: google.cloud.compute.v1.ZoneOperations.DeleteCallback): void;

                    /**
                     * Calls Delete.
                     * @param request DeleteZoneOperationRequest message or plain object
                     * @returns Promise
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteZoneOperationRequest): Promise<google.cloud.compute.v1.DeleteZoneOperationResponse>;

                    /**
                     * Calls Get.
                     * @param request GetZoneOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public get(request: google.cloud.compute.v1.IGetZoneOperationRequest, callback: google.cloud.compute.v1.ZoneOperations.GetCallback): void;

                    /**
                     * Calls Get.
                     * @param request GetZoneOperationRequest message or plain object
                     * @returns Promise
                     */
                    public get(request: google.cloud.compute.v1.IGetZoneOperationRequest): Promise<google.cloud.compute.v1.Operation>;

                    /**
                     * Calls List.
                     * @param request ListZoneOperationsRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and OperationList
                     */
                    public list(request: google.cloud.compute.v1.IListZoneOperationsRequest, callback: google.cloud.compute.v1.ZoneOperations.ListCallback): void;

                    /**
                     * Calls List.
                     * @param request ListZoneOperationsRequest message or plain object
                     * @returns Promise
                     */
                    public list(request: google.cloud.compute.v1.IListZoneOperationsRequest): Promise<google.cloud.compute.v1.OperationList>;

                    /**
                     * Calls Wait.
                     * @param request WaitZoneOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public wait(request: google.cloud.compute.v1.IWaitZoneOperationRequest, callback: google.cloud.compute.v1.ZoneOperations.WaitCallback): void;

                    /**
                     * Calls Wait.
                     * @param request WaitZoneOperationRequest message or plain object
                     * @returns Promise
                     */
                    public wait(request: google.cloud.compute.v1.IWaitZoneOperationRequest): Promise<google.cloud.compute.v1.Operation>;
                }

                namespace ZoneOperations {

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.ZoneOperations#delete_}.
                     * @param error Error, if any
                     * @param [response] DeleteZoneOperationResponse
                     */
                    type DeleteCallback = (error: (Error|null), response?: google.cloud.compute.v1.DeleteZoneOperationResponse) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.ZoneOperations#get}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type GetCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.ZoneOperations#list}.
                     * @param error Error, if any
                     * @param [response] OperationList
                     */
                    type ListCallback = (error: (Error|null), response?: google.cloud.compute.v1.OperationList) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.ZoneOperations#wait}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type WaitCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;
                }

                /** Represents a GlobalOperations */
                class GlobalOperations extends $protobuf.rpc.Service {

                    /**
                     * Constructs a new GlobalOperations service.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     */
                    constructor(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean);

                    /**
                     * Creates new GlobalOperations service using the specified rpc implementation.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     * @returns RPC service. Useful where requests and/or responses are streamed.
                     */
                    public static create(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean): GlobalOperations;

                    /**
                     * Calls AggregatedList.
                     * @param request AggregatedListGlobalOperationsRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and OperationAggregatedList
                     */
                    public aggregatedList(request: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest, callback: google.cloud.compute.v1.GlobalOperations.AggregatedListCallback): void;

                    /**
                     * Calls AggregatedList.
                     * @param request AggregatedListGlobalOperationsRequest message or plain object
                     * @returns Promise
                     */
                    public aggregatedList(request: google.cloud.compute.v1.IAggregatedListGlobalOperationsRequest): Promise<google.cloud.compute.v1.OperationAggregatedList>;

                    /**
                     * Calls Delete.
                     * @param request DeleteGlobalOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and DeleteGlobalOperationResponse
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteGlobalOperationRequest, callback: google.cloud.compute.v1.GlobalOperations.DeleteCallback): void;

                    /**
                     * Calls Delete.
                     * @param request DeleteGlobalOperationRequest message or plain object
                     * @returns Promise
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteGlobalOperationRequest): Promise<google.cloud.compute.v1.DeleteGlobalOperationResponse>;

                    /**
                     * Calls Get.
                     * @param request GetGlobalOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public get(request: google.cloud.compute.v1.IGetGlobalOperationRequest, callback: google.cloud.compute.v1.GlobalOperations.GetCallback): void;

                    /**
                     * Calls Get.
                     * @param request GetGlobalOperationRequest message or plain object
                     * @returns Promise
                     */
                    public get(request: google.cloud.compute.v1.IGetGlobalOperationRequest): Promise<google.cloud.compute.v1.Operation>;

                    /**
                     * Calls List.
                     * @param request ListGlobalOperationsRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and OperationList
                     */
                    public list(request: google.cloud.compute.v1.IListGlobalOperationsRequest, callback: google.cloud.compute.v1.GlobalOperations.ListCallback): void;

                    /**
                     * Calls List.
                     * @param request ListGlobalOperationsRequest message or plain object
                     * @returns Promise
                     */
                    public list(request: google.cloud.compute.v1.IListGlobalOperationsRequest): Promise<google.cloud.compute.v1.OperationList>;

                    /**
                     * Calls Wait.
                     * @param request WaitGlobalOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public wait(request: google.cloud.compute.v1.IWaitGlobalOperationRequest, callback: google.cloud.compute.v1.GlobalOperations.WaitCallback): void;

                    /**
                     * Calls Wait.
                     * @param request WaitGlobalOperationRequest message or plain object
                     * @returns Promise
                     */
                    public wait(request: google.cloud.compute.v1.IWaitGlobalOperationRequest): Promise<google.cloud.compute.v1.Operation>;
                }

                namespace GlobalOperations {

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOperations#aggregatedList}.
                     * @param error Error, if any
                     * @param [response] OperationAggregatedList
                     */
                    type AggregatedListCallback = (error: (Error|null), response?: google.cloud.compute.v1.OperationAggregatedList) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOperations#delete_}.
                     * @param error Error, if any
                     * @param [response] DeleteGlobalOperationResponse
                     */
                    type DeleteCallback = (error: (Error|null), response?: google.cloud.compute.v1.DeleteGlobalOperationResponse) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOperations#get}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type GetCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOperations#list}.
                     * @param error Error, if any
                     * @param [response] OperationList
                     */
                    type ListCallback = (error: (Error|null), response?: google.cloud.compute.v1.OperationList) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOperations#wait}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type WaitCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;
                }

                /** Represents a GlobalOrganizationOperations */
                class GlobalOrganizationOperations extends $protobuf.rpc.Service {

                    /**
                     * Constructs a new GlobalOrganizationOperations service.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     */
                    constructor(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean);

                    /**
                     * Creates new GlobalOrganizationOperations service using the specified rpc implementation.
                     * @param rpcImpl RPC implementation
                     * @param [requestDelimited=false] Whether requests are length-delimited
                     * @param [responseDelimited=false] Whether responses are length-delimited
                     * @returns RPC service. Useful where requests and/or responses are streamed.
                     */
                    public static create(rpcImpl: $protobuf.RPCImpl, requestDelimited?: boolean, responseDelimited?: boolean): GlobalOrganizationOperations;

                    /**
                     * Calls Delete.
                     * @param request DeleteGlobalOrganizationOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and DeleteGlobalOrganizationOperationResponse
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest, callback: google.cloud.compute.v1.GlobalOrganizationOperations.DeleteCallback): void;

                    /**
                     * Calls Delete.
                     * @param request DeleteGlobalOrganizationOperationRequest message or plain object
                     * @returns Promise
                     */
                    public delete(request: google.cloud.compute.v1.IDeleteGlobalOrganizationOperationRequest): Promise<google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse>;

                    /**
                     * Calls Get.
                     * @param request GetGlobalOrganizationOperationRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and Operation
                     */
                    public get(request: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest, callback: google.cloud.compute.v1.GlobalOrganizationOperations.GetCallback): void;

                    /**
                     * Calls Get.
                     * @param request GetGlobalOrganizationOperationRequest message or plain object
                     * @returns Promise
                     */
                    public get(request: google.cloud.compute.v1.IGetGlobalOrganizationOperationRequest): Promise<google.cloud.compute.v1.Operation>;

                    /**
                     * Calls List.
                     * @param request ListGlobalOrganizationOperationsRequest message or plain object
                     * @param callback Node-style callback called with the error, if any, and OperationList
                     */
                    public list(request: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest, callback: google.cloud.compute.v1.GlobalOrganizationOperations.ListCallback): void;

                    /**
                     * Calls List.
                     * @param request ListGlobalOrganizationOperationsRequest message or plain object
                     * @returns Promise
                     */
                    public list(request: google.cloud.compute.v1.IListGlobalOrganizationOperationsRequest): Promise<google.cloud.compute.v1.OperationList>;
                }

                namespace GlobalOrganizationOperations {

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOrganizationOperations#delete_}.
                     * @param error Error, if any
                     * @param [response] DeleteGlobalOrganizationOperationResponse
                     */
                    type DeleteCallback = (error: (Error|null), response?: google.cloud.compute.v1.DeleteGlobalOrganizationOperationResponse) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOrganizationOperations#get}.
                     * @param error Error, if any
                     * @param [response] Operation
                     */
                    type GetCallback = (error: (Error|null), response?: google.cloud.compute.v1.Operation) => void;

                    /**
                     * Callback as used by {@link google.cloud.compute.v1.GlobalOrganizationOperations#list}.
                     * @param error Error, if any
                     * @param [response] OperationList
                     */
                    type ListCallback = (error: (Error|null), response?: google.cloud.compute.v1.OperationList) => void;
                }
            }
        }
    }

    /** Namespace api. */
    namespace api {

        /** Properties of a Http. */
        interface IHttp {

            /** Http rules */
            rules?: (google.api.IHttpRule[]|null);

            /** Http fullyDecodeReservedExpansion */
            fullyDecodeReservedExpansion?: (boolean|null);
        }

        /** Represents a Http. */
        class Http implements IHttp {

            /**
             * Constructs a new Http.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.api.IHttp);

            /** Http rules. */
            public rules: google.api.IHttpRule[];

            /** Http fullyDecodeReservedExpansion. */
            public fullyDecodeReservedExpansion: boolean;

            /**
             * Creates a new Http instance using the specified properties.
             * @param [properties] Properties to set
             * @returns Http instance
             */
            public static create(properties?: google.api.IHttp): google.api.Http;

            /**
             * Encodes the specified Http message. Does not implicitly {@link google.api.Http.verify|verify} messages.
             * @param message Http message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.api.IHttp, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified Http message, length delimited. Does not implicitly {@link google.api.Http.verify|verify} messages.
             * @param message Http message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.api.IHttp, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Http message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Http
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.api.Http;

            /**
             * Decodes a Http message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns Http
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.api.Http;

            /**
             * Verifies a Http message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Http message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Http
             */
            public static fromObject(object: { [k: string]: any }): google.api.Http;

            /**
             * Creates a plain object from a Http message. Also converts values to other types if specified.
             * @param message Http
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.api.Http, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Http to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a HttpRule. */
        interface IHttpRule {

            /** HttpRule selector */
            selector?: (string|null);

            /** HttpRule get */
            get?: (string|null);

            /** HttpRule put */
            put?: (string|null);

            /** HttpRule post */
            post?: (string|null);

            /** HttpRule delete */
            "delete"?: (string|null);

            /** HttpRule patch */
            patch?: (string|null);

            /** HttpRule custom */
            custom?: (google.api.ICustomHttpPattern|null);

            /** HttpRule body */
            body?: (string|null);

            /** HttpRule responseBody */
            responseBody?: (string|null);

            /** HttpRule additionalBindings */
            additionalBindings?: (google.api.IHttpRule[]|null);
        }

        /** Represents a HttpRule. */
        class HttpRule implements IHttpRule {

            /**
             * Constructs a new HttpRule.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.api.IHttpRule);

            /** HttpRule selector. */
            public selector: string;

            /** HttpRule get. */
            public get?: (string|null);

            /** HttpRule put. */
            public put?: (string|null);

            /** HttpRule post. */
            public post?: (string|null);

            /** HttpRule delete. */
            public delete?: (string|null);

            /** HttpRule patch. */
            public patch?: (string|null);

            /** HttpRule custom. */
            public custom?: (google.api.ICustomHttpPattern|null);

            /** HttpRule body. */
            public body: string;

            /** HttpRule responseBody. */
            public responseBody: string;

            /** HttpRule additionalBindings. */
            public additionalBindings: google.api.IHttpRule[];

            /** HttpRule pattern. */
            public pattern?: ("get"|"put"|"post"|"delete"|"patch"|"custom");

            /**
             * Creates a new HttpRule instance using the specified properties.
             * @param [properties] Properties to set
             * @returns HttpRule instance
             */
            public static create(properties?: google.api.IHttpRule): google.api.HttpRule;

            /**
             * Encodes the specified HttpRule message. Does not implicitly {@link google.api.HttpRule.verify|verify} messages.
             * @param message HttpRule message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.api.IHttpRule, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified HttpRule message, length delimited. Does not implicitly {@link google.api.HttpRule.verify|verify} messages.
             * @param message HttpRule message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.api.IHttpRule, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a HttpRule message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns HttpRule
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.api.HttpRule;

            /**
             * Decodes a HttpRule message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns HttpRule
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.api.HttpRule;

            /**
             * Verifies a HttpRule message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a HttpRule message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns HttpRule
             */
            public static fromObject(object: { [k: string]: any }): google.api.HttpRule;

            /**
             * Creates a plain object from a HttpRule message. Also converts values to other types if specified.
             * @param message HttpRule
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.api.HttpRule, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this HttpRule to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a CustomHttpPattern. */
        interface ICustomHttpPattern {

            /** CustomHttpPattern kind */
            kind?: (string|null);

            /** CustomHttpPattern path */
            path?: (string|null);
        }

        /** Represents a CustomHttpPattern. */
        class CustomHttpPattern implements ICustomHttpPattern {

            /**
             * Constructs a new CustomHttpPattern.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.api.ICustomHttpPattern);

            /** CustomHttpPattern kind. */
            public kind: string;

            /** CustomHttpPattern path. */
            public path: string;

            /**
             * Creates a new CustomHttpPattern instance using the specified properties.
             * @param [properties] Properties to set
             * @returns CustomHttpPattern instance
             */
            public static create(properties?: google.api.ICustomHttpPattern): google.api.CustomHttpPattern;

            /**
             * Encodes the specified CustomHttpPattern message. Does not implicitly {@link google.api.CustomHttpPattern.verify|verify} messages.
             * @param message CustomHttpPattern message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.api.ICustomHttpPattern, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified CustomHttpPattern message, length delimited. Does not implicitly {@link google.api.CustomHttpPattern.verify|verify} messages.
             * @param message CustomHttpPattern message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.api.ICustomHttpPattern, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a CustomHttpPattern message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns CustomHttpPattern
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.api.CustomHttpPattern;

            /**
             * Decodes a CustomHttpPattern message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns CustomHttpPattern
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.api.CustomHttpPattern;

            /**
             * Verifies a CustomHttpPattern message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a CustomHttpPattern message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns CustomHttpPattern
             */
            public static fromObject(object: { [k: string]: any }): google.api.CustomHttpPattern;

            /**
             * Creates a plain object from a CustomHttpPattern message. Also converts values to other types if specified.
             * @param message CustomHttpPattern
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.api.CustomHttpPattern, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this CustomHttpPattern to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }
    }

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of a FileDescriptorSet. */
        interface IFileDescriptorSet {

            /** FileDescriptorSet file */
            file?: (google.protobuf.IFileDescriptorProto[]|null);
        }

        /** Represents a FileDescriptorSet. */
        class FileDescriptorSet implements IFileDescriptorSet {

            /**
             * Constructs a new FileDescriptorSet.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorSet);

            /** FileDescriptorSet file. */
            public file: google.protobuf.IFileDescriptorProto[];

            /**
             * Creates a new FileDescriptorSet instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FileDescriptorSet instance
             */
            public static create(properties?: google.protobuf.IFileDescriptorSet): google.protobuf.FileDescriptorSet;

            /**
             * Encodes the specified FileDescriptorSet message. Does not implicitly {@link google.protobuf.FileDescriptorSet.verify|verify} messages.
             * @param message FileDescriptorSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FileDescriptorSet message, length delimited. Does not implicitly {@link google.protobuf.FileDescriptorSet.verify|verify} messages.
             * @param message FileDescriptorSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFileDescriptorSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorSet message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorSet;

            /**
             * Decodes a FileDescriptorSet message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FileDescriptorSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FileDescriptorSet;

            /**
             * Verifies a FileDescriptorSet message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorSet message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorSet
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorSet;

            /**
             * Creates a plain object from a FileDescriptorSet message. Also converts values to other types if specified.
             * @param message FileDescriptorSet
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorSet, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorSet to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a FileDescriptorProto. */
        interface IFileDescriptorProto {

            /** FileDescriptorProto name */
            name?: (string|null);

            /** FileDescriptorProto package */
            "package"?: (string|null);

            /** FileDescriptorProto dependency */
            dependency?: (string[]|null);

            /** FileDescriptorProto publicDependency */
            publicDependency?: (number[]|null);

            /** FileDescriptorProto weakDependency */
            weakDependency?: (number[]|null);

            /** FileDescriptorProto messageType */
            messageType?: (google.protobuf.IDescriptorProto[]|null);

            /** FileDescriptorProto enumType */
            enumType?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** FileDescriptorProto service */
            service?: (google.protobuf.IServiceDescriptorProto[]|null);

            /** FileDescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** FileDescriptorProto options */
            options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto sourceCodeInfo */
            sourceCodeInfo?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax */
            syntax?: (string|null);
        }

        /** Represents a FileDescriptorProto. */
        class FileDescriptorProto implements IFileDescriptorProto {

            /**
             * Constructs a new FileDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorProto);

            /** FileDescriptorProto name. */
            public name: string;

            /** FileDescriptorProto package. */
            public package: string;

            /** FileDescriptorProto dependency. */
            public dependency: string[];

            /** FileDescriptorProto publicDependency. */
            public publicDependency: number[];

            /** FileDescriptorProto weakDependency. */
            public weakDependency: number[];

            /** FileDescriptorProto messageType. */
            public messageType: google.protobuf.IDescriptorProto[];

            /** FileDescriptorProto enumType. */
            public enumType: google.protobuf.IEnumDescriptorProto[];

            /** FileDescriptorProto service. */
            public service: google.protobuf.IServiceDescriptorProto[];

            /** FileDescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** FileDescriptorProto options. */
            public options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto sourceCodeInfo. */
            public sourceCodeInfo?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax. */
            public syntax: string;

            /**
             * Creates a new FileDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FileDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IFileDescriptorProto): google.protobuf.FileDescriptorProto;

            /**
             * Encodes the specified FileDescriptorProto message. Does not implicitly {@link google.protobuf.FileDescriptorProto.verify|verify} messages.
             * @param message FileDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FileDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.FileDescriptorProto.verify|verify} messages.
             * @param message FileDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFileDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorProto;

            /**
             * Decodes a FileDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FileDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FileDescriptorProto;

            /**
             * Verifies a FileDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorProto;

            /**
             * Creates a plain object from a FileDescriptorProto message. Also converts values to other types if specified.
             * @param message FileDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a DescriptorProto. */
        interface IDescriptorProto {

            /** DescriptorProto name */
            name?: (string|null);

            /** DescriptorProto field */
            field?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto nestedType */
            nestedType?: (google.protobuf.IDescriptorProto[]|null);

            /** DescriptorProto enumType */
            enumType?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** DescriptorProto extensionRange */
            extensionRange?: (google.protobuf.DescriptorProto.IExtensionRange[]|null);

            /** DescriptorProto oneofDecl */
            oneofDecl?: (google.protobuf.IOneofDescriptorProto[]|null);

            /** DescriptorProto options */
            options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reservedRange */
            reservedRange?: (google.protobuf.DescriptorProto.IReservedRange[]|null);

            /** DescriptorProto reservedName */
            reservedName?: (string[]|null);
        }

        /** Represents a DescriptorProto. */
        class DescriptorProto implements IDescriptorProto {

            /**
             * Constructs a new DescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDescriptorProto);

            /** DescriptorProto name. */
            public name: string;

            /** DescriptorProto field. */
            public field: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto nestedType. */
            public nestedType: google.protobuf.IDescriptorProto[];

            /** DescriptorProto enumType. */
            public enumType: google.protobuf.IEnumDescriptorProto[];

            /** DescriptorProto extensionRange. */
            public extensionRange: google.protobuf.DescriptorProto.IExtensionRange[];

            /** DescriptorProto oneofDecl. */
            public oneofDecl: google.protobuf.IOneofDescriptorProto[];

            /** DescriptorProto options. */
            public options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reservedRange. */
            public reservedRange: google.protobuf.DescriptorProto.IReservedRange[];

            /** DescriptorProto reservedName. */
            public reservedName: string[];

            /**
             * Creates a new DescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns DescriptorProto instance
             */
            public static create(properties?: google.protobuf.IDescriptorProto): google.protobuf.DescriptorProto;

            /**
             * Encodes the specified DescriptorProto message. Does not implicitly {@link google.protobuf.DescriptorProto.verify|verify} messages.
             * @param message DescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified DescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.DescriptorProto.verify|verify} messages.
             * @param message DescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a DescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns DescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto;

            /**
             * Decodes a DescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns DescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.DescriptorProto;

            /**
             * Verifies a DescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a DescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns DescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto;

            /**
             * Creates a plain object from a DescriptorProto message. Also converts values to other types if specified.
             * @param message DescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.DescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this DescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace DescriptorProto {

            /** Properties of an ExtensionRange. */
            interface IExtensionRange {

                /** ExtensionRange start */
                start?: (number|null);

                /** ExtensionRange end */
                end?: (number|null);

                /** ExtensionRange options */
                options?: (google.protobuf.IExtensionRangeOptions|null);
            }

            /** Represents an ExtensionRange. */
            class ExtensionRange implements IExtensionRange {

                /**
                 * Constructs a new ExtensionRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IExtensionRange);

                /** ExtensionRange start. */
                public start: number;

                /** ExtensionRange end. */
                public end: number;

                /** ExtensionRange options. */
                public options?: (google.protobuf.IExtensionRangeOptions|null);

                /**
                 * Creates a new ExtensionRange instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns ExtensionRange instance
                 */
                public static create(properties?: google.protobuf.DescriptorProto.IExtensionRange): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Encodes the specified ExtensionRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ExtensionRange.verify|verify} messages.
                 * @param message ExtensionRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IExtensionRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified ExtensionRange message, length delimited. Does not implicitly {@link google.protobuf.DescriptorProto.ExtensionRange.verify|verify} messages.
                 * @param message ExtensionRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.DescriptorProto.IExtensionRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an ExtensionRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ExtensionRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Decodes an ExtensionRange message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns ExtensionRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Verifies an ExtensionRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an ExtensionRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ExtensionRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Creates a plain object from an ExtensionRange message. Also converts values to other types if specified.
                 * @param message ExtensionRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ExtensionRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ExtensionRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }

            /** Properties of a ReservedRange. */
            interface IReservedRange {

                /** ReservedRange start */
                start?: (number|null);

                /** ReservedRange end */
                end?: (number|null);
            }

            /** Represents a ReservedRange. */
            class ReservedRange implements IReservedRange {

                /**
                 * Constructs a new ReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IReservedRange);

                /** ReservedRange start. */
                public start: number;

                /** ReservedRange end. */
                public end: number;

                /**
                 * Creates a new ReservedRange instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns ReservedRange instance
                 */
                public static create(properties?: google.protobuf.DescriptorProto.IReservedRange): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Encodes the specified ReservedRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ReservedRange.verify|verify} messages.
                 * @param message ReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified ReservedRange message, length delimited. Does not implicitly {@link google.protobuf.DescriptorProto.ReservedRange.verify|verify} messages.
                 * @param message ReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.DescriptorProto.IReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a ReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Decodes a ReservedRange message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns ReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Verifies a ReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a ReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Creates a plain object from a ReservedRange message. Also converts values to other types if specified.
                 * @param message ReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }
        }

        /** Properties of an ExtensionRangeOptions. */
        interface IExtensionRangeOptions {

            /** ExtensionRangeOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an ExtensionRangeOptions. */
        class ExtensionRangeOptions implements IExtensionRangeOptions {

            /**
             * Constructs a new ExtensionRangeOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IExtensionRangeOptions);

            /** ExtensionRangeOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new ExtensionRangeOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns ExtensionRangeOptions instance
             */
            public static create(properties?: google.protobuf.IExtensionRangeOptions): google.protobuf.ExtensionRangeOptions;

            /**
             * Encodes the specified ExtensionRangeOptions message. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.verify|verify} messages.
             * @param message ExtensionRangeOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IExtensionRangeOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified ExtensionRangeOptions message, length delimited. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.verify|verify} messages.
             * @param message ExtensionRangeOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IExtensionRangeOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an ExtensionRangeOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ExtensionRangeOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ExtensionRangeOptions;

            /**
             * Decodes an ExtensionRangeOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns ExtensionRangeOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.ExtensionRangeOptions;

            /**
             * Verifies an ExtensionRangeOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an ExtensionRangeOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ExtensionRangeOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ExtensionRangeOptions;

            /**
             * Creates a plain object from an ExtensionRangeOptions message. Also converts values to other types if specified.
             * @param message ExtensionRangeOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ExtensionRangeOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ExtensionRangeOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a FieldDescriptorProto. */
        interface IFieldDescriptorProto {

            /** FieldDescriptorProto name */
            name?: (string|null);

            /** FieldDescriptorProto number */
            number?: (number|null);

            /** FieldDescriptorProto label */
            label?: (google.protobuf.FieldDescriptorProto.Label|null);

            /** FieldDescriptorProto type */
            type?: (google.protobuf.FieldDescriptorProto.Type|null);

            /** FieldDescriptorProto typeName */
            typeName?: (string|null);

            /** FieldDescriptorProto extendee */
            extendee?: (string|null);

            /** FieldDescriptorProto defaultValue */
            defaultValue?: (string|null);

            /** FieldDescriptorProto oneofIndex */
            oneofIndex?: (number|null);

            /** FieldDescriptorProto jsonName */
            jsonName?: (string|null);

            /** FieldDescriptorProto options */
            options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3Optional */
            proto3Optional?: (boolean|null);
        }

        /** Represents a FieldDescriptorProto. */
        class FieldDescriptorProto implements IFieldDescriptorProto {

            /**
             * Constructs a new FieldDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldDescriptorProto);

            /** FieldDescriptorProto name. */
            public name: string;

            /** FieldDescriptorProto number. */
            public number: number;

            /** FieldDescriptorProto label. */
            public label: google.protobuf.FieldDescriptorProto.Label;

            /** FieldDescriptorProto type. */
            public type: google.protobuf.FieldDescriptorProto.Type;

            /** FieldDescriptorProto typeName. */
            public typeName: string;

            /** FieldDescriptorProto extendee. */
            public extendee: string;

            /** FieldDescriptorProto defaultValue. */
            public defaultValue: string;

            /** FieldDescriptorProto oneofIndex. */
            public oneofIndex: number;

            /** FieldDescriptorProto jsonName. */
            public jsonName: string;

            /** FieldDescriptorProto options. */
            public options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3Optional. */
            public proto3Optional: boolean;

            /**
             * Creates a new FieldDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FieldDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IFieldDescriptorProto): google.protobuf.FieldDescriptorProto;

            /**
             * Encodes the specified FieldDescriptorProto message. Does not implicitly {@link google.protobuf.FieldDescriptorProto.verify|verify} messages.
             * @param message FieldDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FieldDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.FieldDescriptorProto.verify|verify} messages.
             * @param message FieldDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFieldDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldDescriptorProto;

            /**
             * Decodes a FieldDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FieldDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FieldDescriptorProto;

            /**
             * Verifies a FieldDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldDescriptorProto;

            /**
             * Creates a plain object from a FieldDescriptorProto message. Also converts values to other types if specified.
             * @param message FieldDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace FieldDescriptorProto {

            /** Type enum. */
            enum Type {
                TYPE_DOUBLE = 1,
                TYPE_FLOAT = 2,
                TYPE_INT64 = 3,
                TYPE_UINT64 = 4,
                TYPE_INT32 = 5,
                TYPE_FIXED64 = 6,
                TYPE_FIXED32 = 7,
                TYPE_BOOL = 8,
                TYPE_STRING = 9,
                TYPE_GROUP = 10,
                TYPE_MESSAGE = 11,
                TYPE_BYTES = 12,
                TYPE_UINT32 = 13,
                TYPE_ENUM = 14,
                TYPE_SFIXED32 = 15,
                TYPE_SFIXED64 = 16,
                TYPE_SINT32 = 17,
                TYPE_SINT64 = 18
            }

            /** Label enum. */
            enum Label {
                LABEL_OPTIONAL = 1,
                LABEL_REQUIRED = 2,
                LABEL_REPEATED = 3
            }
        }

        /** Properties of an OneofDescriptorProto. */
        interface IOneofDescriptorProto {

            /** OneofDescriptorProto name */
            name?: (string|null);

            /** OneofDescriptorProto options */
            options?: (google.protobuf.IOneofOptions|null);
        }

        /** Represents an OneofDescriptorProto. */
        class OneofDescriptorProto implements IOneofDescriptorProto {

            /**
             * Constructs a new OneofDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofDescriptorProto);

            /** OneofDescriptorProto name. */
            public name: string;

            /** OneofDescriptorProto options. */
            public options?: (google.protobuf.IOneofOptions|null);

            /**
             * Creates a new OneofDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns OneofDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IOneofDescriptorProto): google.protobuf.OneofDescriptorProto;

            /**
             * Encodes the specified OneofDescriptorProto message. Does not implicitly {@link google.protobuf.OneofDescriptorProto.verify|verify} messages.
             * @param message OneofDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified OneofDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.OneofDescriptorProto.verify|verify} messages.
             * @param message OneofDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IOneofDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofDescriptorProto;

            /**
             * Decodes an OneofDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns OneofDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.OneofDescriptorProto;

            /**
             * Verifies an OneofDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofDescriptorProto;

            /**
             * Creates a plain object from an OneofDescriptorProto message. Also converts values to other types if specified.
             * @param message OneofDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of an EnumDescriptorProto. */
        interface IEnumDescriptorProto {

            /** EnumDescriptorProto name */
            name?: (string|null);

            /** EnumDescriptorProto value */
            value?: (google.protobuf.IEnumValueDescriptorProto[]|null);

            /** EnumDescriptorProto options */
            options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reservedRange */
            reservedRange?: (google.protobuf.EnumDescriptorProto.IEnumReservedRange[]|null);

            /** EnumDescriptorProto reservedName */
            reservedName?: (string[]|null);
        }

        /** Represents an EnumDescriptorProto. */
        class EnumDescriptorProto implements IEnumDescriptorProto {

            /**
             * Constructs a new EnumDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumDescriptorProto);

            /** EnumDescriptorProto name. */
            public name: string;

            /** EnumDescriptorProto value. */
            public value: google.protobuf.IEnumValueDescriptorProto[];

            /** EnumDescriptorProto options. */
            public options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reservedRange. */
            public reservedRange: google.protobuf.EnumDescriptorProto.IEnumReservedRange[];

            /** EnumDescriptorProto reservedName. */
            public reservedName: string[];

            /**
             * Creates a new EnumDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns EnumDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IEnumDescriptorProto): google.protobuf.EnumDescriptorProto;

            /**
             * Encodes the specified EnumDescriptorProto message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.verify|verify} messages.
             * @param message EnumDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified EnumDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.EnumDescriptorProto.verify|verify} messages.
             * @param message EnumDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IEnumDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto;

            /**
             * Decodes an EnumDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns EnumDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.EnumDescriptorProto;

            /**
             * Verifies an EnumDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto;

            /**
             * Creates a plain object from an EnumDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace EnumDescriptorProto {

            /** Properties of an EnumReservedRange. */
            interface IEnumReservedRange {

                /** EnumReservedRange start */
                start?: (number|null);

                /** EnumReservedRange end */
                end?: (number|null);
            }

            /** Represents an EnumReservedRange. */
            class EnumReservedRange implements IEnumReservedRange {

                /**
                 * Constructs a new EnumReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.EnumDescriptorProto.IEnumReservedRange);

                /** EnumReservedRange start. */
                public start: number;

                /** EnumReservedRange end. */
                public end: number;

                /**
                 * Creates a new EnumReservedRange instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns EnumReservedRange instance
                 */
                public static create(properties?: google.protobuf.EnumDescriptorProto.IEnumReservedRange): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Encodes the specified EnumReservedRange message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.EnumReservedRange.verify|verify} messages.
                 * @param message EnumReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.EnumDescriptorProto.IEnumReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified EnumReservedRange message, length delimited. Does not implicitly {@link google.protobuf.EnumDescriptorProto.EnumReservedRange.verify|verify} messages.
                 * @param message EnumReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.EnumDescriptorProto.IEnumReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an EnumReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns EnumReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Decodes an EnumReservedRange message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns EnumReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Verifies an EnumReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an EnumReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns EnumReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Creates a plain object from an EnumReservedRange message. Also converts values to other types if specified.
                 * @param message EnumReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.EnumDescriptorProto.EnumReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this EnumReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }
        }

        /** Properties of an EnumValueDescriptorProto. */
        interface IEnumValueDescriptorProto {

            /** EnumValueDescriptorProto name */
            name?: (string|null);

            /** EnumValueDescriptorProto number */
            number?: (number|null);

            /** EnumValueDescriptorProto options */
            options?: (google.protobuf.IEnumValueOptions|null);
        }

        /** Represents an EnumValueDescriptorProto. */
        class EnumValueDescriptorProto implements IEnumValueDescriptorProto {

            /**
             * Constructs a new EnumValueDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueDescriptorProto);

            /** EnumValueDescriptorProto name. */
            public name: string;

            /** EnumValueDescriptorProto number. */
            public number: number;

            /** EnumValueDescriptorProto options. */
            public options?: (google.protobuf.IEnumValueOptions|null);

            /**
             * Creates a new EnumValueDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns EnumValueDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IEnumValueDescriptorProto): google.protobuf.EnumValueDescriptorProto;

            /**
             * Encodes the specified EnumValueDescriptorProto message. Does not implicitly {@link google.protobuf.EnumValueDescriptorProto.verify|verify} messages.
             * @param message EnumValueDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified EnumValueDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.EnumValueDescriptorProto.verify|verify} messages.
             * @param message EnumValueDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IEnumValueDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueDescriptorProto;

            /**
             * Decodes an EnumValueDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns EnumValueDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.EnumValueDescriptorProto;

            /**
             * Verifies an EnumValueDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueDescriptorProto;

            /**
             * Creates a plain object from an EnumValueDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumValueDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a ServiceDescriptorProto. */
        interface IServiceDescriptorProto {

            /** ServiceDescriptorProto name */
            name?: (string|null);

            /** ServiceDescriptorProto method */
            method?: (google.protobuf.IMethodDescriptorProto[]|null);

            /** ServiceDescriptorProto options */
            options?: (google.protobuf.IServiceOptions|null);
        }

        /** Represents a ServiceDescriptorProto. */
        class ServiceDescriptorProto implements IServiceDescriptorProto {

            /**
             * Constructs a new ServiceDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceDescriptorProto);

            /** ServiceDescriptorProto name. */
            public name: string;

            /** ServiceDescriptorProto method. */
            public method: google.protobuf.IMethodDescriptorProto[];

            /** ServiceDescriptorProto options. */
            public options?: (google.protobuf.IServiceOptions|null);

            /**
             * Creates a new ServiceDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns ServiceDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IServiceDescriptorProto): google.protobuf.ServiceDescriptorProto;

            /**
             * Encodes the specified ServiceDescriptorProto message. Does not implicitly {@link google.protobuf.ServiceDescriptorProto.verify|verify} messages.
             * @param message ServiceDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified ServiceDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.ServiceDescriptorProto.verify|verify} messages.
             * @param message ServiceDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IServiceDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceDescriptorProto;

            /**
             * Decodes a ServiceDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns ServiceDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.ServiceDescriptorProto;

            /**
             * Verifies a ServiceDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceDescriptorProto;

            /**
             * Creates a plain object from a ServiceDescriptorProto message. Also converts values to other types if specified.
             * @param message ServiceDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a MethodDescriptorProto. */
        interface IMethodDescriptorProto {

            /** MethodDescriptorProto name */
            name?: (string|null);

            /** MethodDescriptorProto inputType */
            inputType?: (string|null);

            /** MethodDescriptorProto outputType */
            outputType?: (string|null);

            /** MethodDescriptorProto options */
            options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto clientStreaming */
            clientStreaming?: (boolean|null);

            /** MethodDescriptorProto serverStreaming */
            serverStreaming?: (boolean|null);
        }

        /** Represents a MethodDescriptorProto. */
        class MethodDescriptorProto implements IMethodDescriptorProto {

            /**
             * Constructs a new MethodDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodDescriptorProto);

            /** MethodDescriptorProto name. */
            public name: string;

            /** MethodDescriptorProto inputType. */
            public inputType: string;

            /** MethodDescriptorProto outputType. */
            public outputType: string;

            /** MethodDescriptorProto options. */
            public options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto clientStreaming. */
            public clientStreaming: boolean;

            /** MethodDescriptorProto serverStreaming. */
            public serverStreaming: boolean;

            /**
             * Creates a new MethodDescriptorProto instance using the specified properties.
             * @param [properties] Properties to set
             * @returns MethodDescriptorProto instance
             */
            public static create(properties?: google.protobuf.IMethodDescriptorProto): google.protobuf.MethodDescriptorProto;

            /**
             * Encodes the specified MethodDescriptorProto message. Does not implicitly {@link google.protobuf.MethodDescriptorProto.verify|verify} messages.
             * @param message MethodDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified MethodDescriptorProto message, length delimited. Does not implicitly {@link google.protobuf.MethodDescriptorProto.verify|verify} messages.
             * @param message MethodDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IMethodDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodDescriptorProto;

            /**
             * Decodes a MethodDescriptorProto message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns MethodDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.MethodDescriptorProto;

            /**
             * Verifies a MethodDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodDescriptorProto;

            /**
             * Creates a plain object from a MethodDescriptorProto message. Also converts values to other types if specified.
             * @param message MethodDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a FileOptions. */
        interface IFileOptions {

            /** FileOptions javaPackage */
            javaPackage?: (string|null);

            /** FileOptions javaOuterClassname */
            javaOuterClassname?: (string|null);

            /** FileOptions javaMultipleFiles */
            javaMultipleFiles?: (boolean|null);

            /** FileOptions javaGenerateEqualsAndHash */
            javaGenerateEqualsAndHash?: (boolean|null);

            /** FileOptions javaStringCheckUtf8 */
            javaStringCheckUtf8?: (boolean|null);

            /** FileOptions optimizeFor */
            optimizeFor?: (google.protobuf.FileOptions.OptimizeMode|null);

            /** FileOptions goPackage */
            goPackage?: (string|null);

            /** FileOptions ccGenericServices */
            ccGenericServices?: (boolean|null);

            /** FileOptions javaGenericServices */
            javaGenericServices?: (boolean|null);

            /** FileOptions pyGenericServices */
            pyGenericServices?: (boolean|null);

            /** FileOptions phpGenericServices */
            phpGenericServices?: (boolean|null);

            /** FileOptions deprecated */
            deprecated?: (boolean|null);

            /** FileOptions ccEnableArenas */
            ccEnableArenas?: (boolean|null);

            /** FileOptions objcClassPrefix */
            objcClassPrefix?: (string|null);

            /** FileOptions csharpNamespace */
            csharpNamespace?: (string|null);

            /** FileOptions swiftPrefix */
            swiftPrefix?: (string|null);

            /** FileOptions phpClassPrefix */
            phpClassPrefix?: (string|null);

            /** FileOptions phpNamespace */
            phpNamespace?: (string|null);

            /** FileOptions phpMetadataNamespace */
            phpMetadataNamespace?: (string|null);

            /** FileOptions rubyPackage */
            rubyPackage?: (string|null);

            /** FileOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a FileOptions. */
        class FileOptions implements IFileOptions {

            /**
             * Constructs a new FileOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileOptions);

            /** FileOptions javaPackage. */
            public javaPackage: string;

            /** FileOptions javaOuterClassname. */
            public javaOuterClassname: string;

            /** FileOptions javaMultipleFiles. */
            public javaMultipleFiles: boolean;

            /** FileOptions javaGenerateEqualsAndHash. */
            public javaGenerateEqualsAndHash: boolean;

            /** FileOptions javaStringCheckUtf8. */
            public javaStringCheckUtf8: boolean;

            /** FileOptions optimizeFor. */
            public optimizeFor: google.protobuf.FileOptions.OptimizeMode;

            /** FileOptions goPackage. */
            public goPackage: string;

            /** FileOptions ccGenericServices. */
            public ccGenericServices: boolean;

            /** FileOptions javaGenericServices. */
            public javaGenericServices: boolean;

            /** FileOptions pyGenericServices. */
            public pyGenericServices: boolean;

            /** FileOptions phpGenericServices. */
            public phpGenericServices: boolean;

            /** FileOptions deprecated. */
            public deprecated: boolean;

            /** FileOptions ccEnableArenas. */
            public ccEnableArenas: boolean;

            /** FileOptions objcClassPrefix. */
            public objcClassPrefix: string;

            /** FileOptions csharpNamespace. */
            public csharpNamespace: string;

            /** FileOptions swiftPrefix. */
            public swiftPrefix: string;

            /** FileOptions phpClassPrefix. */
            public phpClassPrefix: string;

            /** FileOptions phpNamespace. */
            public phpNamespace: string;

            /** FileOptions phpMetadataNamespace. */
            public phpMetadataNamespace: string;

            /** FileOptions rubyPackage. */
            public rubyPackage: string;

            /** FileOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new FileOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FileOptions instance
             */
            public static create(properties?: google.protobuf.IFileOptions): google.protobuf.FileOptions;

            /**
             * Encodes the specified FileOptions message. Does not implicitly {@link google.protobuf.FileOptions.verify|verify} messages.
             * @param message FileOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FileOptions message, length delimited. Does not implicitly {@link google.protobuf.FileOptions.verify|verify} messages.
             * @param message FileOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFileOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileOptions;

            /**
             * Decodes a FileOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FileOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FileOptions;

            /**
             * Verifies a FileOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileOptions;

            /**
             * Creates a plain object from a FileOptions message. Also converts values to other types if specified.
             * @param message FileOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace FileOptions {

            /** OptimizeMode enum. */
            enum OptimizeMode {
                SPEED = 1,
                CODE_SIZE = 2,
                LITE_RUNTIME = 3
            }
        }

        /** Properties of a MessageOptions. */
        interface IMessageOptions {

            /** MessageOptions messageSetWireFormat */
            messageSetWireFormat?: (boolean|null);

            /** MessageOptions noStandardDescriptorAccessor */
            noStandardDescriptorAccessor?: (boolean|null);

            /** MessageOptions deprecated */
            deprecated?: (boolean|null);

            /** MessageOptions mapEntry */
            mapEntry?: (boolean|null);

            /** MessageOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a MessageOptions. */
        class MessageOptions implements IMessageOptions {

            /**
             * Constructs a new MessageOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMessageOptions);

            /** MessageOptions messageSetWireFormat. */
            public messageSetWireFormat: boolean;

            /** MessageOptions noStandardDescriptorAccessor. */
            public noStandardDescriptorAccessor: boolean;

            /** MessageOptions deprecated. */
            public deprecated: boolean;

            /** MessageOptions mapEntry. */
            public mapEntry: boolean;

            /** MessageOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new MessageOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns MessageOptions instance
             */
            public static create(properties?: google.protobuf.IMessageOptions): google.protobuf.MessageOptions;

            /**
             * Encodes the specified MessageOptions message. Does not implicitly {@link google.protobuf.MessageOptions.verify|verify} messages.
             * @param message MessageOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMessageOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified MessageOptions message, length delimited. Does not implicitly {@link google.protobuf.MessageOptions.verify|verify} messages.
             * @param message MessageOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IMessageOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MessageOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MessageOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MessageOptions;

            /**
             * Decodes a MessageOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns MessageOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.MessageOptions;

            /**
             * Verifies a MessageOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MessageOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MessageOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MessageOptions;

            /**
             * Creates a plain object from a MessageOptions message. Also converts values to other types if specified.
             * @param message MessageOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MessageOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MessageOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a FieldOptions. */
        interface IFieldOptions {

            /** FieldOptions ctype */
            ctype?: (google.protobuf.FieldOptions.CType|null);

            /** FieldOptions packed */
            packed?: (boolean|null);

            /** FieldOptions jstype */
            jstype?: (google.protobuf.FieldOptions.JSType|null);

            /** FieldOptions lazy */
            lazy?: (boolean|null);

            /** FieldOptions deprecated */
            deprecated?: (boolean|null);

            /** FieldOptions weak */
            weak?: (boolean|null);

            /** FieldOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a FieldOptions. */
        class FieldOptions implements IFieldOptions {

            /**
             * Constructs a new FieldOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldOptions);

            /** FieldOptions ctype. */
            public ctype: google.protobuf.FieldOptions.CType;

            /** FieldOptions packed. */
            public packed: boolean;

            /** FieldOptions jstype. */
            public jstype: google.protobuf.FieldOptions.JSType;

            /** FieldOptions lazy. */
            public lazy: boolean;

            /** FieldOptions deprecated. */
            public deprecated: boolean;

            /** FieldOptions weak. */
            public weak: boolean;

            /** FieldOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new FieldOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns FieldOptions instance
             */
            public static create(properties?: google.protobuf.IFieldOptions): google.protobuf.FieldOptions;

            /**
             * Encodes the specified FieldOptions message. Does not implicitly {@link google.protobuf.FieldOptions.verify|verify} messages.
             * @param message FieldOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified FieldOptions message, length delimited. Does not implicitly {@link google.protobuf.FieldOptions.verify|verify} messages.
             * @param message FieldOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IFieldOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions;

            /**
             * Decodes a FieldOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns FieldOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.FieldOptions;

            /**
             * Verifies a FieldOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions;

            /**
             * Creates a plain object from a FieldOptions message. Also converts values to other types if specified.
             * @param message FieldOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace FieldOptions {

            /** CType enum. */
            enum CType {
                STRING = 0,
                CORD = 1,
                STRING_PIECE = 2
            }

            /** JSType enum. */
            enum JSType {
                JS_NORMAL = 0,
                JS_STRING = 1,
                JS_NUMBER = 2
            }
        }

        /** Properties of an OneofOptions. */
        interface IOneofOptions {

            /** OneofOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an OneofOptions. */
        class OneofOptions implements IOneofOptions {

            /**
             * Constructs a new OneofOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofOptions);

            /** OneofOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new OneofOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns OneofOptions instance
             */
            public static create(properties?: google.protobuf.IOneofOptions): google.protobuf.OneofOptions;

            /**
             * Encodes the specified OneofOptions message. Does not implicitly {@link google.protobuf.OneofOptions.verify|verify} messages.
             * @param message OneofOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified OneofOptions message, length delimited. Does not implicitly {@link google.protobuf.OneofOptions.verify|verify} messages.
             * @param message OneofOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IOneofOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofOptions;

            /**
             * Decodes an OneofOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns OneofOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.OneofOptions;

            /**
             * Verifies an OneofOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofOptions;

            /**
             * Creates a plain object from an OneofOptions message. Also converts values to other types if specified.
             * @param message OneofOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of an EnumOptions. */
        interface IEnumOptions {

            /** EnumOptions allowAlias */
            allowAlias?: (boolean|null);

            /** EnumOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumOptions. */
        class EnumOptions implements IEnumOptions {

            /**
             * Constructs a new EnumOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumOptions);

            /** EnumOptions allowAlias. */
            public allowAlias: boolean;

            /** EnumOptions deprecated. */
            public deprecated: boolean;

            /** EnumOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new EnumOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns EnumOptions instance
             */
            public static create(properties?: google.protobuf.IEnumOptions): google.protobuf.EnumOptions;

            /**
             * Encodes the specified EnumOptions message. Does not implicitly {@link google.protobuf.EnumOptions.verify|verify} messages.
             * @param message EnumOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified EnumOptions message, length delimited. Does not implicitly {@link google.protobuf.EnumOptions.verify|verify} messages.
             * @param message EnumOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IEnumOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumOptions;

            /**
             * Decodes an EnumOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns EnumOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.EnumOptions;

            /**
             * Verifies an EnumOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumOptions;

            /**
             * Creates a plain object from an EnumOptions message. Also converts values to other types if specified.
             * @param message EnumOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of an EnumValueOptions. */
        interface IEnumValueOptions {

            /** EnumValueOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumValueOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumValueOptions. */
        class EnumValueOptions implements IEnumValueOptions {

            /**
             * Constructs a new EnumValueOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueOptions);

            /** EnumValueOptions deprecated. */
            public deprecated: boolean;

            /** EnumValueOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new EnumValueOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns EnumValueOptions instance
             */
            public static create(properties?: google.protobuf.IEnumValueOptions): google.protobuf.EnumValueOptions;

            /**
             * Encodes the specified EnumValueOptions message. Does not implicitly {@link google.protobuf.EnumValueOptions.verify|verify} messages.
             * @param message EnumValueOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified EnumValueOptions message, length delimited. Does not implicitly {@link google.protobuf.EnumValueOptions.verify|verify} messages.
             * @param message EnumValueOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IEnumValueOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueOptions;

            /**
             * Decodes an EnumValueOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns EnumValueOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.EnumValueOptions;

            /**
             * Verifies an EnumValueOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueOptions;

            /**
             * Creates a plain object from an EnumValueOptions message. Also converts values to other types if specified.
             * @param message EnumValueOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a ServiceOptions. */
        interface IServiceOptions {

            /** ServiceOptions deprecated */
            deprecated?: (boolean|null);

            /** ServiceOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a ServiceOptions. */
        class ServiceOptions implements IServiceOptions {

            /**
             * Constructs a new ServiceOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceOptions);

            /** ServiceOptions deprecated. */
            public deprecated: boolean;

            /** ServiceOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new ServiceOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns ServiceOptions instance
             */
            public static create(properties?: google.protobuf.IServiceOptions): google.protobuf.ServiceOptions;

            /**
             * Encodes the specified ServiceOptions message. Does not implicitly {@link google.protobuf.ServiceOptions.verify|verify} messages.
             * @param message ServiceOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified ServiceOptions message, length delimited. Does not implicitly {@link google.protobuf.ServiceOptions.verify|verify} messages.
             * @param message ServiceOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IServiceOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceOptions;

            /**
             * Decodes a ServiceOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns ServiceOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.ServiceOptions;

            /**
             * Verifies a ServiceOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceOptions;

            /**
             * Creates a plain object from a ServiceOptions message. Also converts values to other types if specified.
             * @param message ServiceOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        /** Properties of a MethodOptions. */
        interface IMethodOptions {

            /** MethodOptions deprecated */
            deprecated?: (boolean|null);

            /** MethodOptions idempotencyLevel */
            idempotencyLevel?: (google.protobuf.MethodOptions.IdempotencyLevel|null);

            /** MethodOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);

            /** MethodOptions .google.api.http */
            ".google.api.http"?: (google.api.IHttpRule|null);
        }

        /** Represents a MethodOptions. */
        class MethodOptions implements IMethodOptions {

            /**
             * Constructs a new MethodOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodOptions);

            /** MethodOptions deprecated. */
            public deprecated: boolean;

            /** MethodOptions idempotencyLevel. */
            public idempotencyLevel: google.protobuf.MethodOptions.IdempotencyLevel;

            /** MethodOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Creates a new MethodOptions instance using the specified properties.
             * @param [properties] Properties to set
             * @returns MethodOptions instance
             */
            public static create(properties?: google.protobuf.IMethodOptions): google.protobuf.MethodOptions;

            /**
             * Encodes the specified MethodOptions message. Does not implicitly {@link google.protobuf.MethodOptions.verify|verify} messages.
             * @param message MethodOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified MethodOptions message, length delimited. Does not implicitly {@link google.protobuf.MethodOptions.verify|verify} messages.
             * @param message MethodOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IMethodOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodOptions;

            /**
             * Decodes a MethodOptions message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns MethodOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.MethodOptions;

            /**
             * Verifies a MethodOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodOptions;

            /**
             * Creates a plain object from a MethodOptions message. Also converts values to other types if specified.
             * @param message MethodOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace MethodOptions {

            /** IdempotencyLevel enum. */
            enum IdempotencyLevel {
                IDEMPOTENCY_UNKNOWN = 0,
                NO_SIDE_EFFECTS = 1,
                IDEMPOTENT = 2
            }
        }

        /** Properties of an UninterpretedOption. */
        interface IUninterpretedOption {

            /** UninterpretedOption name */
            name?: (google.protobuf.UninterpretedOption.INamePart[]|null);

            /** UninterpretedOption identifierValue */
            identifierValue?: (string|null);

            /** UninterpretedOption positiveIntValue */
            positiveIntValue?: (number|Long|null);

            /** UninterpretedOption negativeIntValue */
            negativeIntValue?: (number|Long|null);

            /** UninterpretedOption doubleValue */
            doubleValue?: (number|null);

            /** UninterpretedOption stringValue */
            stringValue?: (Uint8Array|null);

            /** UninterpretedOption aggregateValue */
            aggregateValue?: (string|null);
        }

        /** Represents an UninterpretedOption. */
        class UninterpretedOption implements IUninterpretedOption {

            /**
             * Constructs a new UninterpretedOption.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUninterpretedOption);

            /** UninterpretedOption name. */
            public name: google.protobuf.UninterpretedOption.INamePart[];

            /** UninterpretedOption identifierValue. */
            public identifierValue: string;

            /** UninterpretedOption positiveIntValue. */
            public positiveIntValue: (number|Long);

            /** UninterpretedOption negativeIntValue. */
            public negativeIntValue: (number|Long);

            /** UninterpretedOption doubleValue. */
            public doubleValue: number;

            /** UninterpretedOption stringValue. */
            public stringValue: Uint8Array;

            /** UninterpretedOption aggregateValue. */
            public aggregateValue: string;

            /**
             * Creates a new UninterpretedOption instance using the specified properties.
             * @param [properties] Properties to set
             * @returns UninterpretedOption instance
             */
            public static create(properties?: google.protobuf.IUninterpretedOption): google.protobuf.UninterpretedOption;

            /**
             * Encodes the specified UninterpretedOption message. Does not implicitly {@link google.protobuf.UninterpretedOption.verify|verify} messages.
             * @param message UninterpretedOption message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUninterpretedOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified UninterpretedOption message, length delimited. Does not implicitly {@link google.protobuf.UninterpretedOption.verify|verify} messages.
             * @param message UninterpretedOption message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IUninterpretedOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an UninterpretedOption message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UninterpretedOption
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption;

            /**
             * Decodes an UninterpretedOption message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns UninterpretedOption
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UninterpretedOption;

            /**
             * Verifies an UninterpretedOption message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an UninterpretedOption message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UninterpretedOption
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption;

            /**
             * Creates a plain object from an UninterpretedOption message. Also converts values to other types if specified.
             * @param message UninterpretedOption
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UninterpretedOption, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UninterpretedOption to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace UninterpretedOption {

            /** Properties of a NamePart. */
            interface INamePart {

                /** NamePart namePart */
                namePart: string;

                /** NamePart isExtension */
                isExtension: boolean;
            }

            /** Represents a NamePart. */
            class NamePart implements INamePart {

                /**
                 * Constructs a new NamePart.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.UninterpretedOption.INamePart);

                /** NamePart namePart. */
                public namePart: string;

                /** NamePart isExtension. */
                public isExtension: boolean;

                /**
                 * Creates a new NamePart instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns NamePart instance
                 */
                public static create(properties?: google.protobuf.UninterpretedOption.INamePart): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Encodes the specified NamePart message. Does not implicitly {@link google.protobuf.UninterpretedOption.NamePart.verify|verify} messages.
                 * @param message NamePart message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.UninterpretedOption.INamePart, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified NamePart message, length delimited. Does not implicitly {@link google.protobuf.UninterpretedOption.NamePart.verify|verify} messages.
                 * @param message NamePart message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.UninterpretedOption.INamePart, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a NamePart message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns NamePart
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Decodes a NamePart message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns NamePart
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Verifies a NamePart message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a NamePart message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns NamePart
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Creates a plain object from a NamePart message. Also converts values to other types if specified.
                 * @param message NamePart
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.UninterpretedOption.NamePart, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this NamePart to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }
        }

        /** Properties of a SourceCodeInfo. */
        interface ISourceCodeInfo {

            /** SourceCodeInfo location */
            location?: (google.protobuf.SourceCodeInfo.ILocation[]|null);
        }

        /** Represents a SourceCodeInfo. */
        class SourceCodeInfo implements ISourceCodeInfo {

            /**
             * Constructs a new SourceCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.ISourceCodeInfo);

            /** SourceCodeInfo location. */
            public location: google.protobuf.SourceCodeInfo.ILocation[];

            /**
             * Creates a new SourceCodeInfo instance using the specified properties.
             * @param [properties] Properties to set
             * @returns SourceCodeInfo instance
             */
            public static create(properties?: google.protobuf.ISourceCodeInfo): google.protobuf.SourceCodeInfo;

            /**
             * Encodes the specified SourceCodeInfo message. Does not implicitly {@link google.protobuf.SourceCodeInfo.verify|verify} messages.
             * @param message SourceCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.ISourceCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified SourceCodeInfo message, length delimited. Does not implicitly {@link google.protobuf.SourceCodeInfo.verify|verify} messages.
             * @param message SourceCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.ISourceCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a SourceCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns SourceCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo;

            /**
             * Decodes a SourceCodeInfo message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns SourceCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.SourceCodeInfo;

            /**
             * Verifies a SourceCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a SourceCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns SourceCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo;

            /**
             * Creates a plain object from a SourceCodeInfo message. Also converts values to other types if specified.
             * @param message SourceCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.SourceCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this SourceCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace SourceCodeInfo {

            /** Properties of a Location. */
            interface ILocation {

                /** Location path */
                path?: (number[]|null);

                /** Location span */
                span?: (number[]|null);

                /** Location leadingComments */
                leadingComments?: (string|null);

                /** Location trailingComments */
                trailingComments?: (string|null);

                /** Location leadingDetachedComments */
                leadingDetachedComments?: (string[]|null);
            }

            /** Represents a Location. */
            class Location implements ILocation {

                /**
                 * Constructs a new Location.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.SourceCodeInfo.ILocation);

                /** Location path. */
                public path: number[];

                /** Location span. */
                public span: number[];

                /** Location leadingComments. */
                public leadingComments: string;

                /** Location trailingComments. */
                public trailingComments: string;

                /** Location leadingDetachedComments. */
                public leadingDetachedComments: string[];

                /**
                 * Creates a new Location instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns Location instance
                 */
                public static create(properties?: google.protobuf.SourceCodeInfo.ILocation): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Encodes the specified Location message. Does not implicitly {@link google.protobuf.SourceCodeInfo.Location.verify|verify} messages.
                 * @param message Location message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.SourceCodeInfo.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified Location message, length delimited. Does not implicitly {@link google.protobuf.SourceCodeInfo.Location.verify|verify} messages.
                 * @param message Location message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.SourceCodeInfo.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Location message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Location
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Decodes a Location message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns Location
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Verifies a Location message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Location message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Location
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Creates a plain object from a Location message. Also converts values to other types if specified.
                 * @param message Location
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.SourceCodeInfo.Location, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Location to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }
        }

        /** Properties of a GeneratedCodeInfo. */
        interface IGeneratedCodeInfo {

            /** GeneratedCodeInfo annotation */
            annotation?: (google.protobuf.GeneratedCodeInfo.IAnnotation[]|null);
        }

        /** Represents a GeneratedCodeInfo. */
        class GeneratedCodeInfo implements IGeneratedCodeInfo {

            /**
             * Constructs a new GeneratedCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IGeneratedCodeInfo);

            /** GeneratedCodeInfo annotation. */
            public annotation: google.protobuf.GeneratedCodeInfo.IAnnotation[];

            /**
             * Creates a new GeneratedCodeInfo instance using the specified properties.
             * @param [properties] Properties to set
             * @returns GeneratedCodeInfo instance
             */
            public static create(properties?: google.protobuf.IGeneratedCodeInfo): google.protobuf.GeneratedCodeInfo;

            /**
             * Encodes the specified GeneratedCodeInfo message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.verify|verify} messages.
             * @param message GeneratedCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IGeneratedCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Encodes the specified GeneratedCodeInfo message, length delimited. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.verify|verify} messages.
             * @param message GeneratedCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encodeDelimited(message: google.protobuf.IGeneratedCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a GeneratedCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns GeneratedCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo;

            /**
             * Decodes a GeneratedCodeInfo message from the specified reader or buffer, length delimited.
             * @param reader Reader or buffer to decode from
             * @returns GeneratedCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.GeneratedCodeInfo;

            /**
             * Verifies a GeneratedCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a GeneratedCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns GeneratedCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo;

            /**
             * Creates a plain object from a GeneratedCodeInfo message. Also converts values to other types if specified.
             * @param message GeneratedCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.GeneratedCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this GeneratedCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };
        }

        namespace GeneratedCodeInfo {

            /** Properties of an Annotation. */
            interface IAnnotation {

                /** Annotation path */
                path?: (number[]|null);

                /** Annotation sourceFile */
                sourceFile?: (string|null);

                /** Annotation begin */
                begin?: (number|null);

                /** Annotation end */
                end?: (number|null);
            }

            /** Represents an Annotation. */
            class Annotation implements IAnnotation {

                /**
                 * Constructs a new Annotation.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.GeneratedCodeInfo.IAnnotation);

                /** Annotation path. */
                public path: number[];

                /** Annotation sourceFile. */
                public sourceFile: string;

                /** Annotation begin. */
                public begin: number;

                /** Annotation end. */
                public end: number;

                /**
                 * Creates a new Annotation instance using the specified properties.
                 * @param [properties] Properties to set
                 * @returns Annotation instance
                 */
                public static create(properties?: google.protobuf.GeneratedCodeInfo.IAnnotation): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Encodes the specified Annotation message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.Annotation.verify|verify} messages.
                 * @param message Annotation message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.GeneratedCodeInfo.IAnnotation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Encodes the specified Annotation message, length delimited. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.Annotation.verify|verify} messages.
                 * @param message Annotation message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encodeDelimited(message: google.protobuf.GeneratedCodeInfo.IAnnotation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an Annotation message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Annotation
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Decodes an Annotation message from the specified reader or buffer, length delimited.
                 * @param reader Reader or buffer to decode from
                 * @returns Annotation
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Verifies an Annotation message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an Annotation message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Annotation
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Creates a plain object from an Annotation message. Also converts values to other types if specified.
                 * @param message Annotation
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.GeneratedCodeInfo.Annotation, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Annotation to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };
            }
        }
    }
}
