// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

syntax = "proto3";

package google.cloud.vision.v1p4beta1;

import "google/cloud/vision/v1p4beta1/geometry.proto";

option cc_enable_arenas = true;
option go_package = "cloud.google.com/go/vision/apiv1p4beta1/visionpb;visionpb";
option java_multiple_files = true;
option java_outer_classname = "CelebrityProto";
option java_package = "com.google.cloud.vision.v1p4beta1";
option objc_class_prefix = "GC<PERSON>";

// Parameters for a celebrity recognition request.
message FaceRecognitionParams {
  // The resource names for one or more
  // [CelebritySet][google.cloud.vision.v1p4beta1.CelebritySet]s. A celebrity
  // set is preloaded and can be specified as "builtin/default". If this is
  // specified, the algorithm will try to match the faces detected in the input
  // image to the Celebrities in the CelebritySets.
  repeated string celebrity_set = 1;
}

// A Celebrity is a group of Faces with an identity.
message Celebrity {
  // The resource name of the preloaded Celebrity. Has the format
  // `builtin/{mid}`.
  string name = 1;

  // The Celebrity's display name.
  string display_name = 2;

  // The Celebrity's description.
  string description = 3;
}

// Information about a face's identity.
message FaceRecognitionResult {
  // The [Celebrity][google.cloud.vision.v1p4beta1.Celebrity] that this face was
  // matched to.
  Celebrity celebrity = 1;

  // Recognition confidence. Range [0, 1].
  float confidence = 2;
}
