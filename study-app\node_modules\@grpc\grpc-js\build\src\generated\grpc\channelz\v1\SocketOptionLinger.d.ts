import type { Duration as _google_protobuf_Duration, Duration__Output as _google_protobuf_Duration__Output } from '../../../google/protobuf/Duration';
/**
 * For use with SocketOption's additional field.  This is primarily used for
 * SO_LINGER.
 */
export interface SocketOptionLinger {
    /**
     * active maps to `struct linger.l_onoff`
     */
    'active'?: (boolean);
    /**
     * duration maps to `struct linger.l_linger`
     */
    'duration'?: (_google_protobuf_Duration | null);
}
/**
 * For use with SocketOption's additional field.  This is primarily used for
 * SO_LINGER.
 */
export interface SocketOptionLinger__Output {
    /**
     * active maps to `struct linger.l_onoff`
     */
    'active': (boolean);
    /**
     * duration maps to `struct linger.l_linger`
     */
    'duration': (_google_protobuf_Duration__Output | null);
}
