{"version": 3, "file": "wrappers.js", "sourceRoot": "", "sources": ["../../typescript/src/wrappers.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;AAsBjC,kDAqBC;AAED,sDAsBC;AAjED,mCAA+D;AAE/D,iCAA8B;AAkB9B,SAAgB,mBAAmB,CACjC,GAA4E;IAE5E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,YAAY,UAAU,EAAE,CAAC;QAClE,OAAO,IAAA,yBAAiB,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IACD,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;QAClC,IAAA,aAAM,EACJ,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM,EACrC,0FAA0F,GAAG,CAAC,KAAK,EAAE,CACtG,CAAC;QACF,OAAQ,GAAG,CAAC,KAAkB,CAAC,QAAQ,EAAE,CAAC;IAC5C,CAAC;IACD,wEAAwE;IACxE,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QACjE,OAAO,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IACD,OAAO,GAAG,CAAC,KAAK,CAAC;AACnB,CAAC;AAED,SAAgB,qBAAqB,CACnC,QAAgB,EAChB,IAAsC;IAEtC,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAClB,OAAO;YACL,KAAK,EAAE,IAAI;SACZ,CAAC;IACJ,CAAC;IACD,IAAI,QAAQ,KAAK,6BAA6B,EAAE,CAAC;QAC/C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,gGAAgG,OAAO,IAAI,EAAE,CAC9G,CAAC;QACJ,CAAC;QACD,OAAO;YACL,KAAK,EAAE,IAAA,2BAAmB,EAAC,IAAI,CAAC;SACjC,CAAC;IACJ,CAAC;IACD,OAAO;QACL,KAAK,EAAE,IAAI;KACZ,CAAC;AACJ,CAAC"}