import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import db from '@/lib/database';

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    // Clear all OCR data for this book
    const result = db.prepare(`
      DELETE FROM ocr_text 
      WHERE image_id IN (
        SELECT id FROM images WHERE book_id = ?
      )
    `).run(bookId);

    return NextResponse.json({
      success: true,
      message: `Cleared OCR data for ${result.changes} images`,
      cleared_count: result.changes
    });

  } catch (error) {
    console.error('Error clearing OCR data:', error);
    return NextResponse.json(
      { error: 'Failed to clear OCR data' },
      { status: 500 }
    );
  }
}
