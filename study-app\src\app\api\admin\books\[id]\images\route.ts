import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    // Get images for this book
    const images = BookModel.getImages(bookId);
    
    return NextResponse.json({
      success: true,
      images,
      book: {
        id: book.id,
        title: book.title,
        class_name: book.class_name,
        subject_name: book.subject_name
      }
    });

  } catch (error) {
    console.error('Get book images error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to get images'
    }, { status: 500 });
  }
}
