import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import db from '@/lib/database';
import { generateChapterBasedPageNumbers } from '@/lib/pageNumbering';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    const { pageTypeUpdates } = await request.json();
    
    if (!Array.isArray(pageTypeUpdates)) {
      return NextResponse.json({ error: 'pageTypeUpdates must be an array' }, { status: 400 });
    }

    // Validate page types
    const validPageTypes = [
      'cover', 'contents', 'unassigned',
      ...Array.from({length: 30}, (_, i) => `chapter-${i + 1}`)
    ];

    for (const update of pageTypeUpdates) {
      if (!validPageTypes.includes(update.pageType)) {
        return NextResponse.json({ 
          error: `Invalid page type: ${update.pageType}` 
        }, { status: 400 });
      }
    }

    // Update page types
    const updateStmt = db.prepare(`
      UPDATE images 
      SET page_type = ?
      WHERE id = ? AND book_id = ?
    `);

    const transaction = db.transaction(() => {
      pageTypeUpdates.forEach(update => {
        updateStmt.run(update.pageType, update.imageId, bookId);
      });
    });

    transaction();

    // Generate chapter-based page numbering after updates
    generateChapterBasedPageNumbers(bookId);

    return NextResponse.json({
      success: true,
      message: 'Page types updated successfully'
    });

  } catch (error) {
    console.error('Update page types error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to update page types'
    }, { status: 500 });
  }
}


