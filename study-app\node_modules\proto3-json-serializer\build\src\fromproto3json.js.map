{"version": 3, "file": "fromproto3json.js", "sourceRoot": "", "sources": ["../../typescript/src/fromproto3json.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;AAkBjC,wFAoLC;AAED,wCAWC;AAhND,+BAAsD;AACtD,mCAA4C;AAC5C,iCAAgD;AAEhD,mCAIiB;AACjB,iCAAuE;AACvE,yCAAgE;AAChE,2CAAkE;AAClE,yCAAiD;AACjD,2CAAkE;AAElE,SAAgB,sCAAsC,CACpD,IAA4C,EAC5C,IAAe;IAEf,MAAM,sBAAsB,GAC1B,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,gCAAyB,EAAC,IAAI,CAAC,CAAC;IAEpE,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACjD,kBAAkB;QAClB,IAAI,sBAAsB,KAAK,4BAA4B,EAAE,CAAC;YAC5D,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,IAAA,+BAAwB,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mDAAmD;IACnD,kEAAkE;IAElE,8CAA8C;IAC9C,IAAI,sBAAsB,KAAK,wBAAwB,EAAE,CAAC;QACxD,OAAO,IAAA,yCAAiC,EAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,mBAAY,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CACb,mEAAmE,sBAAsB,sDAAsD,OAAO,IAAI,EAAE,CAC7J,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,gCAAqB,EAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8BAA8B;IAC9B,IAAI,sBAAsB,KAAK,sBAAsB,EAAE,CAAC;QACtD,OAAO,IAAA,qCAA+B,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,sBAAsB,KAAK,yBAAyB,EAAE,CAAC;QACzD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,4FAA4F,OAAO,IAAI,EAAE,CAC1G,CAAC;QACJ,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CACb,mGAAmG,CACpG,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,0CAAkC,EAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE,CAAC;QAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,6CAAqC,EAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,sBAAsB,KAAK,2BAA2B,EAAE,CAAC;QAC3D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,6FAA6F,OAAO,IAAI,EAAE,CAC3G,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,+CAAoC,EAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE,CAAC;QAC5D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,iDAAqC,EAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,sBAAsB,KAAK,4BAA4B,EAAE,CAAC;QAC5D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CACb,8FAA8F,OAAO,IAAI,EAAE,CAC5G,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,iDAAqC,EAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,SAAS;QACX,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACxC,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAE7B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CACb,uEAAuE,GAAG,EAAE,CAC7E,CAAC;gBACJ,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAChC,sCAAsC,CACpC,YAAY,IAAI,SAAS,EACzB,OAAO,CACR,CACF,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC;YACrB,MAAM,GAAG,GAAoB,EAAE,CAAC;YAChC,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvD,GAAG,CAAC,MAAM,CAAC,GAAG,sCAAsC,CAClD,YAAY,IAAI,SAAS,EACzB,QAAqB,CACtB,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;QACpB,CAAC;aAAM,IACL,SAAS,CAAC,KAAK,CAAC,iDAAiD,CAAC,EAClE,CAAC;YACD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC3D,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YAChC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC;aAAM,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;YACjC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CACb,iDAAiD,GAAG,YAAY,KAAK,CAAC,IAAI,yBAAyB,KAAK,EAAE,CAC3G,CAAC;YACJ,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,2BAAmB,EAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,eAAe;YACf,IAAA,aAAM,EACJ,YAAY,KAAK,IAAI,EACrB,iDAAiD,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAC;YACF,MAAM,iBAAiB,GAAG,sCAAsC,CAC9D,YAAa,EACb,KAAK,CACN,CAAC;YACF,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB,CAAC;QAClC,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,cAAc,CAAC,IAAmB,EAAE,IAAe;IACjE,MAAM,YAAY,GAAG,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACxE,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,4HAA4H;IAC5H,IAAA,aAAM,EACJ,OAAO,YAAY,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAChE,2CAA2C,IAAI,EAAE,CAClD,CAAC;IACF,OAAO,IAAI,CAAC,UAAU,CAAC,YAAkB,CAAC,CAAC;AAC7C,CAAC"}