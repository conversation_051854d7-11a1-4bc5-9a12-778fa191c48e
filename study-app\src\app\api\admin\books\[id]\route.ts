import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const existingBook = BookModel.getById(bookId);
    if (!existingBook) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    const { title, class_id, subject_id, description, status } = await request.json();

    // Validate required fields
    if (!title || !class_id || !subject_id) {
      return NextResponse.json(
        { error: 'Title, class, and subject are required' },
        { status: 400 }
      );
    }

    // Validate status
    const validStatuses = ['draft', 'processing', 'completed'];
    if (status && !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: draft, processing, completed' },
        { status: 400 }
      );
    }

    // Update the book
    const success = BookModel.update(bookId, {
      title,
      class_id,
      subject_id,
      description,
      status
    });

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update book' },
        { status: 500 }
      );
    }

    // Return the updated book
    const updatedBook = BookModel.getById(bookId);
    return NextResponse.json({
      success: true,
      book: updatedBook,
      message: 'Book updated successfully'
    });

  } catch (error) {
    console.error('Error updating book:', error);
    return NextResponse.json(
      { error: 'Failed to update book' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const existingBook = BookModel.getById(bookId);
    if (!existingBook) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    // Delete the book
    const success = BookModel.delete(bookId);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete book' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Book deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting book:', error);
    return NextResponse.json(
      { error: 'Failed to delete book' },
      { status: 500 }
    );
  }
}
