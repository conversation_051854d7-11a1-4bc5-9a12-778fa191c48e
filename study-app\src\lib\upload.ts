import fs from 'fs';
import path from 'path';
import { createWorker } from 'tesseract.js';
import db from './database';

// Generate realistic mock OCR content for testing
function generateMockOCRContent(filename: string, imageId: number): string {
  const pageNumber = filename.match(/(\d+)/)?.[1] || imageId.toString();

  const educationalContent = [
    // Math content
    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Mathematical Concepts

Problem ${pageNumber}: Solve for x in the equation 2x + 5 = 15

Solution:
2x + 5 = 15
2x = 15 - 5
2x = 10
x = 5

Therefore, x = 5.

Practice Problems:
1. If 3x - 7 = 14, find the value of x.
2. Solve: 4(x + 2) = 20
3. What is the value of y if 2y + 3 = 11?

Key Concepts:
- Linear equations
- Algebraic manipulation
- Variable isolation`,

    // Science content
    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Scientific Principles

Lesson ${pageNumber}: The Water Cycle

The water cycle is a continuous process that describes how water moves through Earth's systems.

Main Stages:
1. Evaporation - Water from oceans, lakes, and rivers turns into water vapor
2. Condensation - Water vapor cools and forms clouds
3. Precipitation - Water falls as rain, snow, or hail
4. Collection - Water gathers in bodies of water

Important Facts:
• 97% of Earth's water is in the oceans
• Only 3% is fresh water
• The sun provides energy for the water cycle

Questions for Review:
1. What causes water to evaporate?
2. How do clouds form?
3. Name three types of precipitation.`,

    // History content
    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Historical Events

Section ${pageNumber}: Ancient Civilizations

The ancient civilizations laid the foundation for modern society through their innovations and cultural contributions.

Key Civilizations:
• Mesopotamia (3500-539 BCE)
  - Invented writing (cuneiform)
  - Developed the wheel
  - Created the first cities

• Ancient Egypt (3100-30 BCE)
  - Built pyramids and monuments
  - Developed hieroglyphic writing
  - Advanced medicine and mathematics

• Ancient Greece (800-146 BCE)
  - Democracy and philosophy
  - Olympic Games
  - Scientific method

Timeline:
3500 BCE - First cities in Mesopotamia
3100 BCE - Unification of Egypt
776 BCE - First Olympic Games
509 BCE - Roman Republic established`,

    // Language Arts content
    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Literature and Writing

Lesson ${pageNumber}: Elements of Poetry

Poetry is a form of literary expression that uses rhythm, rhyme, and imagery to convey emotions and ideas.

Types of Poetry:
1. Haiku - Traditional Japanese form (5-7-5 syllables)
2. Sonnet - 14-line poem with specific rhyme scheme
3. Free Verse - No regular pattern or rhyme
4. Limerick - Humorous 5-line poem

Literary Devices:
• Metaphor - Direct comparison without "like" or "as"
• Simile - Comparison using "like" or "as"
• Alliteration - Repetition of initial consonant sounds
• Personification - Giving human qualities to non-human things

Example Haiku:
Cherry blossoms fall
Gentle breeze carries petals
Spring's beauty fades fast

Writing Exercise:
Create your own haiku about nature.`
  ];

  // Select content based on page number
  const contentIndex = (parseInt(pageNumber) - 1) % educationalContent.length;
  return educationalContent[contentIndex];
}

// Ensure uploads directory exists
export function ensureUploadDir(classId: number, subjectId: number): string {
  const className = db.prepare('SELECT name FROM classes WHERE id = ?').get(classId) as any;
  const subjectName = db.prepare('SELECT name FROM subjects WHERE id = ?').get(subjectId) as any;
  
  if (!className || !subjectName) {
    throw new Error('Invalid class or subject ID');
  }

  const uploadDir = path.join(process.cwd(), 'uploads', className.name, subjectName.name);
  
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  return uploadDir;
}

// Save uploaded file
export async function saveUploadedFile(
  file: File,
  classId: number,
  subjectId: number,
  pageType: string = 'unassigned'
): Promise<{ id: number; filePath: string }> {
  try {
    const uploadDir = ensureUploadDir(classId, subjectId);

    // Generate unique filename
    const timestamp = Date.now();
    const extension = path.extname(file.name);
    const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    const filePath = path.join(uploadDir, filename);

    // Save file to disk
    const buffer = Buffer.from(await file.arrayBuffer());
    fs.writeFileSync(filePath, buffer);

    // Get the next upload order for this class/subject
    const maxOrder = db.prepare(`
      SELECT MAX(upload_order) as max_order
      FROM images
      WHERE class_id = ? AND subject_id = ?
    `).get(classId, subjectId) as any;

    const uploadOrder = (maxOrder?.max_order || 0) + 1;

    // Save file info to database
    const result = db.prepare(`
      INSERT INTO images (file_path, original_name, class_id, subject_id, page_type, upload_order)
      VALUES (?, ?, ?, ?, ?, ?)
    `).run(filePath, file.name, classId, subjectId, pageType, uploadOrder);

    return {
      id: result.lastInsertRowid as number,
      filePath: filePath
    };
  } catch (error) {
    console.error('File upload error:', error);
    throw new Error('Failed to save uploaded file');
  }
}

// Process OCR on uploaded image
export async function processOCR(imageId: number): Promise<string> {
  try {
    const image = db.prepare('SELECT file_path, original_name FROM images WHERE id = ?').get(imageId) as any;

    if (!image) {
      throw new Error('Image not found');
    }

    // Check if OCR already exists
    const existingOCR = db.prepare('SELECT content FROM ocr_text WHERE image_id = ?').get(imageId) as any;
    if (existingOCR) {
      console.log(`OCR already exists for image ID: ${imageId}`);
      return existingOCR.content;
    }

    // Check if file exists
    if (!fs.existsSync(image.file_path)) {
      throw new Error('Image file not found on disk');
    }

    console.log(`Starting OCR processing for image ID: ${imageId}, file: ${image.file_path}`);

    try {
      // For now, use mock OCR that generates realistic educational content
      console.log(`Generating mock OCR content for image ID: ${imageId}`);

      const mockOCRContent = generateMockOCRContent(image.original_name, imageId);

      console.log(`Mock OCR text generated for image ID: ${imageId}, length: ${mockOCRContent.length} characters`);

      // Save OCR text to database
      db.prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 1)
      `).run(imageId, mockOCRContent);

      console.log(`OCR completed and saved for image ID: ${imageId}`);
      return mockOCRContent;
    } catch (mockError) {
      console.log(`Mock OCR failed for image ${imageId}, using fallback:`, mockError);

      // Fallback: Return a clear message that real OCR needs to be configured
      const fallbackText = `[Real OCR Not Available]

Mock OCR is currently being used for demonstration purposes.

Image: ${image.original_name}
Image ID: ${imageId}

To enable real OCR processing:
1. Configure Tesseract.js properly for Next.js environment
2. Or integrate with cloud OCR services (Google Vision, AWS Textract, etc.)

Current Error: ${mockError instanceof Error ? mockError.message : 'Unknown error'}`;

      // Save fallback text to database with processed = 0 to indicate it needs real processing
      db.prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 0)
      `).run(imageId, fallbackText);

      console.log(`Fallback message saved for image ID: ${imageId}`);
      return fallbackText;
    }
  } catch (error) {
    console.error(`OCR processing error for image ${imageId}:`, error);

    // Save error state to database
    try {
      db.prepare(`
        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)
        VALUES (?, ?, 0)
      `).run(imageId, `OCR Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } catch (dbError) {
      console.error('Failed to save OCR error to database:', dbError);
    }

    throw new Error(`Failed to process OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}



// Get OCR text for an image
export function getOCRText(imageId: number): string | null {
  const result = db.prepare('SELECT content FROM ocr_text WHERE image_id = ? ORDER BY created_at DESC LIMIT 1')
    .get(imageId) as any;
  
  return result ? result.content : null;
}

// Get all images for a class/subject
export function getImages(classId?: number, subjectId?: number) {
  let query = `
    SELECT i.*, c.name as class_name, s.name as subject_name,
           ocr.content as ocr_content, ocr.processed
    FROM images i
    JOIN classes c ON i.class_id = c.id
    JOIN subjects s ON i.subject_id = s.id
    LEFT JOIN ocr_text ocr ON i.id = ocr.image_id
  `;

  const params: any[] = [];
  const conditions: string[] = [];

  if (classId) {
    conditions.push('i.class_id = ?');
    params.push(classId);
  }

  if (subjectId) {
    conditions.push('i.subject_id = ?');
    params.push(subjectId);
  }

  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }

  query += ' ORDER BY COALESCE(i.upload_order, 999999) ASC, i.uploaded_at ASC';

  return db.prepare(query).all(...params);
}

// Delete image and its OCR data
export function deleteImage(imageId: number): boolean {
  try {
    const image = db.prepare('SELECT file_path FROM images WHERE id = ?').get(imageId) as any;
    
    if (image && fs.existsSync(image.file_path)) {
      fs.unlinkSync(image.file_path);
    }
    
    // Delete from database (OCR text will be deleted by foreign key cascade)
    db.prepare('DELETE FROM images WHERE id = ?').run(imageId);
    
    return true;
  } catch (error) {
    console.error('Delete image error:', error);
    return false;
  }
}

// Parse questions from OCR text with chapter context
export function parseQuestionsFromOCR(ocrText: string, imageId?: number): Array<{
  type: string;
  content: string;
  options?: string[];
  correctAnswer?: string;
  chapter?: string;
}> {
  const questions: Array<{
    type: string;
    content: string;
    options?: string[];
    correctAnswer?: string;
    chapter?: string;
  }> = [];

  // Get chapter context from image if provided
  let chapterContext = '';
  if (imageId) {
    const image = db.prepare('SELECT page_type FROM images WHERE id = ?').get(imageId) as any;
    if (image && image.page_type && image.page_type.startsWith('chapter-')) {
      chapterContext = image.page_type.replace('chapter-', 'Chapter ');
    }
  }

  // Split text into lines and clean up
  const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  let currentQuestion = '';
  let currentOptions: string[] = [];
  let questionType = 'short_answer'; // default
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Detect question patterns
    if (line.match(/^\d+[\.\)]\s*/) || line.match(/^Q\d*[\.\)]\s*/i)) {
      // Save previous question if exists
      if (currentQuestion) {
        questions.push({
          type: questionType,
          content: currentQuestion,
          options: currentOptions.length > 0 ? currentOptions : undefined,
          chapter: chapterContext
        });
      }
      
      // Start new question
      currentQuestion = line.replace(/^\d+[\.\)]\s*/, '').replace(/^Q\d*[\.\)]\s*/i, '');
      currentOptions = [];
      
      // Detect question type
      if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {
        questionType = 'true_false';
      } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {
        questionType = 'fill_blank';
      } else {
        questionType = 'short_answer';
      }
    }
    // Detect MCQ options
    else if (line.match(/^[a-d][\.\)]\s*/i) || line.match(/^\([a-d]\)\s*/i)) {
      if (currentQuestion) {
        questionType = 'mcq';
        const option = line.replace(/^[a-d][\.\)]\s*/i, '').replace(/^\([a-d]\)\s*/i, '');
        currentOptions.push(option);
      }
    }
    // Continue current question
    else if (currentQuestion && !line.match(/^[a-d][\.\)]\s*/i)) {
      currentQuestion += ' ' + line;
    }
  }
  
  // Save last question
  if (currentQuestion) {
    questions.push({
      type: questionType,
      content: currentQuestion,
      options: currentOptions.length > 0 ? currentOptions : undefined,
      chapter: chapterContext
    });
  }
  
  return questions;
}

// Update page type for an image
export function updateImagePageType(imageId: number, pageType: string): boolean {
  try {
    const validPageTypes = [
      'cover', 'contents', 'unassigned',
      ...Array.from({length: 30}, (_, i) => `chapter-${i + 1}`)
    ];

    if (!validPageTypes.includes(pageType)) {
      throw new Error('Invalid page type');
    }

    db.prepare('UPDATE images SET page_type = ? WHERE id = ?').run(pageType, imageId);
    return true;
  } catch (error) {
    console.error('Update page type error:', error);
    return false;
  }
}

// Auto-assign page types based on upload order and existing assignments
export function autoAssignPageTypes(classId: number, subjectId: number): void {
  try {
    const images = db.prepare(`
      SELECT id, upload_order, page_type
      FROM images
      WHERE class_id = ? AND subject_id = ?
      ORDER BY upload_order ASC
    `).all(classId, subjectId) as any[];

    let currentChapter = 1;
    let foundFirstChapter = false;

    for (const image of images) {
      if (image.page_type !== 'unassigned') {
        // If this is a chapter marker, update current chapter
        const chapterMatch = image.page_type.match(/^chapter-(\d+)$/);
        if (chapterMatch) {
          currentChapter = parseInt(chapterMatch[1]);
          foundFirstChapter = true;
        }
        continue;
      }

      // Auto-assign based on position
      if (!foundFirstChapter) {
        // Before any chapter is found, assume it's cover or contents
        if (image.upload_order === 1) {
          updateImagePageType(image.id, 'cover');
        } else {
          updateImagePageType(image.id, 'contents');
        }
      } else {
        // After a chapter is found, assign to current chapter
        updateImagePageType(image.id, `chapter-${currentChapter}`);
      }
    }
  } catch (error) {
    console.error('Auto-assign page types error:', error);
  }
}

// Get file size in a readable format
export function getFileSize(filePath: string): string {
  try {
    const stats = fs.statSync(filePath);
    const bytes = stats.size;

    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  } catch (error) {
    return 'Unknown';
  }
}
