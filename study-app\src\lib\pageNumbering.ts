import db from './database';

interface BookImage {
  id: number;
  page_number: number;
  page_type: string;
  book_id: number;
}

/**
 * Generates automatic page numbering based on chapter assignments
 * Following the pattern: cover, contents, chapter-1, chapter-1-P2, chapter-1-P3, chapter-2, chapter-2-P2, etc.
 */
export function generateChapterBasedPageNumbers(bookId: number): void {
  try {
    // Get all images for this book ordered by current page number
    const images = db.prepare(`
      SELECT id, page_number, page_type, book_id
      FROM images
      WHERE book_id = ?
      ORDER BY page_number ASC
    `).all(bookId) as BookImage[];

    if (images.length === 0) return;

    const updates: { id: number; newPageType: string }[] = [];
    let currentChapter = 0;
    let pageInChapter = 1;

    for (let i = 0; i < images.length; i++) {
      const image = images[i];
      let newPageType = image.page_type;

      // Handle special page types that don't change
      if (image.page_type === 'cover' || image.page_type === 'contents') {
        // Keep as is
        continue;
      }

      // Check if this is a chapter marker (chapter-X)
      const chapterMatch = image.page_type.match(/^chapter-(\d+)$/);
      if (chapterMatch) {
        currentChapter = parseInt(chapterMatch[1]);
        pageInChapter = 1;
        // Keep the chapter marker as is
        continue;
      }

      // If we have a current chapter and this is an unassigned or chapter page
      if (currentChapter > 0) {
        if (image.page_type === 'unassigned' || image.page_type.startsWith(`chapter-${currentChapter}`)) {
          if (pageInChapter === 1) {
            // First page of chapter keeps the chapter-X format
            newPageType = `chapter-${currentChapter}`;
          } else {
            // Subsequent pages get chapter-X-PY format
            newPageType = `chapter-${currentChapter}-P${pageInChapter}`;
          }
          pageInChapter++;
        }
      }

      // Only update if the page type changed
      if (newPageType !== image.page_type) {
        updates.push({
          id: image.id,
          newPageType
        });
      }
    }

    // Apply updates in a transaction
    if (updates.length > 0) {
      const updateStmt = db.prepare(`
        UPDATE images 
        SET page_type = ?
        WHERE id = ?
      `);

      const transaction = db.transaction(() => {
        updates.forEach(update => {
          updateStmt.run(update.newPageType, update.id);
        });
      });

      transaction();
    }

  } catch (error) {
    console.error('Error generating chapter-based page numbers:', error);
    throw error;
  }
}

/**
 * Auto-assigns page types based on position and existing chapter markers
 */
export function autoAssignPageTypes(bookId: number): void {
  try {
    // Get all images for this book ordered by page number
    const images = db.prepare(`
      SELECT id, page_type, page_number
      FROM images
      WHERE book_id = ?
      ORDER BY page_number ASC
    `).all(bookId) as BookImage[];

    if (images.length === 0) return;

    let currentChapter = 1;
    let foundFirstChapter = false;
    const updates: { id: number; newPageType: string }[] = [];

    for (const image of images) {
      if (image.page_type !== 'unassigned') {
        // If this is a chapter marker, update current chapter
        const chapterMatch = image.page_type.match(/^chapter-(\d+)$/);
        if (chapterMatch) {
          currentChapter = parseInt(chapterMatch[1]);
          foundFirstChapter = true;
        }
        continue;
      }

      // Auto-assign based on position
      let newPageType = 'unassigned';
      
      if (!foundFirstChapter) {
        // Before any chapter is found, assign based on position
        if (image.page_number === 1) {
          newPageType = 'cover';
        } else if (image.page_number === 2) {
          newPageType = 'contents';
        } else {
          // Start with chapter 1 for remaining pages
          newPageType = 'chapter-1';
          foundFirstChapter = true;
          currentChapter = 1;
        }
      } else {
        // After a chapter is found, assign to current chapter
        newPageType = `chapter-${currentChapter}`;
      }

      if (newPageType !== image.page_type) {
        updates.push({
          id: image.id,
          newPageType
        });
      }
    }

    // Apply updates
    if (updates.length > 0) {
      const updateStmt = db.prepare(`
        UPDATE images 
        SET page_type = ?
        WHERE id = ?
      `);

      const transaction = db.transaction(() => {
        updates.forEach(update => {
          updateStmt.run(update.newPageType, update.id);
        });
      });

      transaction();

      // After auto-assignment, generate chapter-based numbering
      generateChapterBasedPageNumbers(bookId);
    }

  } catch (error) {
    console.error('Error auto-assigning page types:', error);
    throw error;
  }
}

/**
 * Reorders images and updates page numbers
 */
export function reorderImages(bookId: number, imageOrders: { imageId: number; newPosition: number }[]): void {
  try {
    const updateStmt = db.prepare(`
      UPDATE images 
      SET page_number = ?, upload_order = ?
      WHERE id = ? AND book_id = ?
    `);

    const transaction = db.transaction(() => {
      imageOrders.forEach(item => {
        updateStmt.run(item.newPosition, item.newPosition, item.imageId, bookId);
      });
    });

    transaction();

    // After reordering, regenerate chapter-based page numbers
    generateChapterBasedPageNumbers(bookId);

  } catch (error) {
    console.error('Error reordering images:', error);
    throw error;
  }
}

/**
 * Gets the display name for a page type
 */
export function getPageTypeDisplayName(pageType: string): string {
  switch (pageType) {
    case 'cover':
      return 'Cover';
    case 'contents':
      return 'Contents';
    case 'unassigned':
      return 'Unassigned';
    default:
      if (pageType.startsWith('chapter-')) {
        const parts = pageType.split('-');
        if (parts.length === 2) {
          return `Chapter ${parts[1]}`;
        } else if (parts.length === 3 && parts[2].startsWith('P')) {
          return `Chapter ${parts[1]} - Page ${parts[2].substring(1)}`;
        }
      }
      return pageType;
  }
}

/**
 * Gets the color class for a page type badge
 */
export function getPageTypeColor(pageType: string): string {
  switch (pageType) {
    case 'cover':
      return 'bg-purple-100 text-purple-800';
    case 'contents':
      return 'bg-blue-100 text-blue-800';
    case 'unassigned':
      return 'bg-gray-100 text-gray-800';
    default:
      if (pageType.startsWith('chapter-')) {
        return 'bg-green-100 text-green-800';
      }
      return 'bg-gray-100 text-gray-800';
  }
}
