import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import fs from 'fs';
import path from 'path';
import db from '@/lib/database';
import { analyzeFileUpload, generatePageFilename } from '@/lib/fileUtils';

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    
    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 });
    }

    // Create upload directory for this book
    const uploadDir = path.join(process.cwd(), 'uploads', 'books', bookId.toString());
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    const uploadedImages = [];
    const errors = [];
    const warnings = [];

    // Analyze the uploaded files
    const analysis = analyzeFileUpload(Array.from(files));

    // Add validation errors
    errors.push(...analysis.validationErrors);

    // Get the current max page number for this book
    const maxPageResult = db.prepare(`
      SELECT MAX(page_number) as max_page
      FROM images
      WHERE book_id = ?
    `).get(bookId) as any;

    let nextPageNumber = (maxPageResult?.max_page || 0) + 1;

    // Use the sorted files from analysis
    const sortedFiles = analysis.sortedFiles;

    // Process each file in sorted order
    for (let i = 0; i < sortedFiles.length; i++) {
      const file = sortedFiles[i];
      
      try {
        // Skip files that failed validation (already added to errors)
        if (!file.type.startsWith('image/') || file.size > 10 * 1024 * 1024) {
          continue;
        }

        // Generate filename using utility function
        const timestamp = Date.now() + i; // Add index to ensure uniqueness
        const filename = generatePageFilename(file.name, nextPageNumber, timestamp);
        const filePath = path.join(uploadDir, filename);

        // Save file to disk
        const buffer = Buffer.from(await file.arrayBuffer());
        fs.writeFileSync(filePath, buffer);

        // Insert into database
        const result = db.prepare(`
          INSERT INTO images (
            file_path, 
            original_name, 
            class_id, 
            subject_id, 
            book_id, 
            page_number, 
            page_type, 
            upload_order
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          filePath,
          file.name,
          book.class_id,
          book.subject_id,
          bookId,
          nextPageNumber,
          'unassigned',
          nextPageNumber
        );

        uploadedImages.push({
          id: result.lastInsertRowid,
          original_name: file.name,
          page_number: nextPageNumber,
          page_type: 'unassigned',
          file_path: filePath
        });

        nextPageNumber++;

      } catch (fileError) {
        console.error(`Error processing file ${file.name}:`, fileError);
        errors.push(`Failed to process ${file.name}`);
      }
    }

    // Update book status and total pages
    if (uploadedImages.length > 0) {
      const totalPages = db.prepare(`
        SELECT COUNT(*) as count
        FROM images
        WHERE book_id = ?
      `).get(bookId) as any;

      BookModel.update(bookId, {
        total_pages: totalPages.count,
        status: uploadedImages.length > 0 ? 'processing' : book.status
      });
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${uploadedImages.length} images`,
      uploaded_count: uploadedImages.length,
      total_files: files.length,
      errors: errors.length > 0 ? errors : undefined,
      analysis: {
        sequence_detected: analysis.sequenceInfo.isSequential,
        recommendations: analysis.recommendations,
        gaps: analysis.sequenceInfo.gaps,
        duplicates: analysis.sequenceInfo.duplicates
      },
      images: uploadedImages
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Upload failed'
    }, { status: 500 });
  }
}
