/**
 * @license
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
/// <reference types="node" />
import * as Protobuf from 'protobufjs';
import * as descriptor from 'protobufjs/ext/descriptor';
import { Options } from './util';
import Long = require('long');
export { Options, Long };
/**
 * This type exists for use with code generated by the proto-loader-gen-types
 * tool. This type should be used with another interface, e.g.
 * MessageType & AnyExtension for an object that is converted to or from a
 * google.protobuf.Any message.
 * For example, when processing an Any message:
 *
 * ```ts
 * if (isAnyExtension(message)) {
 *   switch (message['@type']) {
 *     case TYPE1_URL:
 *       handleType1(message as AnyExtension & Type1);
 *       break;
 *     case TYPE2_URL:
 *       handleType2(message as AnyExtension & Type2);
 *       break;
 *     // ...
 *   }
 * }
 * ```
 */
export interface AnyExtension {
    /**
     * The fully qualified name of the message type that this object represents,
     * possibly including a URL prefix.
     */
    '@type': string;
}
export declare function isAnyExtension(obj: object): obj is AnyExtension;
declare module 'protobufjs' {
    interface Type {
        toDescriptor(protoVersion: string): Protobuf.Message<descriptor.IDescriptorProto> & descriptor.IDescriptorProto;
    }
    interface RootConstructor {
        new (options?: Options): Root;
        fromDescriptor(descriptorSet: descriptor.IFileDescriptorSet | Protobuf.Reader | Uint8Array): Root;
        fromJSON(json: Protobuf.INamespace, root?: Root): Root;
    }
    interface Root {
        toDescriptor(protoVersion: string): Protobuf.Message<descriptor.IFileDescriptorSet> & descriptor.IFileDescriptorSet;
    }
    interface Enum {
        toDescriptor(protoVersion: string): Protobuf.Message<descriptor.IEnumDescriptorProto> & descriptor.IEnumDescriptorProto;
    }
}
export interface Serialize<T> {
    (value: T): Buffer;
}
export interface Deserialize<T> {
    (bytes: Buffer): T;
}
export interface ProtobufTypeDefinition {
    format: string;
    type: object;
    fileDescriptorProtos: Buffer[];
}
export interface MessageTypeDefinition extends ProtobufTypeDefinition {
    format: 'Protocol Buffer 3 DescriptorProto';
}
export interface EnumTypeDefinition extends ProtobufTypeDefinition {
    format: 'Protocol Buffer 3 EnumDescriptorProto';
}
export declare enum IdempotencyLevel {
    IDEMPOTENCY_UNKNOWN = "IDEMPOTENCY_UNKNOWN",
    NO_SIDE_EFFECTS = "NO_SIDE_EFFECTS",
    IDEMPOTENT = "IDEMPOTENT"
}
export interface NamePart {
    name_part: string;
    is_extension: boolean;
}
export interface UninterpretedOption {
    name?: NamePart[];
    identifier_value?: string;
    positive_int_value?: number;
    negative_int_value?: number;
    double_value?: number;
    string_value?: string;
    aggregate_value?: string;
}
export interface MethodOptions {
    deprecated: boolean;
    idempotency_level: IdempotencyLevel;
    uninterpreted_option: UninterpretedOption[];
    [k: string]: unknown;
}
export interface MethodDefinition<RequestType, ResponseType, OutputRequestType = RequestType, OutputResponseType = ResponseType> {
    path: string;
    requestStream: boolean;
    responseStream: boolean;
    requestSerialize: Serialize<RequestType>;
    responseSerialize: Serialize<ResponseType>;
    requestDeserialize: Deserialize<OutputRequestType>;
    responseDeserialize: Deserialize<OutputResponseType>;
    originalName?: string;
    requestType: MessageTypeDefinition;
    responseType: MessageTypeDefinition;
    options: MethodOptions;
}
export interface ServiceDefinition {
    [index: string]: MethodDefinition<object, object>;
}
export declare type AnyDefinition = ServiceDefinition | MessageTypeDefinition | EnumTypeDefinition;
export interface PackageDefinition {
    [index: string]: AnyDefinition;
}
/**
 * Load a .proto file with the specified options.
 * @param filename One or multiple file paths to load. Can be an absolute path
 *     or relative to an include path.
 * @param options.keepCase Preserve field names. The default is to change them
 *     to camel case.
 * @param options.longs The type that should be used to represent `long` values.
 *     Valid options are `Number` and `String`. Defaults to a `Long` object type
 *     from a library.
 * @param options.enums The type that should be used to represent `enum` values.
 *     The only valid option is `String`. Defaults to the numeric value.
 * @param options.bytes The type that should be used to represent `bytes`
 *     values. Valid options are `Array` and `String`. The default is to use
 *     `Buffer`.
 * @param options.defaults Set default values on output objects. Defaults to
 *     `false`.
 * @param options.arrays Set empty arrays for missing array values even if
 *     `defaults` is `false`. Defaults to `false`.
 * @param options.objects Set empty objects for missing object values even if
 *     `defaults` is `false`. Defaults to `false`.
 * @param options.oneofs Set virtual oneof properties to the present field's
 *     name
 * @param options.json Represent Infinity and NaN as strings in float fields,
 *     and automatically decode google.protobuf.Any values.
 * @param options.includeDirs Paths to search for imported `.proto` files.
 */
export declare function load(filename: string | string[], options?: Options): Promise<PackageDefinition>;
export declare function loadSync(filename: string | string[], options?: Options): PackageDefinition;
export declare function fromJSON(json: Protobuf.INamespace, options?: Options): PackageDefinition;
export declare function loadFileDescriptorSetFromBuffer(descriptorSet: Buffer, options?: Options): PackageDefinition;
export declare function loadFileDescriptorSetFromObject(descriptorSet: Parameters<typeof descriptor.FileDescriptorSet.fromObject>[0], options?: Options): PackageDefinition;
