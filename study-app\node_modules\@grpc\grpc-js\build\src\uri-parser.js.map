{"version": 3, "file": "uri-parser.js", "sourceRoot": "", "sources": ["../../src/uri-parser.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;AAgBH,4BAUC;AASD,sCAmDC;AAED,0CAWC;AAED,kCAUC;AAvGD;;;;;GAKG;AACH,MAAM,SAAS,GAAG,iDAAiD,CAAC;AAEpE,SAAgB,QAAQ,CAAC,SAAiB;IACxC,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC5C,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO;QACL,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;QACpB,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;QACvB,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;KACnB,CAAC;AACJ,CAAC;AAOD,MAAM,YAAY,GAAG,OAAO,CAAC;AAE7B,SAAgB,aAAa,CAAC,IAAY;IACxC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACxC;oDAC4C;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;gBAC/C,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAClC,OAAO;wBACL,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,CAAC,UAAU;qBAClB,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI;aACL,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClC;;kCAE0B;QAC1B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,OAAO;oBACL,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;oBAClB,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;iBACpB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI,EAAE,IAAI;aACX,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,eAAe,CAAC,QAAkB;IAChD,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAChC,OAAO,QAAQ,CAAC,IAAI,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,2CAA2C;QAC3C,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC7C,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAgB,WAAW,CAAC,GAAY;IACtC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAC7B,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;IAC7B,CAAC;IACD,IAAI,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;QAChC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;IACvC,CAAC;IACD,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC;IACnB,OAAO,MAAM,CAAC;AAChB,CAAC"}