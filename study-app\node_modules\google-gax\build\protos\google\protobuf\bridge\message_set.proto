// Protocol Buffers - Google's data interchange format
// Copyright 2007 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Kenton Varda)
//
// DEPRECATED: DO NOT USE FOR NEW FIELDS.
syntax = "proto2";

package google.protobuf.bridge;

option java_outer_classname = "MessageSetProtos";
option java_multiple_files = true;
option objc_class_prefix = "GPB";

// This is proto2's version of MessageSet.
//
// DEPRECATED: DO NOT USE FOR NEW FIELDS.
//
// If you are using editions or proto2, please make your own extendable messages
// for your use case.
// If you are using proto3, please use `Any` instead.
//
// MessageSet was the implementation of extensions for proto1.
// When proto2 was introduced, extensions were implemented as a first-class
// feature.
// This schema for MessageSet was meant to be a "bridge" solution to migrate
// MessageSet-bearing messages from proto1 to google.protobuf.
//
// This schema has been open-sourced only to facilitate the migration of
// Google products with MessageSet-bearing messages to open-source environments.
message MessageSet {
  option deprecated = true;
  option message_set_wire_format = true;

  extensions 4 to max
  [verification = UNVERIFIED];
}
