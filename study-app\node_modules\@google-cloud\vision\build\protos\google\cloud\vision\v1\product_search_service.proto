// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.vision.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/vision/v1/geometry.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option cc_enable_arenas = true;
option go_package = "cloud.google.com/go/vision/v2/apiv1/visionpb;visionpb";
option java_multiple_files = true;
option java_outer_classname = "ProductSearchServiceProto";
option java_package = "com.google.cloud.vision.v1";
option objc_class_prefix = "GCVN";

// Manages Products and ProductSets of reference images for use in product
// search. It uses the following resource model:
//
// - The API has a collection of [ProductSet][google.cloud.vision.v1.ProductSet]
// resources, named `projects/*/locations/*/productSets/*`, which acts as a way
// to put different products into groups to limit identification.
//
// In parallel,
//
// - The API has a collection of [Product][google.cloud.vision.v1.Product]
// resources, named
//   `projects/*/locations/*/products/*`
//
// - Each [Product][google.cloud.vision.v1.Product] has a collection of
// [ReferenceImage][google.cloud.vision.v1.ReferenceImage] resources, named
//   `projects/*/locations/*/products/*/referenceImages/*`
service ProductSearch {
  option (google.api.default_host) = "vision.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-vision";

  // Creates and returns a new ProductSet resource.
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if display_name is missing, or is longer than
  //   4096 characters.
  rpc CreateProductSet(CreateProductSetRequest) returns (ProductSet) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/productSets"
      body: "product_set"
    };
    option (google.api.method_signature) = "parent,product_set,product_set_id";
  }

  // Lists ProductSets in an unspecified order.
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if page_size is greater than 100, or less
  //   than 1.
  rpc ListProductSets(ListProductSetsRequest)
      returns (ListProductSetsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/productSets"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information associated with a ProductSet.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the ProductSet does not exist.
  rpc GetProductSet(GetProductSetRequest) returns (ProductSet) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/productSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Makes changes to a ProductSet resource.
  // Only display_name can be updated currently.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the ProductSet does not exist.
  // * Returns INVALID_ARGUMENT if display_name is present in update_mask but
  //   missing from the request or longer than 4096 characters.
  rpc UpdateProductSet(UpdateProductSetRequest) returns (ProductSet) {
    option (google.api.http) = {
      patch: "/v1/{product_set.name=projects/*/locations/*/productSets/*}"
      body: "product_set"
    };
    option (google.api.method_signature) = "product_set,update_mask";
  }

  // Permanently deletes a ProductSet. Products and ReferenceImages in the
  // ProductSet are not deleted.
  //
  // The actual image files are not deleted from Google Cloud Storage.
  rpc DeleteProductSet(DeleteProductSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/productSets/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates and returns a new product resource.
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if display_name is missing or longer than 4096
  //   characters.
  // * Returns INVALID_ARGUMENT if description is longer than 4096 characters.
  // * Returns INVALID_ARGUMENT if product_category is missing or invalid.
  rpc CreateProduct(CreateProductRequest) returns (Product) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/products"
      body: "product"
    };
    option (google.api.method_signature) = "parent,product,product_id";
  }

  // Lists products in an unspecified order.
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if page_size is greater than 100 or less than 1.
  rpc ListProducts(ListProductsRequest) returns (ListProductsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/products"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information associated with a Product.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the Product does not exist.
  rpc GetProduct(GetProductRequest) returns (Product) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/products/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Makes changes to a Product resource.
  // Only the `display_name`, `description`, and `labels` fields can be updated
  // right now.
  //
  // If labels are updated, the change will not be reflected in queries until
  // the next index time.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the Product does not exist.
  // * Returns INVALID_ARGUMENT if display_name is present in update_mask but is
  //   missing from the request or longer than 4096 characters.
  // * Returns INVALID_ARGUMENT if description is present in update_mask but is
  //   longer than 4096 characters.
  // * Returns INVALID_ARGUMENT if product_category is present in update_mask.
  rpc UpdateProduct(UpdateProductRequest) returns (Product) {
    option (google.api.http) = {
      patch: "/v1/{product.name=projects/*/locations/*/products/*}"
      body: "product"
    };
    option (google.api.method_signature) = "product,update_mask";
  }

  // Permanently deletes a product and its reference images.
  //
  // Metadata of the product and all its images will be deleted right away, but
  // search queries against ProductSets containing the product may still work
  // until all related caches are refreshed.
  rpc DeleteProduct(DeleteProductRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/products/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates and returns a new ReferenceImage resource.
  //
  // The `bounding_poly` field is optional. If `bounding_poly` is not specified,
  // the system will try to detect regions of interest in the image that are
  // compatible with the product_category on the parent product. If it is
  // specified, detection is ALWAYS skipped. The system converts polygons into
  // non-rotated rectangles.
  //
  // Note that the pipeline will resize the image if the image resolution is too
  // large to process (above 50MP).
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if the image_uri is missing or longer than 4096
  //   characters.
  // * Returns INVALID_ARGUMENT if the product does not exist.
  // * Returns INVALID_ARGUMENT if bounding_poly is not provided, and nothing
  //   compatible with the parent product's product_category is detected.
  // * Returns INVALID_ARGUMENT if bounding_poly contains more than 10 polygons.
  rpc CreateReferenceImage(CreateReferenceImageRequest)
      returns (ReferenceImage) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/products/*}/referenceImages"
      body: "reference_image"
    };
    option (google.api.method_signature) =
        "parent,reference_image,reference_image_id";
  }

  // Permanently deletes a reference image.
  //
  // The image metadata will be deleted right away, but search queries
  // against ProductSets containing the image may still work until all related
  // caches are refreshed.
  //
  // The actual image files are not deleted from Google Cloud Storage.
  rpc DeleteReferenceImage(DeleteReferenceImageRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/products/*/referenceImages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists reference images.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the parent product does not exist.
  // * Returns INVALID_ARGUMENT if the page_size is greater than 100, or less
  //   than 1.
  rpc ListReferenceImages(ListReferenceImagesRequest)
      returns (ListReferenceImagesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/products/*}/referenceImages"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets information associated with a ReferenceImage.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the specified image does not exist.
  rpc GetReferenceImage(GetReferenceImageRequest) returns (ReferenceImage) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/products/*/referenceImages/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Adds a Product to the specified ProductSet. If the Product is already
  // present, no change is made.
  //
  // One Product can be added to at most 100 ProductSets.
  //
  // Possible errors:
  //
  // * Returns NOT_FOUND if the Product or the ProductSet doesn't exist.
  rpc AddProductToProductSet(AddProductToProductSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/productSets/*}:addProduct"
      body: "*"
    };
    option (google.api.method_signature) = "name,product";
  }

  // Removes a Product from the specified ProductSet.
  rpc RemoveProductFromProductSet(RemoveProductFromProductSetRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/productSets/*}:removeProduct"
      body: "*"
    };
    option (google.api.method_signature) = "name,product";
  }

  // Lists the Products in a ProductSet, in an unspecified order. If the
  // ProductSet does not exist, the products field of the response will be
  // empty.
  //
  // Possible errors:
  //
  // * Returns INVALID_ARGUMENT if page_size is greater than 100 or less than 1.
  rpc ListProductsInProductSet(ListProductsInProductSetRequest)
      returns (ListProductsInProductSetResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/productSets/*}/products"
    };
    option (google.api.method_signature) = "name";
  }

  // Asynchronous API that imports a list of reference images to specified
  // product sets based on a list of image information.
  //
  // The [google.longrunning.Operation][google.longrunning.Operation] API can be
  // used to keep track of the progress and results of the request.
  // `Operation.metadata` contains `BatchOperationMetadata`. (progress)
  // `Operation.response` contains `ImportProductSetsResponse`. (results)
  //
  // The input source of this method is a csv file on Google Cloud Storage.
  // For the format of the csv file please see
  // [ImportProductSetsGcsSource.csv_file_uri][google.cloud.vision.v1.ImportProductSetsGcsSource.csv_file_uri].
  rpc ImportProductSets(ImportProductSetsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/productSets:import"
      body: "*"
    };
    option (google.api.method_signature) = "parent,input_config";
    option (google.longrunning.operation_info) = {
      response_type: "ImportProductSetsResponse"
      metadata_type: "BatchOperationMetadata"
    };
  }

  // Asynchronous API to delete all Products in a ProductSet or all Products
  // that are in no ProductSet.
  //
  // If a Product is a member of the specified ProductSet in addition to other
  // ProductSets, the Product will still be deleted.
  //
  // It is recommended to not delete the specified ProductSet until after this
  // operation has completed. It is also recommended to not add any of the
  // Products involved in the batch delete to a new ProductSet while this
  // operation is running because those Products may still end up deleted.
  //
  // It's not possible to undo the PurgeProducts operation. Therefore, it is
  // recommended to keep the csv files used in ImportProductSets (if that was
  // how you originally built the Product Set) before starting PurgeProducts, in
  // case you need to re-import the data after deletion.
  //
  // If the plan is to purge all of the Products from a ProductSet and then
  // re-use the empty ProductSet to re-import new Products into the empty
  // ProductSet, you must wait until the PurgeProducts operation has finished
  // for that ProductSet.
  //
  // The [google.longrunning.Operation][google.longrunning.Operation] API can be
  // used to keep track of the progress and results of the request.
  // `Operation.metadata` contains `BatchOperationMetadata`. (progress)
  rpc PurgeProducts(PurgeProductsRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/products:purge"
      body: "*"
    };
    option (google.api.method_signature) = "parent";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "BatchOperationMetadata"
    };
  }
}

// A Product contains ReferenceImages.
message Product {
  option (google.api.resource) = {
    type: "vision.googleapis.com/Product"
    pattern: "projects/{project}/locations/{location}/products/{product}"
  };

  // A product label represented as a key-value pair.
  message KeyValue {
    // The key of the label attached to the product. Cannot be empty and cannot
    // exceed 128 bytes.
    string key = 1;

    // The value of the label attached to the product. Cannot be empty and
    // cannot exceed 128 bytes.
    string value = 2;
  }

  // The resource name of the product.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
  //
  // This field is ignored when creating a product.
  string name = 1;

  // The user-provided name for this Product. Must not be empty. Must be at most
  // 4096 characters long.
  string display_name = 2;

  // User-provided metadata to be stored with this product. Must be at most 4096
  // characters long.
  string description = 3;

  // Immutable. The category for the product identified by the reference image.
  // This should be one of "homegoods-v2", "apparel-v2", "toys-v2",
  // "packagedgoods-v1" or "general-v1". The legacy categories "homegoods",
  // "apparel", and "toys" are still supported, but these should not be used for
  // new products.
  string product_category = 4 [(google.api.field_behavior) = IMMUTABLE];

  // Key-value pairs that can be attached to a product. At query time,
  // constraints can be specified based on the product_labels.
  //
  // Note that integer values can be provided as strings, e.g. "1199". Only
  // strings with integer values can match a range-based restriction which is
  // to be supported soon.
  //
  // Multiple values can be assigned to the same key. One product may have up to
  // 500 product_labels.
  //
  // Notice that the total number of distinct product_labels over all products
  // in one ProductSet cannot exceed 1M, otherwise the product search pipeline
  // will refuse to work for that ProductSet.
  repeated KeyValue product_labels = 5;
}

// A ProductSet contains Products. A ProductSet can contain a maximum of 1
// million reference images. If the limit is exceeded, periodic indexing will
// fail.
message ProductSet {
  option (google.api.resource) = {
    type: "vision.googleapis.com/ProductSet"
    pattern: "projects/{project}/locations/{location}/productSets/{product_set}"
  };

  // The resource name of the ProductSet.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`.
  //
  // This field is ignored when creating a ProductSet.
  string name = 1;

  // The user-provided name for this ProductSet. Must not be empty. Must be at
  // most 4096 characters long.
  string display_name = 2;

  // Output only. The time at which this ProductSet was last indexed. Query
  // results will reflect all updates before this time. If this ProductSet has
  // never been indexed, this timestamp is the default value
  // "1970-01-01T00:00:00Z".
  //
  // This field is ignored when creating a ProductSet.
  google.protobuf.Timestamp index_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If there was an error with indexing the product set, the field
  // is populated.
  //
  // This field is ignored when creating a ProductSet.
  google.rpc.Status index_error = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A `ReferenceImage` represents a product image and its associated metadata,
// such as bounding boxes.
message ReferenceImage {
  option (google.api.resource) = {
    type: "vision.googleapis.com/ReferenceImage"
    pattern: "projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}"
  };

  // The resource name of the reference image.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
  //
  // This field is ignored when creating a reference image.
  string name = 1;

  // Required. The Google Cloud Storage URI of the reference image.
  //
  // The URI must start with `gs://`.
  string uri = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. Bounding polygons around the areas of interest in the reference
  // image. If this field is empty, the system will try to detect regions of
  // interest. At most 10 bounding polygons will be used.
  //
  // The provided shape is converted into a non-rotated rectangle. Once
  // converted, the small edge of the rectangle must be greater than or equal
  // to 300 pixels. The aspect ratio must be 1:4 or less (i.e. 1:3 is ok; 1:5
  // is not).
  repeated BoundingPoly bounding_polys = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Request message for the `CreateProduct` method.
message CreateProductRequest {
  // Required. The project in which the Product should be created.
  //
  // Format is
  // `projects/PROJECT_ID/locations/LOC_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The product to create.
  Product product = 2 [(google.api.field_behavior) = REQUIRED];

  // A user-supplied resource id for this Product. If set, the server will
  // attempt to use this value as the resource id. If it is already in use, an
  // error is returned with code ALREADY_EXISTS. Must be at most 128 characters
  // long. It cannot contain the character `/`.
  string product_id = 3;
}

// Request message for the `ListProducts` method.
message ListProductsRequest {
  // Required. The project OR ProductSet from which Products should be listed.
  //
  // Format:
  // `projects/PROJECT_ID/locations/LOC_ID`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return. Default 10, maximum 100.
  int32 page_size = 2;

  // The next_page_token returned from a previous List request, if any.
  string page_token = 3;
}

// Response message for the `ListProducts` method.
message ListProductsResponse {
  // List of products.
  repeated Product products = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for the `GetProduct` method.
message GetProductRequest {
  // Required. Resource name of the Product to get.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];
}

// Request message for the `UpdateProduct` method.
message UpdateProductRequest {
  // Required. The Product resource which replaces the one on the server.
  // product.name is immutable.
  Product product = 1 [(google.api.field_behavior) = REQUIRED];

  // The [FieldMask][google.protobuf.FieldMask] that specifies which fields
  // to update.
  // If update_mask isn't specified, all mutable fields are to be updated.
  // Valid mask paths include `product_labels`, `display_name`, and
  // `description`.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the `DeleteProduct` method.
message DeleteProductRequest {
  // Required. Resource name of product to delete.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];
}

// Request message for the `CreateProductSet` method.
message CreateProductSetRequest {
  // Required. The project in which the ProductSet should be created.
  //
  // Format is `projects/PROJECT_ID/locations/LOC_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The ProductSet to create.
  ProductSet product_set = 2 [(google.api.field_behavior) = REQUIRED];

  // A user-supplied resource id for this ProductSet. If set, the server will
  // attempt to use this value as the resource id. If it is already in use, an
  // error is returned with code ALREADY_EXISTS. Must be at most 128 characters
  // long. It cannot contain the character `/`.
  string product_set_id = 3;
}

// Request message for the `ListProductSets` method.
message ListProductSetsRequest {
  // Required. The project from which ProductSets should be listed.
  //
  // Format is `projects/PROJECT_ID/locations/LOC_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The maximum number of items to return. Default 10, maximum 100.
  int32 page_size = 2;

  // The next_page_token returned from a previous List request, if any.
  string page_token = 3;
}

// Response message for the `ListProductSets` method.
message ListProductSetsResponse {
  // List of ProductSets.
  repeated ProductSet product_sets = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Request message for the `GetProductSet` method.
message GetProductSetRequest {
  // Required. Resource name of the ProductSet to get.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ProductSet"
    }
  ];
}

// Request message for the `UpdateProductSet` method.
message UpdateProductSetRequest {
  // Required. The ProductSet resource which replaces the one on the server.
  ProductSet product_set = 1 [(google.api.field_behavior) = REQUIRED];

  // The [FieldMask][google.protobuf.FieldMask] that specifies which fields to
  // update.
  // If update_mask isn't specified, all mutable fields are to be updated.
  // Valid mask path is `display_name`.
  google.protobuf.FieldMask update_mask = 2;
}

// Request message for the `DeleteProductSet` method.
message DeleteProductSetRequest {
  // Required. Resource name of the ProductSet to delete.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ProductSet"
    }
  ];
}

// Request message for the `CreateReferenceImage` method.
message CreateReferenceImageRequest {
  // Required. Resource name of the product in which to create the reference
  // image.
  //
  // Format is
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];

  // Required. The reference image to create.
  // If an image ID is specified, it is ignored.
  ReferenceImage reference_image = 2 [(google.api.field_behavior) = REQUIRED];

  // A user-supplied resource id for the ReferenceImage to be added. If set,
  // the server will attempt to use this value as the resource id. If it is
  // already in use, an error is returned with code ALREADY_EXISTS. Must be at
  // most 128 characters long. It cannot contain the character `/`.
  string reference_image_id = 3;
}

// Request message for the `ListReferenceImages` method.
message ListReferenceImagesRequest {
  // Required. Resource name of the product containing the reference images.
  //
  // Format is
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];

  // The maximum number of items to return. Default 10, maximum 100.
  int32 page_size = 2;

  // A token identifying a page of results to be returned. This is the value
  // of `nextPageToken` returned in a previous reference image list request.
  //
  // Defaults to the first page if not specified.
  string page_token = 3;
}

// Response message for the `ListReferenceImages` method.
message ListReferenceImagesResponse {
  // The list of reference images.
  repeated ReferenceImage reference_images = 1;

  // The maximum number of items to return. Default 10, maximum 100.
  int32 page_size = 2;

  // The next_page_token returned from a previous List request, if any.
  string next_page_token = 3;
}

// Request message for the `GetReferenceImage` method.
message GetReferenceImageRequest {
  // Required. The resource name of the ReferenceImage to get.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ReferenceImage"
    }
  ];
}

// Request message for the `DeleteReferenceImage` method.
message DeleteReferenceImageRequest {
  // Required. The resource name of the reference image to delete.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ReferenceImage"
    }
  ];
}

// Request message for the `AddProductToProductSet` method.
message AddProductToProductSetRequest {
  // Required. The resource name for the ProductSet to modify.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ProductSet"
    }
  ];

  // Required. The resource name for the Product to be added to this ProductSet.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  string product = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];
}

// Request message for the `RemoveProductFromProductSet` method.
message RemoveProductFromProductSetRequest {
  // Required. The resource name for the ProductSet to modify.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ProductSet"
    }
  ];

  // Required. The resource name for the Product to be removed from this
  // ProductSet.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  string product = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "vision.googleapis.com/Product" }
  ];
}

// Request message for the `ListProductsInProductSet` method.
message ListProductsInProductSetRequest {
  // Required. The ProductSet resource for which to retrieve Products.
  //
  // Format is:
  // `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vision.googleapis.com/ProductSet"
    }
  ];

  // The maximum number of items to return. Default 10, maximum 100.
  int32 page_size = 2;

  // The next_page_token returned from a previous List request, if any.
  string page_token = 3;
}

// Response message for the `ListProductsInProductSet` method.
message ListProductsInProductSetResponse {
  // The list of Products.
  repeated Product products = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The Google Cloud Storage location for a csv file which preserves a list of
// ImportProductSetRequests in each line.
message ImportProductSetsGcsSource {
  // The Google Cloud Storage URI of the input csv file.
  //
  // The URI must start with `gs://`.
  //
  // The format of the input csv file should be one image per line.
  // In each line, there are 8 columns.
  //
  // 1.  image-uri
  // 2.  image-id
  // 3.  product-set-id
  // 4.  product-id
  // 5.  product-category
  // 6.  product-display-name
  // 7.  labels
  // 8.  bounding-poly
  //
  // The `image-uri`, `product-set-id`, `product-id`, and `product-category`
  // columns are required. All other columns are optional.
  //
  // If the `ProductSet` or `Product` specified by the `product-set-id` and
  // `product-id` values does not exist, then the system will create a new
  // `ProductSet` or `Product` for the image. In this case, the
  // `product-display-name` column refers to
  // [display_name][google.cloud.vision.v1.Product.display_name], the
  // `product-category` column refers to
  // [product_category][google.cloud.vision.v1.Product.product_category], and
  // the `labels` column refers to
  // [product_labels][google.cloud.vision.v1.Product.product_labels].
  //
  // The `image-id` column is optional but must be unique if provided. If it is
  // empty, the system will automatically assign a unique id to the image.
  //
  // The `product-display-name` column is optional. If it is empty, the system
  // sets the [display_name][google.cloud.vision.v1.Product.display_name] field
  // for the product to a space (" "). You can update the `display_name` later
  // by using the API.
  //
  // If a `Product` with the specified `product-id` already exists, then the
  // system ignores the `product-display-name`, `product-category`, and `labels`
  // columns.
  //
  // The `labels` column (optional) is a line containing a list of
  // comma-separated key-value pairs, in the following format:
  //
  //     "key_1=value_1,key_2=value_2,...,key_n=value_n"
  //
  // The `bounding-poly` column (optional) identifies one region of
  // interest from the image in the same manner as `CreateReferenceImage`. If
  // you do not specify the `bounding-poly` column, then the system will try to
  // detect regions of interest automatically.
  //
  // At most one `bounding-poly` column is allowed per line. If the image
  // contains multiple regions of interest, add a line to the CSV file that
  // includes the same product information, and the `bounding-poly` values for
  // each region of interest.
  //
  // The `bounding-poly` column must contain an even number of comma-separated
  // numbers, in the format "p1_x,p1_y,p2_x,p2_y,...,pn_x,pn_y". Use
  // non-negative integers for absolute bounding polygons, and float values
  // in [0, 1] for normalized bounding polygons.
  //
  // The system will resize the image if the image resolution is too
  // large to process (larger than 20MP).
  string csv_file_uri = 1;
}

// The input content for the `ImportProductSets` method.
message ImportProductSetsInputConfig {
  // The source of the input.
  oneof source {
    // The Google Cloud Storage location for a csv file which preserves a list
    // of ImportProductSetRequests in each line.
    ImportProductSetsGcsSource gcs_source = 1;
  }
}

// Request message for the `ImportProductSets` method.
message ImportProductSetsRequest {
  // Required. The project in which the ProductSets should be imported.
  //
  // Format is `projects/PROJECT_ID/locations/LOC_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The input content for the list of requests.
  ImportProductSetsInputConfig input_config = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Response message for the `ImportProductSets` method.
//
// This message is returned by the
// [google.longrunning.Operations.GetOperation][google.longrunning.Operations.GetOperation]
// method in the returned
// [google.longrunning.Operation.response][google.longrunning.Operation.response]
// field.
message ImportProductSetsResponse {
  // The list of reference_images that are imported successfully.
  repeated ReferenceImage reference_images = 1;

  // The rpc status for each ImportProductSet request, including both successes
  // and errors.
  //
  // The number of statuses here matches the number of lines in the csv file,
  // and statuses[i] stores the success or failure status of processing the i-th
  // line of the csv, starting from line 0.
  repeated google.rpc.Status statuses = 2;
}

// Metadata for the batch operations such as the current state.
//
// This is included in the `metadata` field of the `Operation` returned by the
// `GetOperation` call of the `google::longrunning::Operations` service.
message BatchOperationMetadata {
  // Enumerates the possible states that the batch request can be in.
  enum State {
    // Invalid.
    STATE_UNSPECIFIED = 0;

    // Request is actively being processed.
    PROCESSING = 1;

    // The request is done and at least one item has been successfully
    // processed.
    SUCCESSFUL = 2;

    // The request is done and no item has been successfully processed.
    FAILED = 3;

    // The request is done after the longrunning.Operations.CancelOperation has
    // been called by the user.  Any records that were processed before the
    // cancel command are output as specified in the request.
    CANCELLED = 4;
  }

  // The current state of the batch operation.
  State state = 1;

  // The time when the batch request was submitted to the server.
  google.protobuf.Timestamp submit_time = 2;

  // The time when the batch request is finished and
  // [google.longrunning.Operation.done][google.longrunning.Operation.done] is
  // set to true.
  google.protobuf.Timestamp end_time = 3;
}

// Config to control which ProductSet contains the Products to be deleted.
message ProductSetPurgeConfig {
  // The ProductSet that contains the Products to delete. If a Product is a
  // member of product_set_id in addition to other ProductSets, the Product will
  // still be deleted.
  string product_set_id = 1;
}

// Request message for the `PurgeProducts` method.
message PurgeProductsRequest {
  // The Products to delete.
  oneof target {
    // Specify which ProductSet contains the Products to be deleted.
    ProductSetPurgeConfig product_set_purge_config = 2;

    // If delete_orphan_products is true, all Products that are not in any
    // ProductSet will be deleted.
    bool delete_orphan_products = 3;
  }

  // Required. The project and location in which the Products should be deleted.
  //
  // Format is `projects/PROJECT_ID/locations/LOC_ID`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // The default value is false. Override this value to true to actually perform
  // the purge.
  bool force = 4;
}
