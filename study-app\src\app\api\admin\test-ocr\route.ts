import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { processOCR } from '@/lib/upload';
import db from '@/lib/database';

export async function POST(request: NextRequest) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { imageId } = await request.json();

    if (!imageId) {
      return NextResponse.json({ error: 'Image ID is required' }, { status: 400 });
    }

    // Process OCR with new mock system (force regenerate)
    const text = await processOCR(imageId, true);
    
    return NextResponse.json({ 
      success: true, 
      text: text,
      imageId: imageId,
      message: 'OCR data cleared and regenerated with new mock system'
    });
  } catch (error) {
    console.error('Test OCR processing error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'OCR processing failed' 
    }, { status: 500 });
  }
}
