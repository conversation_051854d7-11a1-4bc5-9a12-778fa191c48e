{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Books', href: '/admin/books', icon: '📖' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/CopyTextButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface CopyTextButtonProps {\n  text: string;\n}\n\nexport default function CopyTextButton({ text }: CopyTextButtonProps) {\n  const [copied, setCopied] = useState(false);\n\n  const handleCopy = async () => {\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (error) {\n      console.error('Failed to copy text:', error);\n    }\n  };\n\n  return (\n    <button\n      onClick={handleCopy}\n      className=\"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n    >\n      {copied ? (\n        <>\n          <svg className=\"w-4 h-4 mr-1 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n          </svg>\n          Copied!\n        </>\n      ) : (\n        <>\n          <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n          </svg>\n          Copy Text\n        </>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQe,SAAS,eAAe,EAAE,IAAI,EAAuB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;kBAET,uBACC;;8BACE,8OAAC;oBAAI,WAAU;oBAA8B,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACrF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBACjE;;yCAIR;;8BACE,8OAAC;oBAAI,WAAU;oBAAe,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACtE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBACjE;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/OCRTextProcessor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { processOCRText, getProcessingPreview, TextProcessingOptions, DEFAULT_PROCESSING_OPTIONS } from '@/lib/text-processor';\n\ninterface OCRTextProcessorProps {\n  originalText: string;\n  imageId: number;\n  onTextUpdate?: (newText: string) => void;\n}\n\nexport default function OCRTextProcessor({ originalText, imageId, onTextUpdate }: OCRTextProcessorProps) {\n  const [showProcessor, setShowProcessor] = useState(false);\n  const [processingOptions, setProcessingOptions] = useState<TextProcessingOptions>(DEFAULT_PROCESSING_OPTIONS);\n  const [processedText, setProcessedText] = useState(originalText);\n  const [isEditing, setIsEditing] = useState(false);\n  const [editedText, setEditedText] = useState(originalText);\n\n  const handleProcessText = () => {\n    const processed = processOCRText(originalText, processingOptions);\n    setProcessedText(processed);\n    setEditedText(processed);\n    if (onTextUpdate) {\n      onTextUpdate(processed);\n    }\n  };\n\n  const handleSaveEdit = async () => {\n    try {\n      const response = await fetch(`/api/admin/images/${imageId}/ocr`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ content: editedText }),\n      });\n\n      if (response.ok) {\n        setProcessedText(editedText);\n        setIsEditing(false);\n        if (onTextUpdate) {\n          onTextUpdate(editedText);\n        }\n      } else {\n        alert('Failed to save text changes');\n      }\n    } catch (error) {\n      console.error('Error saving text:', error);\n      alert('Failed to save text changes');\n    }\n  };\n\n  const preview = getProcessingPreview(originalText, processingOptions);\n\n  return (\n    <div className=\"border rounded-lg p-4 bg-gray-50\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold\">Text Processing & Editing</h3>\n        <button\n          onClick={() => setShowProcessor(!showProcessor)}\n          className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700\"\n        >\n          {showProcessor ? 'Hide' : 'Show'} Options\n        </button>\n      </div>\n\n      {showProcessor && (\n        <div className=\"space-y-4\">\n          {/* Processing Options */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">Processing Options</h4>\n              \n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={processingOptions.fixLineBreaks}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, fixLineBreaks: e.target.checked }))}\n                />\n                <span className=\"text-sm\">Fix line breaks & word continuity</span>\n              </label>\n\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={processingOptions.mergeParagraphs}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, mergeParagraphs: e.target.checked }))}\n                />\n                <span className=\"text-sm\">Merge broken paragraphs</span>\n              </label>\n\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={processingOptions.removeExtraSpaces}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, removeExtraSpaces: e.target.checked }))}\n                />\n                <span className=\"text-sm\">Remove extra spaces</span>\n              </label>\n\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={processingOptions.fixCapitalization}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, fixCapitalization: e.target.checked }))}\n                />\n                <span className=\"text-sm\">Fix capitalization</span>\n              </label>\n\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={processingOptions.preserveFormatting}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, preserveFormatting: e.target.checked }))}\n                />\n                <span className=\"text-sm\">Preserve original formatting</span>\n              </label>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h4 className=\"font-medium\">Processing Stats</h4>\n              <div className=\"text-sm text-gray-600 space-y-1\">\n                <div>Original: {preview.changes.originalWords} words, {preview.changes.originalLines} lines</div>\n                <div>Processed: {preview.changes.processedWords} words, {preview.changes.processedLines} lines</div>\n                <div>Characters: {preview.changes.originalLength} → {preview.changes.processedLength}</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={handleProcessText}\n              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n            >\n              Apply Processing\n            </button>\n            \n            <button\n              onClick={() => setIsEditing(!isEditing)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              {isEditing ? 'Cancel Edit' : 'Manual Edit'}\n            </button>\n\n            <button\n              onClick={() => {\n                setProcessingOptions(DEFAULT_PROCESSING_OPTIONS);\n                setProcessedText(originalText);\n                setEditedText(originalText);\n              }}\n              className=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\"\n            >\n              Reset\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Text Display/Edit Area */}\n      <div className=\"mt-4\">\n        {isEditing ? (\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              <h4 className=\"font-medium\">Edit Text</h4>\n              <div className=\"space-x-2\">\n                <button\n                  onClick={handleSaveEdit}\n                  className=\"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700\"\n                >\n                  Save Changes\n                </button>\n                <button\n                  onClick={() => {\n                    setEditedText(processedText);\n                    setIsEditing(false);\n                  }}\n                  className=\"px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700\"\n                >\n                  Cancel\n                </button>\n              </div>\n            </div>\n            <textarea\n              value={editedText}\n              onChange={(e) => setEditedText(e.target.value)}\n              className=\"w-full h-64 p-3 border rounded font-mono text-sm\"\n              placeholder=\"Edit the OCR text here...\"\n            />\n          </div>\n        ) : (\n          <div className=\"space-y-2\">\n            <h4 className=\"font-medium\">Processed Text</h4>\n            <div className=\"bg-white p-3 border rounded max-h-64 overflow-y-auto\">\n              <pre className=\"whitespace-pre-wrap text-sm font-mono\">{processedText}</pre>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,iBAAiB,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAyB;IACrG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,+HAAA,CAAA,6BAA0B;IAC5G,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB;QACxB,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;QAC/C,iBAAiB;QACjB,cAAc;QACd,IAAI,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ,IAAI,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAW;YAC7C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;gBACjB,aAAa;gBACb,IAAI,cAAc;oBAChB,aAAa;gBACf;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc;IAEnD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC;wBACC,SAAS,IAAM,iBAAiB,CAAC;wBACjC,WAAU;;4BAET,gBAAgB,SAAS;4BAAO;;;;;;;;;;;;;YAIpC,+BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,aAAa;gDACxC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,eAAe,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;;;;;;0DAE7F,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAG5B,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,eAAe;gDAC1C,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;;;;;;0DAE/F,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAG5B,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,iBAAiB;gDAC5C,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;;;;;;0DAEjG,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAG5B,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,iBAAiB;gDAC5C,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;;;;;;0DAEjG,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAG5B,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS,kBAAkB,kBAAkB;gDAC7C,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,oBAAoB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;;;;;;0DAElG,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAI;oDAAW,QAAQ,OAAO,CAAC,aAAa;oDAAC;oDAAS,QAAQ,OAAO,CAAC,aAAa;oDAAC;;;;;;;0DACrF,8OAAC;;oDAAI;oDAAY,QAAQ,OAAO,CAAC,cAAc;oDAAC;oDAAS,QAAQ,OAAO,CAAC,cAAc;oDAAC;;;;;;;0DACxF,8OAAC;;oDAAI;oDAAa,QAAQ,OAAO,CAAC,cAAc;oDAAC;oDAAI,QAAQ,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;kCAM1F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS,IAAM,aAAa,CAAC;gCAC7B,WAAU;0CAET,YAAY,gBAAgB;;;;;;0CAG/B,8OAAC;gCACC,SAAS;oCACP,qBAAqB,+HAAA,CAAA,6BAA0B;oCAC/C,iBAAiB;oCACjB,cAAc;gCAChB;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP,cAAc;gDACd,aAAa;4CACf;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAKL,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;4BACV,aAAY;;;;;;;;;;;yCAIhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAc;;;;;;sCAC5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtE", "debugId": null}}]}