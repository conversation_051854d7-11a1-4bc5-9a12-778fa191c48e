import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import db from '@/lib/database';
import { reorderImages } from '@/lib/pageNumbering';

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const user = await requireAdmin();
  
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { id } = await params;
    const bookId = parseInt(id);
    
    if (isNaN(bookId)) {
      return NextResponse.json({ error: 'Invalid book ID' }, { status: 400 });
    }

    // Verify book exists
    const book = BookModel.getById(bookId);
    if (!book) {
      return NextResponse.json({ error: 'Book not found' }, { status: 404 });
    }

    const { imageOrders } = await request.json();
    
    if (!Array.isArray(imageOrders)) {
      return NextResponse.json({ error: 'imageOrders must be an array' }, { status: 400 });
    }

    // Use the reorderImages function which handles page numbering
    const reorderData = imageOrders.map((item, index) => ({
      imageId: item.imageId,
      newPosition: index + 1
    }));

    reorderImages(bookId, reorderData);

    return NextResponse.json({
      success: true,
      message: 'Images reordered successfully'
    });

  } catch (error) {
    console.error('Reorder images error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to reorder images'
    }, { status: 500 });
  }
}
