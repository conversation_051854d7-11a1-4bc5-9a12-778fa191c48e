# Google Vision API Setup Guide

## 🎯 Why Google Vision API?

- **1,000 FREE OCR requests per month**
- **High accuracy** text extraction from images
- **Handwriting recognition** support
- **Multi-language** support
- **No configuration headaches** like Tesseract.js

## 📋 Setup Steps

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the **Vision API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Vision API"
   - Click "Enable"

### 2. Create Service Account

1. Go to "IAM & Admin" > "Service Accounts"
2. Click "Create Service Account"
3. Name: `study-app-vision`
4. Role: `Cloud Vision API Service Agent`
5. Click "Create and Continue"
6. Click "Done"

### 3. Generate Service Account Key

1. Click on your service account
2. Go to "Keys" tab
3. Click "Add Key" > "Create new key"
4. Choose "JSON" format
5. Download the key file

### 4. Configure Authentication

**Option A: Environment Variable (Recommended)**
```bash
# Set the path to your service account key file
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

**Option B: Place key in project**
1. Create `study-app/credentials/` folder
2. Copy your key file as `google-vision-key.json`
3. Add to `.env.local`:
```
GOOGLE_APPLICATION_CREDENTIALS=./credentials/google-vision-key.json
```

### 5. Update .gitignore

Add to your `.gitignore`:
```
# Google Cloud credentials
credentials/
*.json
!package*.json
.env.local
```

## 🧪 Test the Setup

1. Restart your development server
2. Upload an image with text
3. Click "Clear OCR Data" to test new system
4. Check terminal logs for "Using Google Vision API"

## 💰 Pricing

- **Free**: 1,000 requests/month
- **Paid**: $1.50 per 1,000 requests
- **Your cost**: Probably $0-5/month for typical usage

## 🔧 Troubleshooting

### "Google Vision API not configured"
- Check your service account key path
- Verify the Vision API is enabled
- Restart your development server

### "Authentication error"
- Verify service account has correct permissions
- Check the key file is valid JSON
- Ensure environment variable is set correctly

### "Quota exceeded"
- You've used your free 1,000 requests
- Either wait for next month or enable billing

## 📚 Alternative: Keep Mock OCR

If you don't want to set up Google Vision API right now:
- The app will automatically fall back to mock OCR
- You can set it up later when you're ready
- Mock OCR is clearly labeled so you know it's not real

## 🚀 Benefits After Setup

✅ **Real text extraction** from your textbook images  
✅ **High accuracy** OCR results  
✅ **Handwriting support** for notes  
✅ **Multi-language** text recognition  
✅ **No more Tesseract.js headaches**  

The setup takes about 10 minutes and gives you professional-grade OCR!
