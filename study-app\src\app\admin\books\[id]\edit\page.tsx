import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel, ClassModel, SubjectModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import EditBookForm from '@/components/EditBookForm';
import Link from 'next/link';

interface BookEditPageProps {
  params: Promise<{ id: string }>;
}

export default async function BookEditPage({ params }: BookEditPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const { id } = await params;
  const bookId = parseInt(id);
  
  if (isNaN(bookId)) {
    redirect('/admin/books');
  }

  const book = BookModel.getById(bookId);
  
  if (!book) {
    redirect('/admin/books');
  }

  const classes = ClassModel.getAll();
  const subjects = SubjectModel.getAll();

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <Link href="/admin/books" className="hover:text-gray-700">Books</Link>
            <span>›</span>
            <Link href={`/admin/books/${book.id}`} className="hover:text-gray-700">{book.title}</Link>
            <span>›</span>
            <span>Edit</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Book</h1>
          <p className="mt-1 text-sm text-gray-600">
            Update the details for "{book.title}"
          </p>
        </div>

        {/* Edit Form */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <EditBookForm book={book} classes={classes} subjects={subjects} />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
