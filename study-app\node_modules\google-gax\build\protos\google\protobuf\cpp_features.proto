// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto2";

package pb;

import "google/protobuf/descriptor.proto";

extend google.protobuf.FeatureSet {
  optional CppFeatures cpp = 1000;
}

message CppFeatures {
  // Whether or not to treat an enum field as closed.  This option is only
  // applicable to enum fields, and will be removed in the future.  It is
  // consistent with the legacy behavior of using proto3 enum types for proto2
  // fields.
  optional bool legacy_closed_enum = 1 [
    retention = RETENTION_RUNTIME,
    targets = TARGET_TYPE_FIELD,
    targets = TARGET_TYPE_FILE,
    feature_support = {
      edition_introduced: EDITION_2023,
      edition_deprecated: EDITION_2023,
      deprecation_warning: "The legacy closed enum behavior in C++ is "
                           "deprecated and is scheduled to be removed in "
                           "edition 2025.  See http://protobuf.dev/programming-guides/enum/#cpp for "
                           "more information",
    },
    edition_defaults = { edition: EDITION_LEGACY, value: "true" },
    edition_defaults = { edition: EDITION_PROTO3, value: "false" }
  ];

  enum StringType {
    STRING_TYPE_UNKNOWN = 0;
    VIEW = 1;
    CORD = 2;
    STRING = 3;
  }

  optional StringType string_type = 2 [
    retention = RETENTION_RUNTIME,
    targets = TARGET_TYPE_FIELD,
    targets = TARGET_TYPE_FILE,
    feature_support = {
      edition_introduced: EDITION_2023,
    },
    edition_defaults = { edition: EDITION_LEGACY, value: "STRING" },
    edition_defaults = { edition: EDITION_2024, value: "VIEW" }
  ];

  optional bool enum_name_uses_string_view = 3 [
    retention = RETENTION_SOURCE,
    targets = TARGET_TYPE_ENUM,
    targets = TARGET_TYPE_FILE,
    feature_support = {
      edition_introduced: EDITION_2024,
    },
    edition_defaults = { edition: EDITION_LEGACY, value: "false" },
    edition_defaults = { edition: EDITION_2024, value: "true" }
  ];
}
