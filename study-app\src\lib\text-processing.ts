/**
 * Text post-processing utilities for OCR output
 * Cleans up and formats extracted text for better readability
 */

export interface TextProcessingOptions {
  fixLineBreaks: boolean;
  mergeParagraphs: boolean;
  removeExtraSpaces: boolean;
  fixCapitalization: boolean;
  preserveFormatting: boolean;
}

export const DEFAULT_PROCESSING_OPTIONS: TextProcessingOptions = {
  fixLineBreaks: true,
  mergeParagraphs: true,
  removeExtraSpaces: true,
  fixCapitalization: true,
  preserveFormatting: false
};

/**
 * Main text processing function
 */
export function processOCRText(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS): string {
  if (!rawText || rawText.trim().length === 0) {
    return rawText;
  }

  let processedText = rawText;

  // Step 1: Remove OCR artifacts and random characters
  processedText = removeOCRArtifacts(processedText);

  // Step 2: Basic cleanup
  if (options.removeExtraSpaces) {
    processedText = removeExtraSpaces(processedText);
  }

  // Step 3: Fix question numbering and formatting
  processedText = fixQuestionNumbering(processedText);

  // Step 4: Fix line breaks and continuity
  if (options.fixLineBreaks) {
    processedText = fixLineBreaks(processedText);
  }

  // Step 5: Merge broken paragraphs
  if (options.mergeParagraphs) {
    processedText = mergeBrokenParagraphs(processedText);
  }

  // Step 6: Fix capitalization issues
  if (options.fixCapitalization) {
    processedText = fixCapitalization(processedText);
  }

  // Step 7: Final cleanup
  processedText = finalCleanup(processedText);

  return processedText;
}

/**
 * Remove OCR artifacts and random characters
 */
function removeOCRArtifacts(text: string): string {
  return text
    // Remove common OCR artifacts and random characters
    .replace(/^[ル\u3000-\u303F\u3040-\u309F\u30A0-\u30FF\uFF00-\uFFEF]+/gm, '') // Japanese characters at start of lines
    .replace(/[^\x00-\x7F\u00A0-\u024F\u1E00-\u1EFF\u2000-\u206F\u2070-\u209F\u20A0-\u20CF\u2100-\u214F\u2190-\u21FF\u2200-\u22FF]/g, '') // Remove non-Latin characters except common symbols
    // Remove isolated single characters that are likely OCR errors
    .replace(/^\s*[^\w\s]\s*$/gm, '')
    // Remove lines with only symbols or numbers
    .replace(/^\s*[^\w\s]{1,3}\s*$/gm, '')
    // Clean up any resulting empty lines
    .replace(/\n\s*\n\s*\n/g, '\n\n');
}

/**
 * Fix question numbering and formatting
 */
function fixQuestionNumbering(text: string): string {
  return text
    // Fix separated question numbers/letters (e.g., "a." on one line, question on next)
    .replace(/^([a-z]\.)\s*\n\s*([A-Z])/gm, '$1 $2')
    .replace(/^([A-Z]\.)\s*\n\s*([A-Z])/gm, '$1 $2')
    .replace(/^(\d+\.)\s*\n\s*([A-Z])/gm, '$1 $2')
    // Fix numbered questions with letters (e.g., "1. a." becomes "1a.")
    .replace(/(\d+)\.\s+([a-z])\./g, '$1$2.')
    // Fix spacing around question numbers
    .replace(/^([a-z]\.|[A-Z]\.|\d+\.)\s+/gm, '$1 ')
    // Fix questions that start with lowercase after number
    .replace(/^([a-z]\.|[A-Z]\.|\d+\.)\s+([a-z])/gm, (match, num, letter) => num + ' ' + letter.toUpperCase())
    // Join question parts that were split
    .replace(/^([a-z]\.|[A-Z]\.|\d+\.)\s*\n\s*([a-z])/gm, '$1 ' + '$2'.toUpperCase());
}

/**
 * Remove extra spaces and normalize whitespace
 */
function removeExtraSpaces(text: string): string {
  return text
    // Replace multiple spaces with single space
    .replace(/[ \t]+/g, ' ')
    // Remove spaces at beginning and end of lines
    .replace(/^[ \t]+|[ \t]+$/gm, '')
    // Remove multiple consecutive newlines (keep max 2)
    .replace(/\n{3,}/g, '\n\n');
}

/**
 * Fix line breaks and word continuity
 */
function fixLineBreaks(text: string): string {
  return text
    // Fix hyphenated words split across lines
    .replace(/(\w+)-\s*\n\s*(\w+)/g, '$1$2')
    // Join lines that end with lowercase and start with lowercase (likely continuation)
    .replace(/([a-z,])\s*\n\s*([a-z])/g, '$1 $2')
    // Join lines where previous line doesn't end with punctuation and next starts with lowercase
    .replace(/([^.!?:;\n])\s*\n\s*([a-z])/g, '$1 $2')
    // Handle educational content - join lines that are clearly continuations
    .replace(/([a-z])\s*\n\s*([a-z][^.!?]*[a-z])\s*\n/g, '$1 $2 ')
    // Fix broken sentences in educational content
    .replace(/([a-z])\s*\n\s*([a-z][^A-Z]*[.!?])/g, '$1 $2')
    // Preserve intentional line breaks (after punctuation)
    .replace(/([.!?:;])\s*\n\s*([A-Z])/g, '$1\n\n$2')
    // Preserve question formatting
    .replace(/([a-z]\.|[A-Z]\.|\d+\.)\s*\n\s*/g, '$1 ');
}

/**
 * Merge broken paragraphs back together
 */
function mergeBrokenParagraphs(text: string): string {
  const lines = text.split('\n');
  const mergedLines: string[] = [];
  let currentParagraph = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line === '') {
      // Empty line - end current paragraph
      if (currentParagraph.trim()) {
        mergedLines.push(currentParagraph.trim());
        currentParagraph = '';
      }
      mergedLines.push('');
    } else if (isNewParagraph(line, lines[i - 1])) {
      // Start of new paragraph
      if (currentParagraph.trim()) {
        mergedLines.push(currentParagraph.trim());
      }
      currentParagraph = line;
    } else {
      // Continue current paragraph
      if (currentParagraph) {
        currentParagraph += ' ' + line;
      } else {
        currentParagraph = line;
      }
    }
  }

  // Add final paragraph
  if (currentParagraph.trim()) {
    mergedLines.push(currentParagraph.trim());
  }

  return mergedLines.join('\n');
}

/**
 * Determine if a line starts a new paragraph
 */
function isNewParagraph(currentLine: string, previousLine?: string): boolean {
  if (!currentLine || !previousLine) return true;

  const current = currentLine.trim();
  const previous = previousLine.trim();

  // New paragraph indicators
  const newParagraphPatterns = [
    /^\d+\./, // Numbered list
    /^[a-z]\./, // Lettered list (a., b., c.)
    /^[A-Z]\./, // Capital lettered list (A., B., C.)
    /^[A-Z][a-z]*:/, // Section headers (Word:)
    /^Chapter \d+/i, // Chapter headers
    /^Section \d+/i, // Section headers
    /^[A-Z]{2,}/, // ALL CAPS headers
    /^ABOUT|^WORDS|^COMPREHENSION/i, // Common textbook sections
    /^\*/, // Bullet points
    /^-/, // Dash points
    /^[ivx]+\./, // Roman numerals
  ];

  // Check if current line matches new paragraph patterns
  for (const pattern of newParagraphPatterns) {
    if (pattern.test(current)) {
      return true;
    }
  }

  // New paragraph if previous line ended with punctuation and current starts with capital
  if (/[.!?]$/.test(previous) && /^[A-Z]/.test(current)) {
    return true;
  }

  // Educational content: new paragraph for questions
  if (/^[a-z]\.\s+[A-Z]/.test(current) || /^[A-Z]\.\s+[A-Z]/.test(current)) {
    return true;
  }

  return false;
}

/**
 * Fix common capitalization issues
 */
function fixCapitalization(text: string): string {
  return text
    // Capitalize first letter of sentences
    .replace(/(^|[.!?]\s+)([a-z])/g, (match, prefix, letter) => prefix + letter.toUpperCase())
    // Fix common OCR mistakes with 'I'
    .replace(/\bi\b/g, 'I')
    // Fix 'i' at start of sentences
    .replace(/(^|[.!?]\s+)i\b/g, '$1I');
}

/**
 * Final cleanup and formatting
 */
function finalCleanup(text: string): string {
  return text
    // Remove extra whitespace
    .trim()
    // Ensure proper spacing after punctuation
    .replace(/([.!?])([A-Z])/g, '$1 $2')
    // Fix spacing around common punctuation
    .replace(/\s+([,.!?;:])/g, '$1')
    .replace(/([.!?;:])\s*([A-Z])/g, '$1 $2')
    // Remove trailing spaces from lines
    .replace(/[ \t]+$/gm, '')
    // Normalize line endings
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n');
}

/**
 * Get a preview of text processing changes
 */
export function getProcessingPreview(rawText: string, options: TextProcessingOptions = DEFAULT_PROCESSING_OPTIONS) {
  const processed = processOCRText(rawText, options);
  
  return {
    original: rawText,
    processed: processed,
    changes: {
      originalLength: rawText.length,
      processedLength: processed.length,
      originalLines: rawText.split('\n').length,
      processedLines: processed.split('\n').length,
      originalWords: rawText.split(/\s+/).length,
      processedWords: processed.split(/\s+/).length
    }
  };
}

/**
 * Detect the type of content for specialized processing
 */
export function detectContentType(text: string): 'textbook' | 'math' | 'code' | 'table' | 'general' {
  const mathPatterns = [
    /\d+\s*[+\-*/=]\s*\d+/,
    /[xy]\s*[=+\-]/,
    /\b(equation|formula|solve|calculate)\b/i
  ];

  const codePatterns = [
    /function\s+\w+\s*\(/,
    /\bif\s*\(/,
    /\bfor\s*\(/,
    /[{}();]/
  ];

  const tablePatterns = [
    /\|\s*\w+\s*\|/,
    /\t\w+\t/,
    /^\s*\w+\s+\w+\s+\w+\s*$/m
  ];

  if (mathPatterns.some(pattern => pattern.test(text))) {
    return 'math';
  }
  
  if (codePatterns.some(pattern => pattern.test(text))) {
    return 'code';
  }
  
  if (tablePatterns.some(pattern => pattern.test(text))) {
    return 'table';
  }

  if (/chapter|section|exercise|problem/i.test(text)) {
    return 'textbook';
  }

  return 'general';
}
