{"version": 3, "sources": [], "sections": [{"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Books', href: '/admin/books', icon: '📖' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;AAEA;;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,8OAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/admin/test-processing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { processOCRText, getProcessingPreview, DEFAULT_PROCESSING_OPTIONS } from '@/lib/text-processor';\nimport AdminLayout from '@/components/AdminLayout';\n\nconst sampleProblematicText = `ル\nABOUT THE AUTHOR\n<PERSON><PERSON><PERSON> (1907-2002) was a Swedish writer. She is best known for her children's book\nseries. The Pippi Longstocking stories are about an unusual girl. <PERSON><PERSON><PERSON> is brave, funny and so\nincredibly strong that she can lift a horse up with one hand!\nWORDS TO KNOW\nangel a spiritual being that is believed to act as an attendant to god (<PERSON><PERSON><PERSON>'s mother died when\nshe was young)\nbraided plaited\ncannibal someone who eats other people!\ncourage bravery, the ability to do something that frightens you\nfreckles small patches of darker colour on skin (often made darker by the sun)\nremarkable someone or something worth noticing\nsidewalk a paved pathway at the side of a road\nstockings very long socks\nCOMPREHENSION\n1. Answer the following questions.\na. What was the name of <PERSON><PERSON><PERSON>'s house?\nb. What were <PERSON> and <PERSON><PERSON> thinking while standing by their gate?\nC.\nd.\nWhich three countries did <PERSON><PERSON><PERSON> say she has been to?\nWhat was unusual about how <PERSON><PERSON><PERSON> went on her morning walk?\ne.\nWhere were <PERSON><PERSON><PERSON>'s parents?\nMoney\nf.\nWhat did <PERSON><PERSON> say to <PERSON><PERSON><PERSON> about lying?\ng.\nWhat did <PERSON> realize while he was speaking to <PERSON><PERSON><PERSON>?\nh. What was the name of <PERSON>ppi's pet monkey?\ni. How do we know that the pet monkey was polite?\nThese questions are more difficult. Discuss them first.\nj. What details from the story show us that Pippi is a remarkable child?\nHow many lies do you think Pippi tells in this extract?\nk.`;\n\nexport default function TestProcessingPage() {\n  const [inputText, setInputText] = useState(sampleProblematicText);\n  const [processedText, setProcessedText] = useState('');\n  const [showPreview, setShowPreview] = useState(false);\n\n  const handleProcess = () => {\n    const processed = processOCRText(inputText, DEFAULT_PROCESSING_OPTIONS);\n    setProcessedText(processed);\n    setShowPreview(true);\n  };\n\n  const preview = showPreview ? getProcessingPreview(inputText, DEFAULT_PROCESSING_OPTIONS) : null;\n\n  return (\n    <AdminLayout>\n      <div className=\"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-6\">OCR Text Processing Test</h1>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Input Section */}\n            <div className=\"space-y-4\">\n              <h2 className=\"text-xl font-semibold\">Original OCR Text (with problems)</h2>\n              <textarea\n                value={inputText}\n                onChange={(e) => setInputText(e.target.value)}\n                className=\"w-full h-96 p-3 border rounded font-mono text-sm\"\n                placeholder=\"Paste problematic OCR text here...\"\n              />\n              <button\n                onClick={handleProcess}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                Process Text\n              </button>\n            </div>\n\n            {/* Output Section */}\n            <div className=\"space-y-4\">\n              <h2 className=\"text-xl font-semibold\">Processed Text (cleaned)</h2>\n              <div className=\"w-full h-96 p-3 border rounded bg-gray-50 overflow-y-auto\">\n                <pre className=\"whitespace-pre-wrap text-sm font-mono\">{processedText}</pre>\n              </div>\n              \n              {preview && (\n                <div className=\"bg-blue-50 p-4 rounded\">\n                  <h3 className=\"font-medium mb-2\">Processing Statistics</h3>\n                  <div className=\"text-sm space-y-1\">\n                    <div>Words: {preview.changes.originalWords} → {preview.changes.processedWords}</div>\n                    <div>Lines: {preview.changes.originalLines} → {preview.changes.processedLines}</div>\n                    <div>Characters: {preview.changes.originalLength} → {preview.changes.processedLength}</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Improvements Section */}\n          <div className=\"mt-8 bg-green-50 p-6 rounded-lg\">\n            <h3 className=\"text-lg font-semibold mb-4\">What the Enhanced Processing Fixes:</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <h4 className=\"font-medium text-green-800\">OCR Artifacts</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Removes random characters like \"ル\"</li>\n                  <li>Cleans up non-Latin characters</li>\n                  <li>Removes isolated symbols</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Question Formatting</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Joins separated question numbers (C. + question)</li>\n                  <li>Fixes question lettering (a., b., c.)</li>\n                  <li>Proper spacing around numbers</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Text Continuity</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Merges broken sentences</li>\n                  <li>Fixes paragraph structure</li>\n                  <li>Handles hyphenated words</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-medium text-green-800\">Educational Content</h4>\n                <ul className=\"list-disc list-inside text-green-700\">\n                  <li>Recognizes textbook sections</li>\n                  <li>Preserves question formatting</li>\n                  <li>Handles numbered lists properly</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAmC7B,CAAC;AAEY,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,+HAAA,CAAA,6BAA0B;QACtE,iBAAiB;QACjB,eAAe;IACjB;IAEA,MAAM,UAAU,cAAc,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,+HAAA,CAAA,6BAA0B,IAAI;IAE5F,qBACE,8OAAC,iIAAA,CAAA,UAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;;;;;;oCAGzD,yBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAQ,QAAQ,OAAO,CAAC,aAAa;4DAAC;4DAAI,QAAQ,OAAO,CAAC,cAAc;;;;;;;kEAC7E,8OAAC;;4DAAI;4DAAQ,QAAQ,OAAO,CAAC,aAAa;4DAAC;4DAAI,QAAQ,OAAO,CAAC,cAAc;;;;;;;kEAC7E,8OAAC;;4DAAI;4DAAa,QAAQ,OAAO,CAAC,cAAc;4DAAC;4DAAI,QAAQ,OAAO,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ9F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}]}