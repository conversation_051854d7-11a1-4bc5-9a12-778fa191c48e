'use client';

import { useState } from 'react';
import { processOCRText, getProcessingPreview, DEFAULT_PROCESSING_OPTIONS } from '@/lib/text-processing';
import AdminLayout from '@/components/AdminLayout';

const sampleProblematicText = `ル
ABOUT THE AUTHOR
<PERSON><PERSON><PERSON> (1907-2002) was a Swedish writer. She is best known for her children's book
series. The Pippi Longstocking stories are about an unusual girl. <PERSON><PERSON><PERSON> is brave, funny and so
incredibly strong that she can lift a horse up with one hand!
WORDS TO KNOW
angel a spiritual being that is believed to act as an attendant to god (<PERSON><PERSON><PERSON>'s mother died when
she was young)
braided plaited
cannibal someone who eats other people!
courage bravery, the ability to do something that frightens you
freckles small patches of darker colour on skin (often made darker by the sun)
remarkable someone or something worth noticing
sidewalk a paved pathway at the side of a road
stockings very long socks
COMPREHENSION
1. Answer the following questions.
a. What was the name of <PERSON><PERSON><PERSON>'s house?
b. What were <PERSON> and <PERSON><PERSON> thinking while standing by their gate?
C.
d.
Which three countries did <PERSON><PERSON><PERSON> say she has been to?
What was unusual about how <PERSON><PERSON><PERSON> went on her morning walk?
e.
Where were <PERSON><PERSON><PERSON>'s parents?
Money
f.
What did <PERSON><PERSON> say to <PERSON><PERSON><PERSON> about lying?
g.
What did <PERSON> realize while he was speaking to <PERSON><PERSON><PERSON>?
h. What was the name of <PERSON>ppi's pet monkey?
i. How do we know that the pet monkey was polite?
These questions are more difficult. Discuss them first.
j. What details from the story show us that Pippi is a remarkable child?
How many lies do you think Pippi tells in this extract?
k.`;

export default function TestProcessingPage() {
  const [inputText, setInputText] = useState(sampleProblematicText);
  const [processedText, setProcessedText] = useState('');
  const [showPreview, setShowPreview] = useState(false);

  const handleProcess = () => {
    const processed = processOCRText(inputText, DEFAULT_PROCESSING_OPTIONS);
    setProcessedText(processed);
    setShowPreview(true);
  };

  const preview = showPreview ? getProcessingPreview(inputText, DEFAULT_PROCESSING_OPTIONS) : null;

  return (
    <AdminLayout>
      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">OCR Text Processing Test</h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Original OCR Text (with problems)</h2>
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                className="w-full h-96 p-3 border rounded font-mono text-sm text-gray-900 bg-white"
                placeholder="Paste problematic OCR text here..."
              />
              <button
                onClick={handleProcess}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Process Text
              </button>
            </div>

            {/* Output Section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Processed Text (cleaned)</h2>
              <div className="w-full h-96 p-3 border rounded bg-gray-50 overflow-y-auto">
                <pre className="whitespace-pre-wrap text-sm font-mono text-gray-900">{processedText}</pre>
              </div>
              
              {preview && (
                <div className="bg-blue-50 p-4 rounded">
                  <h3 className="font-medium mb-2">Processing Statistics</h3>
                  <div className="text-sm space-y-1 text-gray-900">
                    <div>Words: {preview.changes.originalWords} → {preview.changes.processedWords}</div>
                    <div>Lines: {preview.changes.originalLines} → {preview.changes.processedLines}</div>
                    <div>Characters: {preview.changes.originalLength} → {preview.changes.processedLength}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Improvements Section */}
          <div className="mt-8 bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold mb-4">What the Enhanced Processing Fixes:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-green-800">OCR Artifacts</h4>
                <ul className="list-disc list-inside text-green-700">
                  <li>Removes random characters like "ル"</li>
                  <li>Cleans up non-Latin characters</li>
                  <li>Removes isolated symbols</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-green-800">Question Formatting</h4>
                <ul className="list-disc list-inside text-green-700">
                  <li>Joins separated question numbers (C. + question)</li>
                  <li>Fixes question lettering (a., b., c.)</li>
                  <li>Proper spacing around numbers</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-green-800">Text Continuity</h4>
                <ul className="list-disc list-inside text-green-700">
                  <li>Merges broken sentences</li>
                  <li>Fixes paragraph structure</li>
                  <li>Handles hyphenated words</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-green-800">Educational Content</h4>
                <ul className="list-disc list-inside text-green-700">
                  <li>Recognizes textbook sections</li>
                  <li>Preserves question formatting</li>
                  <li>Handles numbered lists properly</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
