'use client';

import { useState, useRef, useEffect } from 'react';
import { Book } from '@/lib/models';
import BookPageTypeModal from './BookPageTypeModal';
import { getPageTypeDisplayName, getPageTypeColor } from '@/lib/pageNumbering';

interface BookImage {
  id: number;
  original_name: string;
  page_number: number;
  page_type: string;
  file_path: string;
  has_ocr?: boolean;
  ocr_processed?: boolean;
}

interface BookImageManagerProps {
  book: Book;
  initialImages?: BookImage[];
}

export default function BookImageManager({ book, initialImages = [] }: BookImageManagerProps) {
  const [images, setImages] = useState<BookImage[]>(initialImages);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [selectedImages, setSelectedImages] = useState<Set<number>>(new Set());
  const [showPageTypeModal, setShowPageTypeModal] = useState(false);
  const [showReorderModal, setShowReorderModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image preview modal state
  const [previewModal, setPreviewModal] = useState({
    isOpen: false,
    imageUrl: '',
    imageName: ''
  });

  useEffect(() => {
    loadImages();
  }, [book.id]);

  const loadImages = async () => {
    try {
      const response = await fetch(`/api/admin/books/${book.id}/images`);
      if (response.ok) {
        const data = await response.json();
        setImages(data.images || []);
      }
    } catch (error) {
      console.error('Failed to load images:', error);
    }
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      
      // Add all files to FormData
      Array.from(files).forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch(`/api/admin/books/${book.id}/upload-images`, {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setSuccess(`Successfully uploaded ${result.uploaded_count} images`);
        if (result.errors && result.errors.length > 0) {
          setError(`Some files had issues: ${result.errors.join(', ')}`);
        }
        await loadImages(); // Reload images
      } else {
        setError(result.error || 'Upload failed');
      }
    } catch (error) {
      setError('Upload failed. Please try again.');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const openImagePreview = (imageId: number, imageName: string) => {
    setPreviewModal({
      isOpen: true,
      imageUrl: `/api/admin/images/${imageId}/preview`,
      imageName
    });
  };

  const closeImagePreview = () => {
    setPreviewModal({
      isOpen: false,
      imageUrl: '',
      imageName: ''
    });
  };

  const toggleImageSelection = (imageId: number) => {
    const newSelected = new Set(selectedImages);
    if (newSelected.has(imageId)) {
      newSelected.delete(imageId);
    } else {
      newSelected.add(imageId);
    }
    setSelectedImages(newSelected);
  };

  const selectAllImages = () => {
    setSelectedImages(new Set(images.map(img => img.id)));
  };

  const clearSelection = () => {
    setSelectedImages(new Set());
  };



  const handlePageTypeAssignments = async (updates: { imageId: number; pageType: string }[]) => {
    try {
      const response = await fetch(`/api/admin/books/${book.id}/images/page-types`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pageTypeUpdates: updates
        }),
      });

      if (response.ok) {
        setSuccess('Page types updated successfully');
        await loadImages(); // Reload images to show updated page types
        setSelectedImages(new Set()); // Clear selection
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update page types');
      }
    } catch (error) {
      setError('Failed to update page types');
    }
  };

  const moveImageUp = async (imageId: number) => {
    const currentIndex = images.findIndex(img => img.id === imageId);
    if (currentIndex <= 0) return;

    const newImages = [...images];
    [newImages[currentIndex], newImages[currentIndex - 1]] = [newImages[currentIndex - 1], newImages[currentIndex]];

    await reorderImages(newImages);
  };

  const moveImageDown = async (imageId: number) => {
    const currentIndex = images.findIndex(img => img.id === imageId);
    if (currentIndex >= images.length - 1) return;

    const newImages = [...images];
    [newImages[currentIndex], newImages[currentIndex + 1]] = [newImages[currentIndex + 1], newImages[currentIndex]];

    await reorderImages(newImages);
  };

  const reorderImages = async (newImageOrder: BookImage[]) => {
    try {
      const imageOrders = newImageOrder.map((image, index) => ({
        imageId: image.id,
        newPosition: index + 1
      }));

      const response = await fetch(`/api/admin/books/${book.id}/images/reorder`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageOrders }),
      });

      if (response.ok) {
        setSuccess('Images reordered successfully');
        await loadImages(); // Reload images to show new order
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to reorder images');
      }
    } catch (error) {
      setError('Failed to reorder images');
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Upload Images</h3>
        
        <div className="flex items-center justify-center w-full">
          <div className="w-full">
            <button
              onClick={triggerFileInput}
              disabled={uploading}
              className="w-full h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <div className="text-center">
                <div className="text-4xl text-gray-400 mb-2">📁</div>
                <div className="text-lg font-medium text-gray-900">
                  {uploading ? 'Uploading...' : 'Click to select images'}
                </div>
                <div className="text-sm text-gray-500">
                  Or drag and drop multiple image files here
                </div>
              </div>
            </button>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileSelect}
              className="hidden"
            />
          </div>
        </div>

        <div className="mt-4 text-sm text-gray-600">
          <p>• Supported formats: JPG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB per image</p>
          <p>• Images will be automatically numbered in upload order</p>
          <p>• You can reorder and assign page types after upload</p>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}
      </div>

      {/* Images Management Section */}
      {images.length > 0 && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Uploaded Images ({images.length})
            </h3>
            
            <div className="flex space-x-2">
              <button
                onClick={selectAllImages}
                className="text-sm text-indigo-600 hover:text-indigo-800"
              >
                Select All
              </button>
              <button
                onClick={clearSelection}
                className="text-sm text-gray-600 hover:text-gray-800"
              >
                Clear Selection
              </button>
              <button
                onClick={() => setShowPageTypeModal(true)}
                disabled={selectedImages.size === 0}
                className="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Assign Page Types ({selectedImages.size})
              </button>
              <button
                onClick={() => setShowReorderModal(true)}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              >
                Reorder Pages
              </button>
            </div>
          </div>

          {/* Images Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {images.map((image) => (
              <div
                key={image.id}
                className={`relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all ${
                  selectedImages.has(image.id)
                    ? 'border-indigo-500 ring-2 ring-indigo-200'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => toggleImageSelection(image.id)}
              >
                <div className="aspect-w-3 aspect-h-4">
                  <img
                    src={`/api/admin/images/${image.id}/preview`}
                    alt={image.original_name}
                    className="w-full h-32 object-cover"
                    onDoubleClick={(e) => {
                      e.stopPropagation();
                      openImagePreview(image.id, image.original_name);
                    }}
                  />
                </div>
                
                {/* Page Number Badge */}
                <div className="absolute top-1 left-1 bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded">
                  P{image.page_number}
                </div>

                {/* Selection Checkbox */}
                <div className="absolute top-1 right-1">
                  <input
                    type="checkbox"
                    checked={selectedImages.has(image.id)}
                    onChange={() => toggleImageSelection(image.id)}
                    className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>

                {/* Reorder Controls */}
                <div className="absolute top-1 left-1 flex flex-col space-y-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      moveImageUp(image.id);
                    }}
                    disabled={images.findIndex(img => img.id === image.id) === 0}
                    className="w-6 h-6 bg-black bg-opacity-75 text-white text-xs rounded hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    title="Move up"
                  >
                    ↑
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      moveImageDown(image.id);
                    }}
                    disabled={images.findIndex(img => img.id === image.id) === images.length - 1}
                    className="w-6 h-6 bg-black bg-opacity-75 text-white text-xs rounded hover:bg-opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    title="Move down"
                  >
                    ↓
                  </button>
                </div>

                {/* Page Type Badge */}
                <div className="absolute bottom-1 left-1 right-1">
                  <span className={`inline-block w-full text-center text-xs px-1 py-0.5 rounded ${getPageTypeColor(image.page_type)}`}>
                    {getPageTypeDisplayName(image.page_type)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {previewModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="max-w-4xl max-h-full p-4">
            <div className="bg-white rounded-lg overflow-hidden">
              <div className="flex justify-between items-center p-4 border-b">
                <h3 className="text-lg font-medium">{previewModal.imageName}</h3>
                <button
                  onClick={closeImagePreview}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Close</span>
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <img
                  src={previewModal.imageUrl}
                  alt={previewModal.imageName}
                  className="max-w-full max-h-96 mx-auto"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Page Type Assignment Modal */}
      <BookPageTypeModal
        isOpen={showPageTypeModal}
        onClose={() => setShowPageTypeModal(false)}
        images={images}
        selectedImageIds={Array.from(selectedImages)}
        onSave={handlePageTypeAssignments}
      />
    </div>
  );
}
