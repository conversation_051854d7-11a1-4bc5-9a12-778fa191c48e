/**
 * Utility functions for handling file uploads and filename parsing
 */

interface FileWithOrder {
  file: File;
  extractedNumber: number;
  originalIndex: number;
}

/**
 * Extracts numeric values from filenames for sorting
 * Handles various patterns like:
 * - page001.jpg, page002.jpg
 * - 001.jpg, 002.jpg
 * - scan_01.png, scan_02.png
 * - chapter1_page01.jpg
 */
export function extractNumberFromFilename(filename: string): number {
  // Remove file extension
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
  
  // Try to find the last number in the filename (most likely to be page number)
  const numbers = nameWithoutExt.match(/\d+/g);
  
  if (!numbers || numbers.length === 0) {
    return 0;
  }
  
  // Return the last number found (usually the page number)
  return parseInt(numbers[numbers.length - 1]);
}

/**
 * Sorts files by their extracted numbers and handles edge cases
 */
export function sortFilesByNumber(files: File[]): File[] {
  const filesWithOrder: FileWithOrder[] = files.map((file, index) => ({
    file,
    extractedNumber: extractNumberFromFilename(file.name),
    originalIndex: index
  }));

  // Sort by extracted number, then by original index if numbers are the same
  filesWithOrder.sort((a, b) => {
    if (a.extractedNumber !== b.extractedNumber) {
      return a.extractedNumber - b.extractedNumber;
    }
    
    // If numbers are the same, maintain original order
    return a.originalIndex - b.originalIndex;
  });

  return filesWithOrder.map(item => item.file);
}

/**
 * Detects if files appear to be a serialized sequence
 */
export function detectSerializedSequence(files: File[]): {
  isSequential: boolean;
  startNumber: number;
  gaps: number[];
  duplicates: number[];
} {
  const numbers = files.map(file => extractNumberFromFilename(file.name));
  const uniqueNumbers = [...new Set(numbers)];
  
  // Check for duplicates
  const duplicates = numbers.filter((num, index) => 
    numbers.indexOf(num) !== index && num > 0
  );

  if (uniqueNumbers.length === 0) {
    return {
      isSequential: false,
      startNumber: 0,
      gaps: [],
      duplicates: []
    };
  }

  const sortedNumbers = uniqueNumbers.filter(n => n > 0).sort((a, b) => a - b);
  
  if (sortedNumbers.length === 0) {
    return {
      isSequential: false,
      startNumber: 0,
      gaps: [],
      duplicates: []
    };
  }

  const startNumber = sortedNumbers[0];
  const endNumber = sortedNumbers[sortedNumbers.length - 1];
  const expectedLength = endNumber - startNumber + 1;
  
  // Find gaps in the sequence
  const gaps: number[] = [];
  for (let i = startNumber; i <= endNumber; i++) {
    if (!sortedNumbers.includes(i)) {
      gaps.push(i);
    }
  }

  const isSequential = sortedNumbers.length >= 2 && gaps.length === 0;

  return {
    isSequential,
    startNumber,
    gaps,
    duplicates: [...new Set(duplicates)]
  };
}

/**
 * Generates a suggested filename for uploaded files
 */
export function generatePageFilename(
  originalName: string, 
  pageNumber: number, 
  timestamp: number
): string {
  const extension = originalName.split('.').pop() || 'jpg';
  return `page_${pageNumber.toString().padStart(3, '0')}_${timestamp}.${extension}`;
}

/**
 * Validates file types for image uploads
 */
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return {
      isValid: false,
      error: `${file.name} is not an image file`
    };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `${file.name} is too large (max 10MB)`
    };
  }

  // Check for supported formats
  const supportedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];

  if (!supportedTypes.includes(file.type.toLowerCase())) {
    return {
      isValid: false,
      error: `${file.name} format not supported. Use JPG, PNG, GIF, or WebP`
    };
  }

  return { isValid: true };
}

/**
 * Analyzes a batch of files and provides upload recommendations
 */
export function analyzeFileUpload(files: File[]): {
  sortedFiles: File[];
  sequenceInfo: ReturnType<typeof detectSerializedSequence>;
  validationErrors: string[];
  recommendations: string[];
} {
  const validationErrors: string[] = [];
  const recommendations: string[] = [];

  // Validate each file
  files.forEach(file => {
    const validation = validateImageFile(file);
    if (!validation.isValid && validation.error) {
      validationErrors.push(validation.error);
    }
  });

  // Sort files by extracted numbers
  const sortedFiles = sortFilesByNumber(files);
  
  // Analyze sequence
  const sequenceInfo = detectSerializedSequence(files);

  // Generate recommendations
  if (sequenceInfo.isSequential) {
    recommendations.push(`✓ Files appear to be in sequence (${sequenceInfo.startNumber} to ${sequenceInfo.startNumber + files.length - 1})`);
  } else if (sequenceInfo.gaps.length > 0) {
    recommendations.push(`⚠ Missing pages detected: ${sequenceInfo.gaps.join(', ')}`);
  }

  if (sequenceInfo.duplicates.length > 0) {
    recommendations.push(`⚠ Duplicate page numbers found: ${sequenceInfo.duplicates.join(', ')}`);
  }

  if (files.length > 1 && !sequenceInfo.isSequential) {
    recommendations.push('💡 Consider renaming files with sequential numbers (e.g., page001.jpg, page002.jpg) for better organization');
  }

  return {
    sortedFiles,
    sequenceInfo,
    validationErrors,
    recommendations
  };
}
