{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\n'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { User } from '@/lib/auth';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  user?: User;\n}\n\nexport default function AdminLayout({ children, user }: AdminLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await fetch('/api/auth/logout', { method: 'POST' });\n      router.push('/login');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/admin', icon: '🏠' },\n    { name: 'Classes & Subjects', href: '/admin/classes', icon: '📚' },\n    { name: 'Upload Images', href: '/admin/upload', icon: '📤' },\n    { name: 'Questions', href: '/admin/questions', icon: '❓' },\n    { name: 'Tests', href: '/admin/tests', icon: '📝' },\n    { name: 'Users', href: '/admin/users', icon: '👥' },\n    { name: 'Results', href: '/admin/results', icon: '📊' },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <span className=\"sr-only\">Close sidebar</span>\n              <span className=\"text-white text-xl\">×</span>\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">Study App Admin</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 bg-white space-y-1\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <span className=\"mr-3 text-lg\">{item.icon}</span>\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            type=\"button\"\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <span className=\"sr-only\">Open sidebar</span>\n            <span className=\"text-xl\">☰</span>\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-medium text-gray-900 hidden md:block\">\n                  Admin Panel\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <span className=\"text-sm text-gray-700\">Welcome, {user?.name || 'Admin'}</span>\n                  <button\n                    onClick={handleLogout}\n                    className=\"bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium\"\n                  >\n                    Logout\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;;;AANA;AAEA;;;;AAYe,SAAS,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAoB;;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAAE,QAAQ;YAAO;YACjD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAU,MAAM;QAAK;QAChD;YAAE,MAAM;YAAsB,MAAM;YAAkB,MAAM;QAAK;QACjE;YAAE,MAAM;YAAiB,MAAM;YAAiB,MAAM;QAAK;QAC3D;YAAE,MAAM;YAAa,MAAM;YAAoB,MAAM;QAAI;QACzD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAS,MAAM;YAAgB,MAAM;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;YAAkB,MAAM;QAAK;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;;sDAE9B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;kDACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAgB,KAAK,IAAI;;;;;;oDACxC,KAAK,IAAI;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAkC;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAgB,KAAK,IAAI;;;;;;4CACxC,KAAK,IAAI;;uCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0BAc1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,6LAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;;;;;;kDAIpE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAwB;wDAAU,MAAM,QAAQ;;;;;;;8DAChE,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlIwB;;QAEP,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/ImagePreviewModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ImagePreviewModalProps {\n  isOpen: boolean;\n  imageUrl: string;\n  imageName: string;\n  onClose: () => void;\n}\n\nexport default function ImagePreviewModal({ isOpen, imageUrl, imageName, onClose }: ImagePreviewModalProps) {\n  const [zoom, setZoom] = useState(1);\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    if (isOpen) {\n      setZoom(1);\n      setPosition({ x: 0, y: 0 });\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleKeyDown);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [isOpen, onClose]);\n\n  const handleZoomIn = () => {\n    setZoom(prev => Math.min(prev * 1.5, 5));\n  };\n\n  const handleZoomOut = () => {\n    setZoom(prev => Math.max(prev / 1.5, 0.5));\n  };\n\n  const handleResetZoom = () => {\n    setZoom(1);\n    setPosition({ x: 0, y: 0 });\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    if (zoom > 1) {\n      setIsDragging(true);\n      setDragStart({\n        x: e.clientX - position.x,\n        y: e.clientY - position.y\n      });\n    }\n  };\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (isDragging && zoom > 1) {\n      setPosition({\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      });\n    }\n  };\n\n  const handleMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  const handleWheel = (e: React.WheelEvent) => {\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? 0.9 : 1.1;\n    setZoom(prev => Math.max(0.5, Math.min(5, prev * delta)));\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div \n      className=\"image-preview-modal\"\n      onClick={onClose}\n    >\n      <div \n        className=\"image-preview-content\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        <img\n          src={imageUrl}\n          alt={imageName}\n          className=\"image-preview-img\"\n          style={{\n            transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px)`,\n            cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'\n          }}\n          onMouseDown={handleMouseDown}\n          onMouseMove={handleMouseMove}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseUp}\n          onWheel={handleWheel}\n          draggable={false}\n        />\n        \n        <div className=\"image-preview-controls\">\n          <button onClick={handleZoomIn} title=\"Zoom In\">\n            🔍+\n          </button>\n          <button onClick={handleZoomOut} title=\"Zoom Out\">\n            🔍-\n          </button>\n          <button onClick={handleResetZoom} title=\"Reset Zoom\">\n            ↻\n          </button>\n          <button onClick={onClose} title=\"Close (Esc)\">\n            ✕\n          </button>\n        </div>\n        \n        <div \n          style={{\n            position: 'absolute',\n            bottom: '20px',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            background: 'rgba(0, 0, 0, 0.7)',\n            color: 'white',\n            padding: '8px 16px',\n            borderRadius: '6px',\n            fontSize: '14px'\n          }}\n        >\n          {imageName} - Zoom: {Math.round(zoom * 100)}%\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAA0B;;IACxG,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV,QAAQ;gBACR,YAAY;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACzB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;+CAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;sCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB,CAAC;oBACrB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;YACvC;YAEA;+CAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;sCAAG;QAAC;QAAQ;KAAQ;IAEpB,MAAM,eAAe;QACnB,QAAQ,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACvC;IAEA,MAAM,gBAAgB;QACpB,QAAQ,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,KAAK;IACvC;IAEA,MAAM,kBAAkB;QACtB,QAAQ;QACR,YAAY;YAAE,GAAG;YAAG,GAAG;QAAE;IAC3B;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,GAAG;YACZ,cAAc;YACd,aAAa;gBACX,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;gBACzB,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC;YAC3B;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,cAAc,OAAO,GAAG;YAC1B,YAAY;gBACV,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;gBAC1B,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC;YAC5B;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,cAAc;QAChB,MAAM,QAAQ,EAAE,MAAM,GAAG,IAAI,MAAM;QACnC,QAAQ,CAAA,OAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,OAAO;IACnD;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;kBAET,cAAA,6LAAC;YACC,WAAU;YACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAEjC,6LAAC;oBACC,KAAK;oBACL,KAAK;oBACL,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,MAAM,EAAE,KAAK,YAAY,EAAE,SAAS,CAAC,GAAG,KAAK,IAAI,EAAE,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;wBACrF,QAAQ,OAAO,IAAK,aAAa,aAAa,SAAU;oBAC1D;oBACA,aAAa;oBACb,aAAa;oBACb,WAAW;oBACX,cAAc;oBACd,SAAS;oBACT,WAAW;;;;;;8BAGb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,SAAS;4BAAc,OAAM;sCAAU;;;;;;sCAG/C,6LAAC;4BAAO,SAAS;4BAAe,OAAM;sCAAW;;;;;;sCAGjD,6LAAC;4BAAO,SAAS;4BAAiB,OAAM;sCAAa;;;;;;sCAGrD,6LAAC;4BAAO,SAAS;4BAAS,OAAM;sCAAc;;;;;;;;;;;;8BAKhD,6LAAC;oBACC,OAAO;wBACL,UAAU;wBACV,QAAQ;wBACR,MAAM;wBACN,WAAW;wBACX,YAAY;wBACZ,OAAO;wBACP,SAAS;wBACT,cAAc;wBACd,UAAU;oBACZ;;wBAEC;wBAAU;wBAAU,KAAK,KAAK,CAAC,OAAO;wBAAK;;;;;;;;;;;;;;;;;;AAKtD;GA1IwB;KAAA", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/ChapterClassificationModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ChapterClassificationModalProps {\n  isOpen: boolean;\n  images: any[];\n  onClose: () => void;\n  onSave: (classifications: { [key: number]: string }) => void;\n}\n\nexport default function ChapterClassificationModal({ \n  isOpen, \n  images, \n  onClose, \n  onSave \n}: ChapterClassificationModalProps) {\n  const [classifications, setClassifications] = useState<{ [key: number]: string }>({});\n  const [currentChapter, setCurrentChapter] = useState(1);\n  const [autoMode, setAutoMode] = useState(true);\n\n  useEffect(() => {\n    if (isOpen) {\n      // Initialize classifications from existing data\n      const initial: { [key: number]: string } = {};\n      images.forEach(img => {\n        initial[img.id] = img.page_type || 'unassigned';\n      });\n      setClassifications(initial);\n    }\n  }, [isOpen, images]);\n\n  const pageTypeOptions = [\n    { value: 'cover', label: 'Cover Page', color: 'bg-purple-100 text-purple-800' },\n    { value: 'contents', label: 'Contents/Index', color: 'bg-blue-100 text-blue-800' },\n    { value: 'unassigned', label: 'Unassigned', color: 'bg-gray-100 text-gray-800' },\n    ...Array.from({length: 30}, (_, i) => ({\n      value: `chapter-${i + 1}`,\n      label: `Chapter ${i + 1}`,\n      color: 'bg-green-100 text-green-800'\n    }))\n  ];\n\n  const handleClassificationChange = (imageId: number, pageType: string) => {\n    setClassifications(prev => ({\n      ...prev,\n      [imageId]: pageType\n    }));\n  };\n\n  const handleAutoClassify = () => {\n    const newClassifications: { [key: number]: string } = {};\n    let currentChapterNum = 1;\n    let foundFirstChapter = false;\n\n    images.forEach((image, index) => {\n      if (index === 0) {\n        newClassifications[image.id] = 'cover';\n      } else if (index === 1 || index === 2) {\n        newClassifications[image.id] = 'contents';\n      } else {\n        // Look for chapter markers in existing classifications\n        const existingType = classifications[image.id];\n        if (existingType && existingType.startsWith('chapter-')) {\n          const chapterMatch = existingType.match(/chapter-(\\d+)/);\n          if (chapterMatch) {\n            currentChapterNum = parseInt(chapterMatch[1]);\n            foundFirstChapter = true;\n          }\n          newClassifications[image.id] = existingType;\n        } else if (foundFirstChapter) {\n          newClassifications[image.id] = `chapter-${currentChapterNum}`;\n        } else {\n          // Default to chapter 1 if no chapter markers found\n          newClassifications[image.id] = 'chapter-1';\n          foundFirstChapter = true;\n        }\n      }\n    });\n\n    setClassifications(newClassifications);\n  };\n\n  const handleBulkAssign = (startIndex: number, endIndex: number, pageType: string) => {\n    const newClassifications = { ...classifications };\n    for (let i = startIndex; i <= endIndex; i++) {\n      if (images[i]) {\n        newClassifications[images[i].id] = pageType;\n      }\n    }\n    setClassifications(newClassifications);\n  };\n\n  const handleSave = () => {\n    onSave(classifications);\n    onClose();\n  };\n\n  const getPageTypeInfo = (pageType: string) => {\n    return pageTypeOptions.find(opt => opt.value === pageType) || pageTypeOptions[2];\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              Chapter Classification - {images.length} Images\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Quick Actions */}\n          <div className=\"mb-6 flex flex-wrap gap-3\">\n            <button\n              onClick={handleAutoClassify}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n            >\n              🤖 Auto-Classify All\n            </button>\n            \n            <div className=\"flex items-center space-x-2\">\n              <label className=\"text-sm font-medium text-gray-700\">Bulk assign:</label>\n              <select\n                value={currentChapter}\n                onChange={(e) => setCurrentChapter(Number(e.target.value))}\n                className=\"h-8 px-2 border-gray-300 rounded text-sm text-gray-900 bg-white\"\n              >\n                {Array.from({length: 30}, (_, i) => (\n                  <option key={i + 1} value={i + 1}>Chapter {i + 1}</option>\n                ))}\n              </select>\n              <button\n                onClick={() => {\n                  const unassigned = images\n                    .map((img, idx) => ({ img, idx }))\n                    .filter(({ img }) => classifications[img.id] === 'unassigned' || !classifications[img.id]);\n                  \n                  if (unassigned.length > 0) {\n                    handleBulkAssign(\n                      unassigned[0].idx,\n                      unassigned[unassigned.length - 1].idx,\n                      `chapter-${currentChapter}`\n                    );\n                  }\n                }}\n                className=\"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm\"\n              >\n                Assign to Unassigned\n              </button>\n            </div>\n          </div>\n\n          {/* Images Grid */}\n          <div className=\"overflow-y-auto max-h-96\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n              {images.map((image, index) => {\n                const pageTypeInfo = getPageTypeInfo(classifications[image.id] || 'unassigned');\n                \n                return (\n                  <div key={image.id} className=\"border border-gray-200 rounded-lg overflow-hidden\">\n                    <div className=\"relative\">\n                      <img\n                        src={`/api/admin/images/${image.id}/preview`}\n                        alt={image.original_name}\n                        className=\"w-full h-32 object-cover\"\n                        onError={(e) => {\n                          (e.target as HTMLImageElement).src = '/placeholder-image.svg';\n                        }}\n                      />\n                      <div className=\"absolute top-2 left-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs\">\n                        #{index + 1}\n                      </div>\n                    </div>\n                    \n                    <div className=\"p-3\">\n                      <div className=\"text-xs text-gray-600 mb-2 truncate\">\n                        {image.original_name}\n                      </div>\n                      \n                      <div className=\"mb-2\">\n                        <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${pageTypeInfo.color}`}>\n                          {pageTypeInfo.label}\n                        </span>\n                      </div>\n                      \n                      <select\n                        value={classifications[image.id] || 'unassigned'}\n                        onChange={(e) => handleClassificationChange(image.id, e.target.value)}\n                        className=\"w-full h-8 px-2 border-gray-300 rounded text-xs text-gray-900 bg-white\"\n                      >\n                        {pageTypeOptions.map((option) => (\n                          <option key={option.value} value={option.value}>\n                            {option.label}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Summary */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Classification Summary:</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {pageTypeOptions.map(option => {\n                const count = Object.values(classifications).filter(c => c === option.value).length;\n                if (count === 0) return null;\n                \n                return (\n                  <span key={option.value} className={`px-2 py-1 rounded-full text-xs font-medium ${option.color}`}>\n                    {option.label}: {count}\n                  </span>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"px-6 py-4 border-t border-gray-200 flex justify-end space-x-3\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n          >\n            Cancel\n          </button>\n          <button\n            onClick={handleSave}\n            className=\"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm font-medium\"\n          >\n            Save Classifications\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,2BAA2B,EACjD,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EAC0B;;IAChC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,QAAQ;gBACV,gDAAgD;gBAChD,MAAM,UAAqC,CAAC;gBAC5C,OAAO,OAAO;4DAAC,CAAA;wBACb,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,IAAI,SAAS,IAAI;oBACrC;;gBACA,mBAAmB;YACrB;QACF;+CAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAS,OAAO;YAAc,OAAO;QAAgC;QAC9E;YAAE,OAAO;YAAY,OAAO;YAAkB,OAAO;QAA4B;QACjF;YAAE,OAAO;YAAc,OAAO;YAAc,OAAO;QAA4B;WAC5E,MAAM,IAAI,CAAC;YAAC,QAAQ;QAAE,GAAG,CAAC,GAAG,IAAM,CAAC;gBACrC,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;gBACzB,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;gBACzB,OAAO;YACT,CAAC;KACF;IAED,MAAM,6BAA6B,CAAC,SAAiB;QACnD,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;YACb,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,MAAM,qBAAgD,CAAC;QACvD,IAAI,oBAAoB;QACxB,IAAI,oBAAoB;QAExB,OAAO,OAAO,CAAC,CAAC,OAAO;YACrB,IAAI,UAAU,GAAG;gBACf,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG;YACjC,OAAO,IAAI,UAAU,KAAK,UAAU,GAAG;gBACrC,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG;YACjC,OAAO;gBACL,uDAAuD;gBACvD,MAAM,eAAe,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC9C,IAAI,gBAAgB,aAAa,UAAU,CAAC,aAAa;oBACvD,MAAM,eAAe,aAAa,KAAK,CAAC;oBACxC,IAAI,cAAc;wBAChB,oBAAoB,SAAS,YAAY,CAAC,EAAE;wBAC5C,oBAAoB;oBACtB;oBACA,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG;gBACjC,OAAO,IAAI,mBAAmB;oBAC5B,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,mBAAmB;gBAC/D,OAAO;oBACL,mDAAmD;oBACnD,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG;oBAC/B,oBAAoB;gBACtB;YACF;QACF;QAEA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC,YAAoB,UAAkB;QAC9D,MAAM,qBAAqB;YAAE,GAAG,eAAe;QAAC;QAChD,IAAK,IAAI,IAAI,YAAY,KAAK,UAAU,IAAK;YAC3C,IAAI,MAAM,CAAC,EAAE,EAAE;gBACb,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG;YACrC;QACF;QACA,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,OAAO;QACP;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK,aAAa,eAAe,CAAC,EAAE;IAClF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCACxB,OAAO,MAAM;oCAAC;;;;;;;0CAE1C,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAML,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAID,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAoC;;;;;;sDACrD,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;4CACxD,WAAU;sDAET,MAAM,IAAI,CAAC;gDAAC,QAAQ;4CAAE,GAAG,CAAC,GAAG,kBAC5B,6LAAC;oDAAmB,OAAO,IAAI;;wDAAG;wDAAS,IAAI;;mDAAlC,IAAI;;;;;;;;;;sDAGrB,6LAAC;4CACC,SAAS;gDACP,MAAM,aAAa,OAChB,GAAG,CAAC,CAAC,KAAK,MAAQ,CAAC;wDAAE;wDAAK;oDAAI,CAAC,GAC/B,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,GAAK,eAAe,CAAC,IAAI,EAAE,CAAC,KAAK,gBAAgB,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;gDAE3F,IAAI,WAAW,MAAM,GAAG,GAAG;oDACzB,iBACE,UAAU,CAAC,EAAE,CAAC,GAAG,EACjB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,GAAG,EACrC,CAAC,QAAQ,EAAE,gBAAgB;gDAE/B;4CACF;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,OAAO;oCAClB,MAAM,eAAe,gBAAgB,eAAe,CAAC,MAAM,EAAE,CAAC,IAAI;oCAElE,qBACE,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;wDAC5C,KAAK,MAAM,aAAa;wDACxB,WAAU;wDACV,SAAS,CAAC;4DACP,EAAE,MAAM,CAAsB,GAAG,GAAG;wDACvC;;;;;;kEAEF,6LAAC;wDAAI,WAAU;;4DAAoF;4DAC/F,QAAQ;;;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,MAAM,aAAa;;;;;;kEAGtB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAW,CAAC,wDAAwD,EAAE,aAAa,KAAK,EAAE;sEAC7F,aAAa,KAAK;;;;;;;;;;;kEAIvB,6LAAC;wDACC,OAAO,eAAe,CAAC,MAAM,EAAE,CAAC,IAAI;wDACpC,UAAU,CAAC,IAAM,2BAA2B,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wDACpE,WAAU;kEAET,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;gEAA0B,OAAO,OAAO,KAAK;0EAC3C,OAAO,KAAK;+DADF,OAAO,KAAK;;;;;;;;;;;;;;;;;uCAhCvB,MAAM,EAAE;;;;;gCAwCtB;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAA;wCACnB,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM,OAAO,KAAK,EAAE,MAAM;wCACnF,IAAI,UAAU,GAAG,OAAO;wCAExB,qBACE,6LAAC;4CAAwB,WAAW,CAAC,2CAA2C,EAAE,OAAO,KAAK,EAAE;;gDAC7F,OAAO,KAAK;gDAAC;gDAAG;;2CADR,OAAO,KAAK;;;;;oCAI3B;;;;;;;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA9OwB;KAAA", "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/components/ImageUploader.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { Class, Subject } from '@/lib/models';\nimport ImagePreviewModal from './ImagePreviewModal';\nimport ChapterClassificationModal from './ChapterClassificationModal';\n\ninterface ImageUploaderProps {\n  classes: Class[];\n  subjects: Subject[];\n  initialImages: any[];\n}\n\nexport default function ImageUploader({ classes, subjects, initialImages }: ImageUploaderProps) {\n  const [images, setImages] = useState(initialImages);\n  const [selectedClassId, setSelectedClassId] = useState<number | null>(null);\n  const [selectedSubjectId, setSelectedSubjectId] = useState<number | null>(null);\n  const [uploading, setUploading] = useState(false);\n  const [processing, setProcessing] = useState<number[]>([]);\n  const [error, setError] = useState('');\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Image preview modal state\n  const [previewModal, setPreviewModal] = useState({\n    isOpen: false,\n    imageUrl: '',\n    imageName: ''\n  });\n\n  // Chapter classification modal state\n  const [classificationModal, setClassificationModal] = useState(false);\n\n  const filteredSubjects = selectedClassId \n    ? subjects.filter(s => s.class_id === selectedClassId)\n    : [];\n\n  const filteredImages = images.filter(img => {\n    if (selectedClassId && img.class_id !== selectedClassId) return false;\n    if (selectedSubjectId && img.subject_id !== selectedSubjectId) return false;\n    return true;\n  });\n\n  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = event.target.files;\n    if (!files || files.length === 0) return;\n\n    if (!selectedClassId || !selectedSubjectId) {\n      setError('Please select both class and subject before uploading');\n      return;\n    }\n\n    setUploading(true);\n    setError('');\n\n    try {\n      for (const file of Array.from(files)) {\n        // Validate file type\n        if (!file.type.startsWith('image/')) {\n          setError(`${file.name} is not an image file`);\n          continue;\n        }\n\n        // Validate file size (max 10MB)\n        if (file.size > 10 * 1024 * 1024) {\n          setError(`${file.name} is too large (max 10MB)`);\n          continue;\n        }\n\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('classId', selectedClassId.toString());\n        formData.append('subjectId', selectedSubjectId.toString());\n\n        const response = await fetch('/api/admin/upload', {\n          method: 'POST',\n          body: formData,\n        });\n\n        if (response.ok) {\n          const result = await response.json();\n          setImages(prev => [result.image, ...prev]);\n          \n          // Start OCR processing\n          processOCR(result.image.id);\n        } else {\n          const errorData = await response.json();\n          setError(errorData.error || `Failed to upload ${file.name}`);\n        }\n      }\n    } catch (error) {\n      setError('Upload failed. Please try again.');\n    } finally {\n      setUploading(false);\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    }\n  };\n\n  const processOCR = async (imageId: number) => {\n    setProcessing(prev => [...prev, imageId]);\n\n    try {\n      console.log(`Starting OCR for image ID: ${imageId}`);\n\n      const response = await fetch(`/api/admin/images/${imageId}/ocr`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n      });\n\n      const result = await response.json();\n\n      if (response.ok) {\n        console.log(`OCR completed for image ID: ${imageId}`, result);\n\n        setImages(prev => prev.map(img =>\n          img.id === imageId\n            ? { ...img, ocr_content: result.text, processed: true }\n            : img\n        ));\n      } else {\n        console.error('OCR processing failed for image', imageId, result);\n        setError(`OCR failed: ${result.details || result.error}`);\n      }\n    } catch (error) {\n      console.error('OCR processing error:', error);\n      setError('OCR processing failed - network error');\n    } finally {\n      setProcessing(prev => prev.filter(id => id !== imageId));\n    }\n  };\n\n  const handleDeleteImage = async (imageId: number) => {\n    if (!confirm('Are you sure you want to delete this image? This will also delete any extracted questions.')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/admin/images/${imageId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setImages(prev => prev.filter(img => img.id !== imageId));\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to delete image');\n      }\n    } catch (error) {\n      setError('Delete failed. Please try again.');\n    }\n  };\n\n  const handlePageTypeChange = async (imageId: number, pageType: string) => {\n    try {\n      const response = await fetch(`/api/admin/images/${imageId}/page-type`, {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ pageType }),\n      });\n\n      if (response.ok) {\n        setImages(prev => prev.map(img =>\n          img.id === imageId ? { ...img, page_type: pageType } : img\n        ));\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to update page type');\n      }\n    } catch (error) {\n      setError('Failed to update page type');\n    }\n  };\n\n  const handleAutoAssignPageTypes = async () => {\n    if (!selectedClassId || !selectedSubjectId) {\n      setError('Please select both class and subject');\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/admin/images/auto-assign', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ classId: selectedClassId, subjectId: selectedSubjectId }),\n      });\n\n      if (response.ok) {\n        // Refresh images to show updated page types\n        const updatedImages = await fetch(`/api/admin/images?classId=${selectedClassId}&subjectId=${selectedSubjectId}`);\n        if (updatedImages.ok) {\n          const data = await updatedImages.json();\n          setImages(data);\n        }\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to auto-assign page types');\n      }\n    } catch (error) {\n      setError('Failed to auto-assign page types');\n    }\n  };\n\n  const triggerFileInput = () => {\n    fileInputRef.current?.click();\n  };\n\n  const pageTypeOptions = [\n    { value: 'unassigned', label: 'Unassigned' },\n    { value: 'cover', label: 'Cover Page' },\n    { value: 'contents', label: 'Contents Page' },\n    ...Array.from({length: 30}, (_, i) => ({\n      value: `chapter-${i + 1}`,\n      label: `Chapter ${i + 1}`\n    }))\n  ];\n\n  const openImagePreview = (imageId: number, imageName: string) => {\n    setPreviewModal({\n      isOpen: true,\n      imageUrl: `/api/admin/images/${imageId}/preview`,\n      imageName: imageName\n    });\n  };\n\n  const closeImagePreview = () => {\n    setPreviewModal({\n      isOpen: false,\n      imageUrl: '',\n      imageName: ''\n    });\n  };\n\n  const handleBulkClassification = async (classifications: { [key: number]: string }) => {\n    try {\n      // Update all classifications\n      const promises = Object.entries(classifications).map(([imageId, pageType]) =>\n        fetch(`/api/admin/images/${imageId}/page-type`, {\n          method: 'PUT',\n          headers: { 'Content-Type': 'application/json' },\n          body: JSON.stringify({ pageType }),\n        })\n      );\n\n      await Promise.all(promises);\n\n      // Refresh images to show updated page types\n      if (selectedClassId && selectedSubjectId) {\n        const response = await fetch(`/api/admin/images?classId=${selectedClassId}&subjectId=${selectedSubjectId}`);\n        if (response.ok) {\n          const data = await response.json();\n          setImages(data);\n        }\n      }\n    } catch (error) {\n      setError('Failed to update page classifications');\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Form */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900 mb-4\">\n            Upload New Images\n          </h3>\n\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\n              {error}\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">\n                Class\n              </label>\n              <select\n                value={selectedClassId || ''}\n                onChange={(e) => {\n                  setSelectedClassId(e.target.value ? Number(e.target.value) : null);\n                  setSelectedSubjectId(null);\n                }}\n                className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n              >\n                <option value=\"\">Select a class</option>\n                {classes.map((cls) => (\n                  <option key={cls.id} value={cls.id}>{cls.name}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700\">\n                Subject\n              </label>\n              <select\n                value={selectedSubjectId || ''}\n                onChange={(e) => setSelectedSubjectId(e.target.value ? Number(e.target.value) : null)}\n                disabled={!selectedClassId}\n                className=\"mt-1 block w-full h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white disabled:bg-gray-100 disabled:text-gray-500\"\n              >\n                <option value=\"\">Select a subject</option>\n                {filteredSubjects.map((subject) => (\n                  <option key={subject.id} value={subject.id}>{subject.name}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"flex items-end\">\n              <button\n                onClick={triggerFileInput}\n                disabled={!selectedClassId || !selectedSubjectId || uploading}\n                className=\"w-full h-10 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {uploading ? 'Uploading...' : 'Select Images'}\n              </button>\n            </div>\n          </div>\n\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            multiple\n            accept=\"image/*\"\n            onChange={handleFileSelect}\n            className=\"hidden\"\n          />\n\n          <div className=\"mt-4 text-sm text-gray-600\">\n            <p>• Supported formats: JPG, PNG, GIF, WebP</p>\n            <p>• Maximum file size: 10MB per image</p>\n            <p>• Multiple images can be selected at once</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Images List */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n              Uploaded Images ({filteredImages.length})\n            </h3>\n\n            <div className=\"flex space-x-3\">\n              <select\n                value={selectedClassId || ''}\n                onChange={(e) => {\n                  setSelectedClassId(e.target.value ? Number(e.target.value) : null);\n                  setSelectedSubjectId(null);\n                }}\n                className=\"h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n              >\n                <option value=\"\">All Classes</option>\n                {classes.map((cls) => (\n                  <option key={cls.id} value={cls.id}>{cls.name}</option>\n                ))}\n              </select>\n\n              {selectedClassId && (\n                <select\n                  value={selectedSubjectId || ''}\n                  onChange={(e) => setSelectedSubjectId(e.target.value ? Number(e.target.value) : null)}\n                  className=\"h-10 px-3 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm text-gray-900 bg-white\"\n                >\n                  <option value=\"\">All Subjects</option>\n                  {filteredSubjects.map((subject) => (\n                    <option key={subject.id} value={subject.id}>{subject.name}</option>\n                  ))}\n                </select>\n              )}\n\n              {selectedClassId && selectedSubjectId && filteredImages.length > 0 && (\n                <button\n                  onClick={() => setClassificationModal(true)}\n                  className=\"h-10 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  title=\"Smart chapter classification tool\"\n                >\n                  📚 Classify Pages\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Images Grid */}\n          <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {filteredImages.map((image) => (\n              <div key={image.id} className=\"border border-gray-200 rounded-lg overflow-hidden\">\n                <div\n                  className=\"relative aspect-w-16 aspect-h-9 bg-gray-100 cursor-pointer hover:bg-gray-200 transition-colors group\"\n                  onClick={() => openImagePreview(image.id, image.original_name)}\n                  title=\"Click to view full image\"\n                >\n                  <img\n                    src={`/api/admin/images/${image.id}/preview`}\n                    alt={image.original_name}\n                    className=\"w-full h-48 object-cover group-hover:opacity-90 transition-opacity\"\n                    onError={(e) => {\n                      (e.target as HTMLImageElement).src = '/placeholder-image.svg';\n                    }}\n                  />\n                  <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-20 pointer-events-none\">\n                    <span className=\"text-white text-sm font-medium bg-black bg-opacity-50 px-2 py-1 rounded\">\n                      🔍 Click to preview\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"p-4\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <h4 className=\"text-sm font-medium text-gray-900 truncate\">\n                      {image.original_name}\n                    </h4>\n                    <button\n                      onClick={() => handleDeleteImage(image.id)}\n                      className=\"text-red-600 hover:text-red-900 text-sm\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 space-y-1 mb-3\">\n                    <p>{image.class_name} - {image.subject_name}</p>\n                    <p>Order: #{image.upload_order} | Uploaded: {new Date(image.uploaded_at).toLocaleDateString()}</p>\n                  </div>\n\n                  {/* Page Type Selector */}\n                  <div className=\"mb-3\">\n                    <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                      Page Type\n                    </label>\n                    <select\n                      value={image.page_type || 'unassigned'}\n                      onChange={(e) => handlePageTypeChange(image.id, e.target.value)}\n                      className=\"block w-full h-8 px-2 text-xs border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-900 bg-white\"\n                    >\n                      {pageTypeOptions.map((option) => (\n                        <option key={option.value} value={option.value}>\n                          {option.label}\n                        </option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* OCR Status */}\n                  <div className=\"mt-3\">\n                    {processing.includes(image.id) ? (\n                      <div className=\"flex items-center text-yellow-600\">\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2\"></div>\n                        <span className=\"text-xs\">Processing OCR...</span>\n                      </div>\n                    ) : image.ocr_content ? (\n                      <div className=\"text-green-600 text-xs flex items-center\">\n                        <span className=\"mr-1\">✓</span>\n                        OCR Complete\n                      </div>\n                    ) : (\n                      <div className=\"text-gray-500 text-xs\">\n                        OCR Pending\n                      </div>\n                    )}\n                  </div>\n\n                  {/* OCR Text Preview */}\n                  {image.ocr_content && (\n                    <div className=\"mt-3\">\n                      <details className=\"text-xs\">\n                        <summary className=\"cursor-pointer text-indigo-600 hover:text-indigo-800\">\n                          View OCR Text\n                        </summary>\n                        <div className=\"mt-2 p-2 bg-gray-50 rounded text-gray-700 max-h-32 overflow-y-auto\">\n                          {image.ocr_content.substring(0, 200)}\n                          {image.ocr_content.length > 200 && '...'}\n                        </div>\n                      </details>\n                    </div>\n                  )}\n\n                  {/* Actions */}\n                  <div className=\"mt-3 flex space-x-2\">\n                    {image.ocr_content && (\n                      <button\n                        onClick={() => window.open(`/admin/questions?imageId=${image.id}`, '_blank')}\n                        className=\"text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded hover:bg-indigo-200\"\n                      >\n                        Extract Questions\n                      </button>\n                    )}\n\n                    {!image.ocr_content && !processing.includes(image.id) && (\n                      <button\n                        onClick={() => processOCR(image.id)}\n                        className=\"text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded hover:bg-yellow-200\"\n                      >\n                        Retry OCR\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredImages.length === 0 && (\n            <div className=\"text-center py-12\">\n              <div className=\"text-gray-400 text-6xl mb-4\">📷</div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No images uploaded</h3>\n              <p className=\"text-gray-500\">\n                {selectedClassId || selectedSubjectId\n                  ? 'No images found for the selected filters.'\n                  : 'Upload your first textbook image to get started.'\n                }\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Image Preview Modal */}\n      <ImagePreviewModal\n        isOpen={previewModal.isOpen}\n        imageUrl={previewModal.imageUrl}\n        imageName={previewModal.imageName}\n        onClose={closeImagePreview}\n      />\n\n      {/* Chapter Classification Modal */}\n      <ChapterClassificationModal\n        isOpen={classificationModal}\n        images={filteredImages}\n        onClose={() => setClassificationModal(false)}\n        onSave={handleBulkClassification}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAae,SAAS,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAsB;;IAC5F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,4BAA4B;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,QAAQ;QACR,UAAU;QACV,WAAW;IACb;IAEA,qCAAqC;IACrC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,mBAAmB,kBACrB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,mBACpC,EAAE;IAEN,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,IAAI,mBAAmB,IAAI,QAAQ,KAAK,iBAAiB,OAAO;QAChE,IAAI,qBAAqB,IAAI,UAAU,KAAK,mBAAmB,OAAO;QACtE,OAAO;IACT;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,MAAM,MAAM,CAAC,KAAK;QAChC,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;YAC1C,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;gBACpC,qBAAqB;gBACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,SAAS,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;oBAC5C;gBACF;gBAEA,gCAAgC;gBAChC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;oBAChC,SAAS,GAAG,KAAK,IAAI,CAAC,wBAAwB,CAAC;oBAC/C;gBACF;gBAEA,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBACxB,SAAS,MAAM,CAAC,WAAW,gBAAgB,QAAQ;gBACnD,SAAS,MAAM,CAAC,aAAa,kBAAkB,QAAQ;gBAEvD,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,MAAM;gBACR;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,UAAU,CAAA,OAAQ;4BAAC,OAAO,KAAK;+BAAK;yBAAK;oBAEzC,uBAAuB;oBACvB,WAAW,OAAO,KAAK,CAAC,EAAE;gBAC5B,OAAO;oBACL,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,SAAS,UAAU,KAAK,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;gBAC7D;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;YACb,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;QAExC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,SAAS;YAEnD,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ,IAAI,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,EAAE;gBACf,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS,EAAE;gBAEtD,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MACzB,IAAI,EAAE,KAAK,UACP;4BAAE,GAAG,GAAG;4BAAE,aAAa,OAAO,IAAI;4BAAE,WAAW;wBAAK,IACpD;YAER,OAAO;gBACL,QAAQ,KAAK,CAAC,mCAAmC,SAAS;gBAC1D,SAAS,CAAC,YAAY,EAAE,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS;QACX,SAAU;YACR,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;QACjD;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,+FAA+F;YAC1G;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE;gBAC3D,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAClD,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,OAAO,SAAiB;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ,UAAU,CAAC,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MACzB,IAAI,EAAE,KAAK,UAAU;4BAAE,GAAG,GAAG;4BAAE,WAAW;wBAAS,IAAI;YAE3D,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB;YAC1C,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;oBAAiB,WAAW;gBAAkB;YAChF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,4CAA4C;gBAC5C,MAAM,gBAAgB,MAAM,MAAM,CAAC,0BAA0B,EAAE,gBAAgB,WAAW,EAAE,mBAAmB;gBAC/G,IAAI,cAAc,EAAE,EAAE;oBACpB,MAAM,OAAO,MAAM,cAAc,IAAI;oBACrC,UAAU;gBACZ;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAS,OAAO;QAAa;QACtC;YAAE,OAAO;YAAY,OAAO;QAAgB;WACzC,MAAM,IAAI,CAAC;YAAC,QAAQ;QAAE,GAAG,CAAC,GAAG,IAAM,CAAC;gBACrC,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;gBACzB,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG;YAC3B,CAAC;KACF;IAED,MAAM,mBAAmB,CAAC,SAAiB;QACzC,gBAAgB;YACd,QAAQ;YACR,UAAU,CAAC,kBAAkB,EAAE,QAAQ,QAAQ,CAAC;YAChD,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,gBAAgB;YACd,QAAQ;YACR,UAAU;YACV,WAAW;QACb;IACF;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI;YACF,6BAA6B;YAC7B,MAAM,WAAW,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,SAAS,SAAS,GACvE,MAAM,CAAC,kBAAkB,EAAE,QAAQ,UAAU,CAAC,EAAE;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAS;gBAClC;YAGF,MAAM,QAAQ,GAAG,CAAC;YAElB,4CAA4C;YAC5C,IAAI,mBAAmB,mBAAmB;gBACxC,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,gBAAgB,WAAW,EAAE,mBAAmB;gBAC1G,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,UAAU;gBACZ;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmD;;;;;;wBAIhE,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAG3D,6LAAC;4CACC,OAAO,mBAAmB;4CAC1B,UAAU,CAAC;gDACT,mBAAmB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC7D,qBAAqB;4CACvB;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,6LAAC;wDAAoB,OAAO,IAAI,EAAE;kEAAG,IAAI,IAAI;uDAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;8CAKzB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA0C;;;;;;sDAG3D,6LAAC;4CACC,OAAO,qBAAqB;4CAC5B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;4CAChF,UAAU,CAAC;4CACX,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wDAAwB,OAAO,QAAQ,EAAE;kEAAG,QAAQ,IAAI;uDAA5C,QAAQ,EAAE;;;;;;;;;;;;;;;;;8CAK7B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,mBAAmB,CAAC,qBAAqB;wCACpD,WAAU;kDAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;sCAKpC,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAQ;4BACR,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;sCAGZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAA8C;wCACxC,eAAe,MAAM;wCAAC;;;;;;;8CAG1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,OAAO,mBAAmB;4CAC1B,UAAU,CAAC;gDACT,mBAAmB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;gDAC7D,qBAAqB;4CACvB;4CACA,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,QAAQ,GAAG,CAAC,CAAC,oBACZ,6LAAC;wDAAoB,OAAO,IAAI,EAAE;kEAAG,IAAI,IAAI;uDAAhC,IAAI,EAAE;;;;;;;;;;;wCAItB,iCACC,6LAAC;4CACC,OAAO,qBAAqB;4CAC5B,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK,GAAG,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI;4CAChF,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;wDAAwB,OAAO,QAAQ,EAAE;kEAAG,QAAQ,IAAI;uDAA5C,QAAQ,EAAE;;;;;;;;;;;wCAK5B,mBAAmB,qBAAqB,eAAe,MAAM,GAAG,mBAC/D,6LAAC;4CACC,SAAS,IAAM,uBAAuB;4CACtC,WAAU;4CACV,OAAM;sDACP;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oCAAmB,WAAU;;sDAC5B,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,iBAAiB,MAAM,EAAE,EAAE,MAAM,aAAa;4CAC7D,OAAM;;8DAEN,6LAAC;oDACC,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC;oDAC5C,KAAK,MAAM,aAAa;oDACxB,WAAU;oDACV,SAAS,CAAC;wDACP,EAAE,MAAM,CAAsB,GAAG,GAAG;oDACvC;;;;;;8DAEF,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA0E;;;;;;;;;;;;;;;;;sDAM9F,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,MAAM,aAAa;;;;;;sEAEtB,6LAAC;4DACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;4DACzC,WAAU;sEACX;;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAG,MAAM,UAAU;gEAAC;gEAAI,MAAM,YAAY;;;;;;;sEAC3C,6LAAC;;gEAAE;gEAAS,MAAM,YAAY;gEAAC;gEAAc,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;8DAI7F,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,OAAO,MAAM,SAAS,IAAI;4DAC1B,UAAU,CAAC,IAAM,qBAAqB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4DAC9D,WAAU;sEAET,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;;;;;;;8DAQ/B,6LAAC;oDAAI,WAAU;8DACZ,WAAW,QAAQ,CAAC,MAAM,EAAE,kBAC3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;+DAE1B,MAAM,WAAW,iBACnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAO;;;;;;4DAAQ;;;;;;6EAIjC,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;gDAO1C,MAAM,WAAW,kBAChB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAQ,WAAU;;0EACjB,6LAAC;gEAAQ,WAAU;0EAAuD;;;;;;0EAG1E,6LAAC;gEAAI,WAAU;;oEACZ,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG;oEAC/B,MAAM,WAAW,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;;;;;;;8DAO3C,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,WAAW,kBAChB,6LAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,yBAAyB,EAAE,MAAM,EAAE,EAAE,EAAE;4DACnE,WAAU;sEACX;;;;;;wDAKF,CAAC,MAAM,WAAW,IAAI,CAAC,WAAW,QAAQ,CAAC,MAAM,EAAE,mBAClD,6LAAC;4DACC,SAAS,IAAM,WAAW,MAAM,EAAE;4DAClC,WAAU;sEACX;;;;;;;;;;;;;;;;;;;mCA1GC,MAAM,EAAE;;;;;;;;;;wBAoHrB,eAAe,MAAM,KAAK,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CACV,mBAAmB,oBAChB,8CACA;;;;;;;;;;;;;;;;;;;;;;;0BASd,6LAAC,0IAAA,CAAA,UAAiB;gBAChB,QAAQ,aAAa,MAAM;gBAC3B,UAAU,aAAa,QAAQ;gBAC/B,WAAW,aAAa,SAAS;gBACjC,SAAS;;;;;;0BAIX,6LAAC,mJAAA,CAAA,UAA0B;gBACzB,QAAQ;gBACR,QAAQ;gBACR,SAAS,IAAM,uBAAuB;gBACtC,QAAQ;;;;;;;;;;;;AAIhB;GA7gBwB;KAAA", "debugId": null}}]}