import { ImageAnnotatorClient } from '@google-cloud/vision';
import fs from 'fs';

// Initialize the Google Vision client
// Note: You'll need to set up authentication via environment variables or service account key
let visionClient: ImageAnnotatorClient | null = null;

function getVisionClient(): ImageAnnotatorClient {
  if (!visionClient) {
    try {
      // Initialize with default credentials (from environment)
      visionClient = new ImageAnnotatorClient();
    } catch (error) {
      console.error('Failed to initialize Google Vision client:', error);
      throw new Error('Google Vision API not configured. Please set up authentication.');
    }
  }
  return visionClient;
}

export interface OCRResult {
  text: string;
  confidence: number;
  success: boolean;
  error?: string;
}

/**
 * Extract text from an image using Google Vision API
 * @param imagePath - Path to the image file
 * @returns OCR result with extracted text and confidence
 */
export async function extractTextFromImage(imagePath: string): Promise<OCRResult> {
  try {
    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      return {
        text: '',
        confidence: 0,
        success: false,
        error: 'Image file not found'
      };
    }

    console.log(`Starting Google Vision OCR for: ${imagePath}`);

    const client = getVisionClient();

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // Perform text detection
    const [result] = await client.textDetection({
      image: {
        content: imageBuffer
      }
    });

    const detections = result.textAnnotations;

    if (!detections || detections.length === 0) {
      console.log('No text detected in image');
      return {
        text: '',
        confidence: 0,
        success: true,
        error: 'No text found in image'
      };
    }

    // The first annotation contains the full text
    const fullText = detections[0].description || '';
    
    // Calculate average confidence from all detections
    let totalConfidence = 0;
    let confidenceCount = 0;
    
    detections.forEach(detection => {
      if (detection.confidence !== undefined && detection.confidence !== null) {
        totalConfidence += detection.confidence;
        confidenceCount++;
      }
    });

    const averageConfidence = confidenceCount > 0 ? totalConfidence / confidenceCount : 0.9; // Default high confidence

    console.log(`Google Vision OCR completed. Text length: ${fullText.length}, Confidence: ${(averageConfidence * 100).toFixed(1)}%`);

    return {
      text: fullText.trim(),
      confidence: averageConfidence,
      success: true
    };

  } catch (error) {
    console.error('Google Vision OCR error:', error);
    
    return {
      text: '',
      confidence: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown OCR error'
    };
  }
}

/**
 * Check if Google Vision API is properly configured
 * @returns true if API is available, false otherwise
 */
export async function isVisionAPIAvailable(): Promise<boolean> {
  try {
    const client = getVisionClient();
    
    // Try a simple operation to test connectivity
    // We'll use a minimal request to check if the API is accessible
    await client.getProjectId();
    return true;
  } catch (error) {
    console.log('Google Vision API not available:', error);
    return false;
  }
}

/**
 * Get usage information for Google Vision API
 * @returns Information about API usage and limits
 */
export function getVisionAPIInfo() {
  return {
    provider: 'Google Cloud Vision API',
    features: [
      'High-accuracy OCR',
      'Handwriting recognition',
      'Multi-language support',
      'Document structure detection'
    ],
    pricing: {
      free_tier: '1,000 requests per month',
      paid_tier: '$1.50 per 1,000 requests (after free tier)',
      currency: 'USD'
    },
    setup_required: 'Google Cloud project with Vision API enabled and authentication configured'
  };
}
