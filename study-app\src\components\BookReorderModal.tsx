'use client';

import { useState } from 'react';

interface BookImage {
  id: number;
  original_name: string;
  page_number: number;
  page_type: string;
}

interface BookReorderModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: BookImage[];
  onSave: (newOrder: BookImage[]) => Promise<void>;
}

export default function BookReorderModal({
  isOpen,
  onClose,
  images,
  onSave
}: BookReorderModalProps) {
  const [reorderedImages, setReorderedImages] = useState<BookImage[]>(images);
  const [isSaving, setIsSaving] = useState(false);

  if (!isOpen) return null;

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= reorderedImages.length) return;
    
    const newImages = [...reorderedImages];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    setReorderedImages(newImages);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(reorderedImages);
      onClose();
    } catch (error) {
      console.error('Failed to save reorder:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setReorderedImages([...images]);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            Reorder Pages ({reorderedImages.length} images)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <span className="sr-only">Close</span>
            <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="mb-4 text-sm text-gray-600">
            <p>Use the arrow buttons to reorder pages. The first page will become page 1, second will become page 2, etc.</p>
          </div>

          <div className="space-y-2">
            {reorderedImages.map((image, index) => (
              <div
                key={image.id}
                className="flex items-center space-x-3 p-3 border rounded-lg bg-gray-50"
              >
                {/* Position Controls */}
                <div className="flex flex-col space-y-1">
                  <button
                    onClick={() => moveImage(index, index - 1)}
                    disabled={index === 0}
                    className="w-8 h-8 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    title="Move up"
                  >
                    ↑
                  </button>
                  <button
                    onClick={() => moveImage(index, index + 1)}
                    disabled={index === reorderedImages.length - 1}
                    className="w-8 h-8 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    title="Move down"
                  >
                    ↓
                  </button>
                </div>

                {/* New Position */}
                <div className="flex-shrink-0 w-12 text-center">
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded">
                    {index + 1}
                  </span>
                </div>

                {/* Image Preview */}
                <div className="flex-shrink-0">
                  <img
                    src={`/api/admin/images/${image.id}/preview`}
                    alt={image.original_name}
                    className="w-16 h-20 object-cover rounded"
                  />
                </div>

                {/* Image Info */}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {image.original_name}
                  </div>
                  <div className="text-xs text-gray-500">
                    Original Page: {image.page_number}
                  </div>
                  <div className="text-xs text-gray-500">
                    Type: {image.page_type}
                  </div>
                </div>

                {/* Quick Move Controls */}
                <div className="flex flex-col space-y-1">
                  <button
                    onClick={() => moveImage(index, 0)}
                    disabled={index === 0}
                    className="px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Move to first"
                  >
                    First
                  </button>
                  <button
                    onClick={() => moveImage(index, reorderedImages.length - 1)}
                    disabled={index === reorderedImages.length - 1}
                    className="px-2 py-1 bg-gray-200 text-gray-700 text-xs rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Move to last"
                  >
                    Last
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Reset to Original
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? 'Saving...' : 'Save New Order'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
