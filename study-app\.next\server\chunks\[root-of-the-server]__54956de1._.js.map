{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/database.ts"], "sourcesContent": ["import Database from 'better-sqlite3';\nimport path from 'path';\nimport fs from 'fs';\n\n// Database file path\nconst dbPath = path.join(process.cwd(), 'data', 'study_app.db');\n\n// Ensure data directory exists\nconst dataDir = path.dirname(dbPath);\nif (!fs.existsSync(dataDir)) {\n  fs.mkdirSync(dataDir, { recursive: true });\n}\n\n// Initialize database\nconst db = new Database(dbPath);\n\n// Enable foreign keys\ndb.pragma('foreign_keys = ON');\n\n// Database migration function\nexport function migrateDatabase() {\n  try {\n    // Check if new columns exist, if not add them\n    const tableInfo = db.prepare(\"PRAGMA table_info(images)\").all() as any[];\n    const hasPageType = tableInfo.some(col => col.name === 'page_type');\n    const hasUploadOrder = tableInfo.some(col => col.name === 'upload_order');\n\n    if (!hasPageType) {\n      db.exec(\"ALTER TABLE images ADD COLUMN page_type TEXT DEFAULT 'unassigned'\");\n      console.log('Added page_type column to images table');\n    }\n\n    if (!hasUploadOrder) {\n      db.exec(\"ALTER TABLE images ADD COLUMN upload_order INTEGER\");\n      // Set upload order for existing images based on uploaded_at\n      db.exec(`\n        UPDATE images\n        SET upload_order = (\n          SELECT COUNT(*) + 1\n          FROM images i2\n          WHERE i2.class_id = images.class_id\n          AND i2.subject_id = images.subject_id\n          AND i2.uploaded_at < images.uploaded_at\n        )\n      `);\n      console.log('Added upload_order column to images table');\n    }\n\n    // Check if processed column exists in ocr_text table\n    const ocrTableInfo = db.prepare(\"PRAGMA table_info(ocr_text)\").all() as any[];\n    const hasProcessed = ocrTableInfo.some(col => col.name === 'processed');\n\n    if (!hasProcessed) {\n      db.exec(\"ALTER TABLE ocr_text ADD COLUMN processed INTEGER DEFAULT 1\");\n      console.log('Added processed column to ocr_text table');\n    }\n\n    // Check if books table exists\n    const tablesResult = db.prepare(\"SELECT name FROM sqlite_master WHERE type='table' AND name='books'\").get();\n    if (!tablesResult) {\n      console.log('Creating books table...');\n      db.exec(`\n        CREATE TABLE books (\n          id INTEGER PRIMARY KEY AUTOINCREMENT,\n          title TEXT NOT NULL,\n          class_id INTEGER NOT NULL,\n          subject_id INTEGER NOT NULL,\n          description TEXT,\n          cover_image_path TEXT,\n          total_pages INTEGER DEFAULT 0,\n          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n          FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n          FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n        )\n      `);\n      console.log('Books table created successfully');\n    }\n\n    // Check if images table has book_id column\n    const hasBookId = tableInfo.some(col => col.name === 'book_id');\n    if (!hasBookId) {\n      console.log('Adding book_id column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN book_id INTEGER\");\n      console.log('Added book_id column to images table');\n    }\n\n    // Check if images table has page_number column\n    const hasPageNumber = tableInfo.some(col => col.name === 'page_number');\n    if (!hasPageNumber) {\n      console.log('Adding page_number column to images table...');\n      db.exec(\"ALTER TABLE images ADD COLUMN page_number INTEGER\");\n      console.log('Added page_number column to images table');\n    }\n\n  } catch (error) {\n    console.error('Migration error:', error);\n  }\n}\n\n// Database schema initialization\nexport function initializeDatabase() {\n  // Users table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS users (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL,\n      email TEXT UNIQUE NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('admin', 'student')),\n      password_hash TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Classes table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS classes (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    )\n  `);\n\n  // Subjects table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS subjects (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      UNIQUE(class_id, name)\n    )\n  `);\n\n  // Books table - containers for textbook content\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS books (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      description TEXT,\n      cover_image_path TEXT,\n      total_pages INTEGER DEFAULT 0,\n      status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'processing', 'completed')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Images table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS images (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      file_path TEXT NOT NULL,\n      original_name TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      book_id INTEGER,\n      page_number INTEGER,\n      page_type TEXT DEFAULT 'unassigned' CHECK (page_type IN ('cover', 'contents', 'chapter-1', 'chapter-2', 'chapter-3', 'chapter-4', 'chapter-5', 'chapter-6', 'chapter-7', 'chapter-8', 'chapter-9', 'chapter-10', 'chapter-11', 'chapter-12', 'chapter-13', 'chapter-14', 'chapter-15', 'chapter-16', 'chapter-17', 'chapter-18', 'chapter-19', 'chapter-20', 'chapter-21', 'chapter-22', 'chapter-23', 'chapter-24', 'chapter-25', 'chapter-26', 'chapter-27', 'chapter-28', 'chapter-29', 'chapter-30', 'unassigned')),\n      upload_order INTEGER,\n      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE\n    )\n  `);\n\n  // OCR text table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS ocr_text (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      image_id INTEGER NOT NULL,\n      content TEXT NOT NULL,\n      processed BOOLEAN DEFAULT FALSE,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Questions table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapter TEXT,\n      type TEXT NOT NULL CHECK (type IN ('mcq', 'true_false', 'fill_blank', 'short_answer', 'long_answer')),\n      content TEXT NOT NULL,\n      options TEXT, -- JSON string for MCQ options\n      correct_answer TEXT,\n      marks INTEGER DEFAULT 1,\n      difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE\n    )\n  `);\n\n  // Tests table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS tests (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      class_id INTEGER NOT NULL,\n      subject_id INTEGER NOT NULL,\n      chapters TEXT, -- JSON string of selected chapters\n      time_min INTEGER NOT NULL, -- Time limit in minutes\n      total_marks INTEGER DEFAULT 0,\n      instructions TEXT,\n      is_active BOOLEAN DEFAULT TRUE,\n      created_by INTEGER NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,\n      FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,\n      FOREIGN KEY (created_by) REFERENCES users(id)\n    )\n  `);\n\n  // Test questions junction table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_questions (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      question_order INTEGER NOT NULL,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      UNIQUE(test_id, question_id),\n      UNIQUE(test_id, question_order)\n    )\n  `);\n\n  // Test results table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_results (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      test_id INTEGER NOT NULL,\n      user_id INTEGER NOT NULL,\n      started_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      submitted_at DATETIME,\n      total_score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      time_taken INTEGER, -- Time taken in minutes\n      status TEXT DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'submitted', 'graded')),\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (test_id) REFERENCES tests(id) ON DELETE CASCADE,\n      FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(test_id, user_id)\n    )\n  `);\n\n  // Test answers table\n  db.exec(`\n    CREATE TABLE IF NOT EXISTS test_answers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      result_id INTEGER NOT NULL,\n      question_id INTEGER NOT NULL,\n      user_answer TEXT,\n      is_correct BOOLEAN,\n      score REAL DEFAULT 0,\n      max_score REAL DEFAULT 0,\n      graded_by INTEGER,\n      graded_at DATETIME,\n      FOREIGN KEY (result_id) REFERENCES test_results(id) ON DELETE CASCADE,\n      FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id),\n      UNIQUE(result_id, question_id)\n    )\n  `);\n\n  // Create indexes for better performance\n  db.exec(`\n    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);\n    CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);\n    CREATE INDEX IF NOT EXISTS idx_subjects_class ON subjects(class_id);\n    CREATE INDEX IF NOT EXISTS idx_images_class_subject ON images(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_class_subject ON questions(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_questions_type ON questions(type);\n    CREATE INDEX IF NOT EXISTS idx_tests_class_subject ON tests(class_id, subject_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_user ON test_results(user_id);\n    CREATE INDEX IF NOT EXISTS idx_test_results_test ON test_results(test_id);\n  `);\n\n  console.log('Database initialized successfully');\n\n  // Run migrations after initialization\n  migrateDatabase();\n}\n\n// Create default admin user if none exists\nexport function createDefaultAdmin() {\n  const bcrypt = require('bcryptjs');\n\n  const adminExists = db.prepare('SELECT COUNT(*) as count FROM users WHERE role = ?').get('admin');\n\n  if (adminExists.count === 0) {\n    const hashedPassword = bcrypt.hashSync('admin123', 10);\n\n    db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run('Administrator', '<EMAIL>', 'admin', hashedPassword);\n\n    console.log('Default admin user created: <EMAIL> / admin123');\n  }\n}\n\n// Clear OCR data to force re-processing with real OCR\nexport function clearOCRData() {\n  try {\n    console.log('Clearing existing OCR data to enable fresh processing...');\n    db.exec('DELETE FROM ocr_text');\n    db.exec('DELETE FROM questions');\n    console.log('OCR data cleared successfully');\n  } catch (error) {\n    console.error('Error clearing OCR data:', error);\n  }\n}\n\n// Initialize database on import\ninitializeDatabase();\ncreateDefaultAdmin();\n\nexport default db;\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAEA,qBAAqB;AACrB,MAAM,SAAS,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAEhD,+BAA+B;AAC/B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,UAAU;IAC3B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS;QAAE,WAAW;IAAK;AAC1C;AAEA,sBAAsB;AACtB,MAAM,KAAK,IAAI,2HAAA,CAAA,UAAQ,CAAC;AAExB,sBAAsB;AACtB,GAAG,MAAM,CAAC;AAGH,SAAS;IACd,IAAI;QACF,8CAA8C;QAC9C,MAAM,YAAY,GAAG,OAAO,CAAC,6BAA6B,GAAG;QAC7D,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACvD,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE1D,IAAI,CAAC,aAAa;YAChB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,gBAAgB;YACnB,GAAG,IAAI,CAAC;YACR,4DAA4D;YAC5D,GAAG,IAAI,CAAC,CAAC;;;;;;;;;MAST,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,qDAAqD;QACrD,MAAM,eAAe,GAAG,OAAO,CAAC,+BAA+B,GAAG;QAClE,MAAM,eAAe,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QAE3D,IAAI,CAAC,cAAc;YACjB,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,8BAA8B;QAC9B,MAAM,eAAe,GAAG,OAAO,CAAC,sEAAsE,GAAG;QACzG,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;MAeT,CAAC;YACD,QAAQ,GAAG,CAAC;QACd;QAEA,2CAA2C;QAC3C,MAAM,YAAY,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACrD,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;QAEA,+CAA+C;QAC/C,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACzD,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC;YACZ,GAAG,IAAI,CAAC;YACR,QAAQ,GAAG,CAAC;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;IACpC;AACF;AAGO,SAAS;IACd,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gBAAgB;IAChB,GAAG,IAAI,CAAC,CAAC;;;;;;;EAOT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,gDAAgD;IAChD,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;EAeT,CAAC;IAED,eAAe;IACf,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,iBAAiB;IACjB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;EAST,CAAC;IAED,kBAAkB;IAClB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,cAAc;IACd,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;EAiBT,CAAC;IAED,gCAAgC;IAChC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;EAWT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;;;EAkBT,CAAC;IAED,qBAAqB;IACrB,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;;;;;;;EAgBT,CAAC;IAED,wCAAwC;IACxC,GAAG,IAAI,CAAC,CAAC;;;;;;;;;;EAUT,CAAC;IAED,QAAQ,GAAG,CAAC;IAEZ,sCAAsC;IACtC;AACF;AAGO,SAAS;IACd,MAAM;IAEN,MAAM,cAAc,GAAG,OAAO,CAAC,sDAAsD,GAAG,CAAC;IAEzF,IAAI,YAAY,KAAK,KAAK,GAAG;QAC3B,MAAM,iBAAiB,OAAO,QAAQ,CAAC,YAAY;QAEnD,GAAG,OAAO,CAAC,CAAC;;;IAGZ,CAAC,EAAE,GAAG,CAAC,iBAAiB,sBAAsB,SAAS;QAEvD,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,SAAS;IACd,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC;QACR,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;AACF;AAEA,gCAAgC;AAChC;AACA;uCAEe", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport { SignJWT, jwtVerify } from 'jose';\nimport { cookies } from 'next/headers';\nimport db from './database';\n\nconst JWT_SECRET = new TextEncoder().encode(\n  process.env.JWT_SECRET || 'your-secret-key-change-this-in-production'\n);\n\nexport interface User {\n  id: number;\n  name: string;\n  email: string;\n  role: 'admin' | 'student';\n  created_at: string;\n}\n\nexport interface AuthResult {\n  success: boolean;\n  user?: User;\n  error?: string;\n}\n\n// Hash password\nexport function hashPassword(password: string): string {\n  return bcrypt.hashSync(password, 10);\n}\n\n// Verify password\nexport function verifyPassword(password: string, hash: string): boolean {\n  return bcrypt.compareSync(password, hash);\n}\n\n// Create JWT token\nexport async function createToken(user: User): Promise<string> {\n  return await new SignJWT({ \n    userId: user.id, \n    email: user.email, \n    role: user.role \n  })\n    .setProtectedHeader({ alg: 'HS256' })\n    .setIssuedAt()\n    .setExpirationTime('24h')\n    .sign(JWT_SECRET);\n}\n\n// Verify JWT token\nexport async function verifyToken(token: string): Promise<any> {\n  try {\n    const { payload } = await jwtVerify(token, JWT_SECRET);\n    return payload;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Login user\nexport async function loginUser(email: string, password: string): Promise<AuthResult> {\n  try {\n    const user = db.prepare('SELECT * FROM users WHERE email = ?').get(email) as any;\n    \n    if (!user) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    if (!verifyPassword(password, user.password_hash)) {\n      return { success: false, error: 'Invalid email or password' };\n    }\n\n    const userWithoutPassword = {\n      id: user.id,\n      name: user.name,\n      email: user.email,\n      role: user.role,\n      created_at: user.created_at\n    };\n\n    return { success: true, user: userWithoutPassword };\n  } catch (error) {\n    console.error('Login error:', error);\n    return { success: false, error: 'Login failed' };\n  }\n}\n\n// Get current user from cookies\nexport async function getCurrentUser(): Promise<User | null> {\n  try {\n    const cookieStore = await cookies();\n    const token = cookieStore.get('auth-token')?.value;\n    \n    if (!token) {\n      return null;\n    }\n\n    const payload = await verifyToken(token);\n    if (!payload) {\n      return null;\n    }\n\n    const user = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(payload.userId) as User;\n\n    return user || null;\n  } catch (error) {\n    console.error('Get current user error:', error);\n    return null;\n  }\n}\n\n// Set auth cookie\nexport async function setAuthCookie(user: User) {\n  const token = await createToken(user);\n  const cookieStore = await cookies();\n  \n  cookieStore.set('auth-token', token, {\n    httpOnly: true,\n    secure: process.env.NODE_ENV === 'production',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 // 24 hours\n  });\n}\n\n// Clear auth cookie\nexport async function clearAuthCookie() {\n  const cookieStore = await cookies();\n  cookieStore.delete('auth-token');\n}\n\n// Register new user (admin only)\nexport function registerUser(name: string, email: string, password: string, role: 'admin' | 'student'): AuthResult {\n  try {\n    // Check if user already exists\n    const existingUser = db.prepare('SELECT id FROM users WHERE email = ?').get(email);\n    if (existingUser) {\n      return { success: false, error: 'User with this email already exists' };\n    }\n\n    const hashedPassword = hashPassword(password);\n    \n    const result = db.prepare(`\n      INSERT INTO users (name, email, role, password_hash)\n      VALUES (?, ?, ?, ?)\n    `).run(name, email, role, hashedPassword);\n\n    const newUser = db.prepare('SELECT id, name, email, role, created_at FROM users WHERE id = ?')\n      .get(result.lastInsertRowid) as User;\n\n    return { success: true, user: newUser };\n  } catch (error) {\n    console.error('Registration error:', error);\n    return { success: false, error: 'Registration failed' };\n  }\n}\n\n// Middleware to check if user is authenticated\nexport async function requireAuth(): Promise<User | null> {\n  const user = await getCurrentUser();\n  return user;\n}\n\n// Middleware to check if user is admin\nexport async function requireAdmin(): Promise<User | null> {\n  const user = await getCurrentUser();\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  return user;\n}\n\n// Get all users (admin only)\nexport function getAllUsers(): User[] {\n  return db.prepare('SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC').all() as User[];\n}\n\n// Update user\nexport function updateUser(id: number, updates: Partial<Pick<User, 'name' | 'email' | 'role'>>): boolean {\n  try {\n    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');\n    const values = Object.values(updates);\n    \n    db.prepare(`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`)\n      .run(...values, id);\n    \n    return true;\n  } catch (error) {\n    console.error('Update user error:', error);\n    return false;\n  }\n}\n\n// Delete user\nexport function deleteUser(id: number): boolean {\n  try {\n    db.prepare('DELETE FROM users WHERE id = ?').run(id);\n    return true;\n  } catch (error) {\n    console.error('Delete user error:', error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,UAAU,IAAI;AAkBrB,SAAS,aAAa,QAAgB;IAC3C,OAAO,mIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;AACnC;AAGO,SAAS,eAAe,QAAgB,EAAE,IAAY;IAC3D,OAAO,mIAAA,CAAA,UAAM,CAAC,WAAW,CAAC,UAAU;AACtC;AAGO,eAAe,YAAY,IAAU;IAC1C,OAAO,MAAM,IAAI,uJAAA,CAAA,UAAO,CAAC;QACvB,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;IACjB,GACG,kBAAkB,CAAC;QAAE,KAAK;IAAQ,GAClC,WAAW,GACX,iBAAiB,CAAC,OAClB,IAAI,CAAC;AACV;AAGO,eAAe,YAAY,KAAa;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,yJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,eAAe,UAAU,KAAa,EAAE,QAAgB;IAC7D,IAAI;QACF,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,uCAAuC,GAAG,CAAC;QAEnE,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,IAAI,CAAC,eAAe,UAAU,KAAK,aAAa,GAAG;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,MAAM,sBAAsB;YAC1B,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM;QAAoB;IACpD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAChC,MAAM,QAAQ,YAAY,GAAG,CAAC,eAAe;QAE7C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,MAAM,UAAU,MAAM,YAAY;QAClC,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QAEA,MAAM,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACrB,GAAG,CAAC,QAAQ,MAAM;QAErB,OAAO,QAAQ;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,eAAe,cAAc,IAAU;IAC5C,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,YAAY,GAAG,CAAC,cAAc,OAAO;QACnC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,QAAQ,KAAK,KAAK,GAAG,WAAW;IAClC;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,MAAM,CAAC;AACrB;AAGO,SAAS,aAAa,IAAY,EAAE,KAAa,EAAE,QAAgB,EAAE,IAAyB;IACnG,IAAI;QACF,+BAA+B;QAC/B,MAAM,eAAe,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,wCAAwC,GAAG,CAAC;QAC5E,IAAI,cAAc;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAsC;QACxE;QAEA,MAAM,iBAAiB,aAAa;QAEpC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,MAAM,OAAO,MAAM;QAE1B,MAAM,UAAU,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oEACxB,GAAG,CAAC,OAAO,eAAe;QAE7B,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;AACF;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IACnB,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,SAAS;QAClC,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gFAAgF,GAAG;AACvG;AAGO,SAAS,WAAW,EAAU,EAAE,OAAuD;IAC5F,IAAI;QACF,MAAM,YAAY,OAAO,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC;QACrE,MAAM,SAAS,OAAO,MAAM,CAAC;QAE7B,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,6CAA6C,CAAC,EACpF,GAAG,IAAI,QAAQ;QAElB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF;AAGO,SAAS,WAAW,EAAU;IACnC,IAAI;QACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,kCAAkC,GAAG,CAAC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/lib/upload.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { createWorker } from 'tesseract.js';\nimport db from './database';\n\n// Generate realistic mock OCR content for testing\nfunction generateMockOCRContent(filename: string, imageId: number): string {\n  const pageNumber = filename.match(/(\\d+)/)?.[1] || imageId.toString();\n\n  const educationalContent = [\n    // Math content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Mathematical Concepts\n\nProblem ${pageNumber}: Solve for x in the equation 2x + 5 = 15\n\nSolution:\n2x + 5 = 15\n2x = 15 - 5\n2x = 10\nx = 5\n\nTherefore, x = 5.\n\nPractice Problems:\n1. If 3x - 7 = 14, find the value of x.\n2. Solve: 4(x + 2) = 20\n3. What is the value of y if 2y + 3 = 11?\n\nKey Concepts:\n- Linear equations\n- Algebraic manipulation\n- Variable isolation`,\n\n    // Science content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Scientific Principles\n\nLesson ${pageNumber}: The Water Cycle\n\nThe water cycle is a continuous process that describes how water moves through Earth's systems.\n\nMain Stages:\n1. Evaporation - Water from oceans, lakes, and rivers turns into water vapor\n2. Condensation - Water vapor cools and forms clouds\n3. Precipitation - Water falls as rain, snow, or hail\n4. Collection - Water gathers in bodies of water\n\nImportant Facts:\n• 97% of Earth's water is in the oceans\n• Only 3% is fresh water\n• The sun provides energy for the water cycle\n\nQuestions for Review:\n1. What causes water to evaporate?\n2. How do clouds form?\n3. Name three types of precipitation.`,\n\n    // History content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Historical Events\n\nSection ${pageNumber}: Ancient Civilizations\n\nThe ancient civilizations laid the foundation for modern society through their innovations and cultural contributions.\n\nKey Civilizations:\n• Mesopotamia (3500-539 BCE)\n  - Invented writing (cuneiform)\n  - Developed the wheel\n  - Created the first cities\n\n• Ancient Egypt (3100-30 BCE)\n  - Built pyramids and monuments\n  - Developed hieroglyphic writing\n  - Advanced medicine and mathematics\n\n• Ancient Greece (800-146 BCE)\n  - Democracy and philosophy\n  - Olympic Games\n  - Scientific method\n\nTimeline:\n3500 BCE - First cities in Mesopotamia\n3100 BCE - Unification of Egypt\n776 BCE - First Olympic Games\n509 BCE - Roman Republic established`,\n\n    // Language Arts content\n    `Chapter ${Math.ceil(parseInt(pageNumber) / 3)}: Literature and Writing\n\nLesson ${pageNumber}: Elements of Poetry\n\nPoetry is a form of literary expression that uses rhythm, rhyme, and imagery to convey emotions and ideas.\n\nTypes of Poetry:\n1. Haiku - Traditional Japanese form (5-7-5 syllables)\n2. Sonnet - 14-line poem with specific rhyme scheme\n3. Free Verse - No regular pattern or rhyme\n4. Limerick - Humorous 5-line poem\n\nLiterary Devices:\n• Metaphor - Direct comparison without \"like\" or \"as\"\n• Simile - Comparison using \"like\" or \"as\"\n• Alliteration - Repetition of initial consonant sounds\n• Personification - Giving human qualities to non-human things\n\nExample Haiku:\nCherry blossoms fall\nGentle breeze carries petals\nSpring's beauty fades fast\n\nWriting Exercise:\nCreate your own haiku about nature.`\n  ];\n\n  // Select content based on page number\n  const contentIndex = (parseInt(pageNumber) - 1) % educationalContent.length;\n  return educationalContent[contentIndex];\n}\n\n// Ensure uploads directory exists\nexport function ensureUploadDir(classId: number, subjectId: number): string {\n  const className = db.prepare('SELECT name FROM classes WHERE id = ?').get(classId) as any;\n  const subjectName = db.prepare('SELECT name FROM subjects WHERE id = ?').get(subjectId) as any;\n  \n  if (!className || !subjectName) {\n    throw new Error('Invalid class or subject ID');\n  }\n\n  const uploadDir = path.join(process.cwd(), 'uploads', className.name, subjectName.name);\n  \n  if (!fs.existsSync(uploadDir)) {\n    fs.mkdirSync(uploadDir, { recursive: true });\n  }\n  \n  return uploadDir;\n}\n\n// Save uploaded file\nexport async function saveUploadedFile(\n  file: File,\n  classId: number,\n  subjectId: number,\n  pageType: string = 'unassigned'\n): Promise<{ id: number; filePath: string }> {\n  try {\n    const uploadDir = ensureUploadDir(classId, subjectId);\n\n    // Generate unique filename\n    const timestamp = Date.now();\n    const extension = path.extname(file.name);\n    const filename = `${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;\n    const filePath = path.join(uploadDir, filename);\n\n    // Save file to disk\n    const buffer = Buffer.from(await file.arrayBuffer());\n    fs.writeFileSync(filePath, buffer);\n\n    // Get the next upload order for this class/subject\n    const maxOrder = db.prepare(`\n      SELECT MAX(upload_order) as max_order\n      FROM images\n      WHERE class_id = ? AND subject_id = ?\n    `).get(classId, subjectId) as any;\n\n    const uploadOrder = (maxOrder?.max_order || 0) + 1;\n\n    // Save file info to database\n    const result = db.prepare(`\n      INSERT INTO images (file_path, original_name, class_id, subject_id, page_type, upload_order)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `).run(filePath, file.name, classId, subjectId, pageType, uploadOrder);\n\n    return {\n      id: result.lastInsertRowid as number,\n      filePath: filePath\n    };\n  } catch (error) {\n    console.error('File upload error:', error);\n    throw new Error('Failed to save uploaded file');\n  }\n}\n\n// Process OCR on uploaded image\nexport async function processOCR(imageId: number): Promise<string> {\n  try {\n    const image = db.prepare('SELECT file_path, original_name FROM images WHERE id = ?').get(imageId) as any;\n\n    if (!image) {\n      throw new Error('Image not found');\n    }\n\n    // Check if OCR already exists\n    const existingOCR = db.prepare('SELECT content FROM ocr_text WHERE image_id = ?').get(imageId) as any;\n    if (existingOCR) {\n      console.log(`OCR already exists for image ID: ${imageId}`);\n      return existingOCR.content;\n    }\n\n    // Check if file exists\n    if (!fs.existsSync(image.file_path)) {\n      throw new Error('Image file not found on disk');\n    }\n\n    console.log(`Starting OCR processing for image ID: ${imageId}, file: ${image.file_path}`);\n\n    try {\n      // For now, use mock OCR that generates realistic educational content\n      console.log(`Generating mock OCR content for image ID: ${imageId}`);\n\n      const mockOCRContent = generateMockOCRContent(image.original_name, imageId);\n\n      console.log(`Mock OCR text generated for image ID: ${imageId}, length: ${mockOCRContent.length} characters`);\n\n      // Save OCR text to database\n      db.prepare(`\n        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n        VALUES (?, ?, 1)\n      `).run(imageId, mockOCRContent);\n\n      console.log(`OCR completed and saved for image ID: ${imageId}`);\n      return mockOCRContent;\n    } catch (mockError) {\n      console.log(`Mock OCR failed for image ${imageId}, using fallback:`, mockError);\n\n      // Fallback: Return a clear message that real OCR needs to be configured\n      const fallbackText = `[Real OCR Not Available]\n\nMock OCR is currently being used for demonstration purposes.\n\nImage: ${image.original_name}\nImage ID: ${imageId}\n\nTo enable real OCR processing:\n1. Configure Tesseract.js properly for Next.js environment\n2. Or integrate with cloud OCR services (Google Vision, AWS Textract, etc.)\n\nCurrent Error: ${mockError instanceof Error ? mockError.message : 'Unknown error'}`;\n\n      // Save fallback text to database with processed = 0 to indicate it needs real processing\n      db.prepare(`\n        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n        VALUES (?, ?, 0)\n      `).run(imageId, fallbackText);\n\n      console.log(`Fallback message saved for image ID: ${imageId}`);\n      return fallbackText;\n    }\n  } catch (error) {\n    console.error(`OCR processing error for image ${imageId}:`, error);\n\n    // Save error state to database\n    try {\n      db.prepare(`\n        INSERT OR REPLACE INTO ocr_text (image_id, content, processed)\n        VALUES (?, ?, 0)\n      `).run(imageId, `OCR Error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    } catch (dbError) {\n      console.error('Failed to save OCR error to database:', dbError);\n    }\n\n    throw new Error(`Failed to process OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n\n\n// Get OCR text for an image\nexport function getOCRText(imageId: number): string | null {\n  const result = db.prepare('SELECT content FROM ocr_text WHERE image_id = ? ORDER BY created_at DESC LIMIT 1')\n    .get(imageId) as any;\n  \n  return result ? result.content : null;\n}\n\n// Get all images for a class/subject\nexport function getImages(classId?: number, subjectId?: number) {\n  let query = `\n    SELECT i.*, c.name as class_name, s.name as subject_name,\n           ocr.content as ocr_content, ocr.processed\n    FROM images i\n    JOIN classes c ON i.class_id = c.id\n    JOIN subjects s ON i.subject_id = s.id\n    LEFT JOIN ocr_text ocr ON i.id = ocr.image_id\n  `;\n\n  const params: any[] = [];\n  const conditions: string[] = [];\n\n  if (classId) {\n    conditions.push('i.class_id = ?');\n    params.push(classId);\n  }\n\n  if (subjectId) {\n    conditions.push('i.subject_id = ?');\n    params.push(subjectId);\n  }\n\n  if (conditions.length > 0) {\n    query += ' WHERE ' + conditions.join(' AND ');\n  }\n\n  query += ' ORDER BY COALESCE(i.upload_order, 999999) ASC, i.uploaded_at ASC';\n\n  return db.prepare(query).all(...params);\n}\n\n// Delete image and its OCR data\nexport function deleteImage(imageId: number): boolean {\n  try {\n    const image = db.prepare('SELECT file_path FROM images WHERE id = ?').get(imageId) as any;\n    \n    if (image && fs.existsSync(image.file_path)) {\n      fs.unlinkSync(image.file_path);\n    }\n    \n    // Delete from database (OCR text will be deleted by foreign key cascade)\n    db.prepare('DELETE FROM images WHERE id = ?').run(imageId);\n    \n    return true;\n  } catch (error) {\n    console.error('Delete image error:', error);\n    return false;\n  }\n}\n\n// Parse questions from OCR text with chapter context\nexport function parseQuestionsFromOCR(ocrText: string, imageId?: number): Array<{\n  type: string;\n  content: string;\n  options?: string[];\n  correctAnswer?: string;\n  chapter?: string;\n}> {\n  const questions: Array<{\n    type: string;\n    content: string;\n    options?: string[];\n    correctAnswer?: string;\n    chapter?: string;\n  }> = [];\n\n  // Get chapter context from image if provided\n  let chapterContext = '';\n  if (imageId) {\n    const image = db.prepare('SELECT page_type FROM images WHERE id = ?').get(imageId) as any;\n    if (image && image.page_type && image.page_type.startsWith('chapter-')) {\n      chapterContext = image.page_type.replace('chapter-', 'Chapter ');\n    }\n  }\n\n  // Split text into lines and clean up\n  const lines = ocrText.split('\\n').map(line => line.trim()).filter(line => line.length > 0);\n  \n  let currentQuestion = '';\n  let currentOptions: string[] = [];\n  let questionType = 'short_answer'; // default\n  \n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    \n    // Detect question patterns\n    if (line.match(/^\\d+[\\.\\)]\\s*/) || line.match(/^Q\\d*[\\.\\)]\\s*/i)) {\n      // Save previous question if exists\n      if (currentQuestion) {\n        questions.push({\n          type: questionType,\n          content: currentQuestion,\n          options: currentOptions.length > 0 ? currentOptions : undefined,\n          chapter: chapterContext\n        });\n      }\n      \n      // Start new question\n      currentQuestion = line.replace(/^\\d+[\\.\\)]\\s*/, '').replace(/^Q\\d*[\\.\\)]\\s*/i, '');\n      currentOptions = [];\n      \n      // Detect question type\n      if (currentQuestion.toLowerCase().includes('true') && currentQuestion.toLowerCase().includes('false')) {\n        questionType = 'true_false';\n      } else if (currentQuestion.includes('_____') || currentQuestion.includes('____')) {\n        questionType = 'fill_blank';\n      } else {\n        questionType = 'short_answer';\n      }\n    }\n    // Detect MCQ options\n    else if (line.match(/^[a-d][\\.\\)]\\s*/i) || line.match(/^\\([a-d]\\)\\s*/i)) {\n      if (currentQuestion) {\n        questionType = 'mcq';\n        const option = line.replace(/^[a-d][\\.\\)]\\s*/i, '').replace(/^\\([a-d]\\)\\s*/i, '');\n        currentOptions.push(option);\n      }\n    }\n    // Continue current question\n    else if (currentQuestion && !line.match(/^[a-d][\\.\\)]\\s*/i)) {\n      currentQuestion += ' ' + line;\n    }\n  }\n  \n  // Save last question\n  if (currentQuestion) {\n    questions.push({\n      type: questionType,\n      content: currentQuestion,\n      options: currentOptions.length > 0 ? currentOptions : undefined,\n      chapter: chapterContext\n    });\n  }\n  \n  return questions;\n}\n\n// Update page type for an image\nexport function updateImagePageType(imageId: number, pageType: string): boolean {\n  try {\n    const validPageTypes = [\n      'cover', 'contents', 'unassigned',\n      ...Array.from({length: 30}, (_, i) => `chapter-${i + 1}`)\n    ];\n\n    if (!validPageTypes.includes(pageType)) {\n      throw new Error('Invalid page type');\n    }\n\n    db.prepare('UPDATE images SET page_type = ? WHERE id = ?').run(pageType, imageId);\n    return true;\n  } catch (error) {\n    console.error('Update page type error:', error);\n    return false;\n  }\n}\n\n// Auto-assign page types based on upload order and existing assignments\nexport function autoAssignPageTypes(classId: number, subjectId: number): void {\n  try {\n    const images = db.prepare(`\n      SELECT id, upload_order, page_type\n      FROM images\n      WHERE class_id = ? AND subject_id = ?\n      ORDER BY upload_order ASC\n    `).all(classId, subjectId) as any[];\n\n    let currentChapter = 1;\n    let foundFirstChapter = false;\n\n    for (const image of images) {\n      if (image.page_type !== 'unassigned') {\n        // If this is a chapter marker, update current chapter\n        const chapterMatch = image.page_type.match(/^chapter-(\\d+)$/);\n        if (chapterMatch) {\n          currentChapter = parseInt(chapterMatch[1]);\n          foundFirstChapter = true;\n        }\n        continue;\n      }\n\n      // Auto-assign based on position\n      if (!foundFirstChapter) {\n        // Before any chapter is found, assume it's cover or contents\n        if (image.upload_order === 1) {\n          updateImagePageType(image.id, 'cover');\n        } else {\n          updateImagePageType(image.id, 'contents');\n        }\n      } else {\n        // After a chapter is found, assign to current chapter\n        updateImagePageType(image.id, `chapter-${currentChapter}`);\n      }\n    }\n  } catch (error) {\n    console.error('Auto-assign page types error:', error);\n  }\n}\n\n// Get file size in a readable format\nexport function getFileSize(filePath: string): string {\n  try {\n    const stats = fs.statSync(filePath);\n    const bytes = stats.size;\n\n    if (bytes === 0) return '0 Bytes';\n\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  } catch (error) {\n    return 'Unknown';\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,kDAAkD;AAClD,SAAS,uBAAuB,QAAgB,EAAE,OAAe;IAC/D,MAAM,aAAa,SAAS,KAAK,CAAC,UAAU,CAAC,EAAE,IAAI,QAAQ,QAAQ;IAEnE,MAAM,qBAAqB;QACzB,eAAe;QACf,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;QAE3C,EAAE,WAAW;;;;;;;;;;;;;;;;;;oBAkBD,CAAC;QAEjB,kBAAkB;QAClB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;OAE5C,EAAE,WAAW;;;;;;;;;;;;;;;;;;qCAkBiB,CAAC;QAElC,kBAAkB;QAClB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;QAE3C,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;oCAwBe,CAAC;QAEjC,wBAAwB;QACxB,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,SAAS,cAAc,GAAG;;OAE5C,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;mCAsBe,CAAC;KACjC;IAED,sCAAsC;IACtC,MAAM,eAAe,CAAC,SAAS,cAAc,CAAC,IAAI,mBAAmB,MAAM;IAC3E,OAAO,kBAAkB,CAAC,aAAa;AACzC;AAGO,SAAS,gBAAgB,OAAe,EAAE,SAAiB;IAChE,MAAM,YAAY,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,yCAAyC,GAAG,CAAC;IAC1E,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,0CAA0C,GAAG,CAAC;IAE7E,IAAI,CAAC,aAAa,CAAC,aAAa;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,UAAU,IAAI,EAAE,YAAY,IAAI;IAEtF,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,YAAY;QAC7B,6FAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW;YAAE,WAAW;QAAK;IAC5C;IAEA,OAAO;AACT;AAGO,eAAe,iBACpB,IAAU,EACV,OAAe,EACf,SAAiB,EACjB,WAAmB,YAAY;IAE/B,IAAI;QACF,MAAM,YAAY,gBAAgB,SAAS;QAE3C,2BAA2B;QAC3B,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QACxC,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,MAAM;QAC5E,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,oBAAoB;QACpB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,6FAAA,CAAA,UAAE,CAAC,aAAa,CAAC,UAAU;QAE3B,mDAAmD;QACnD,MAAM,WAAW,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;IAI7B,CAAC,EAAE,GAAG,CAAC,SAAS;QAEhB,MAAM,cAAc,CAAC,UAAU,aAAa,CAAC,IAAI;QAEjD,6BAA6B;QAC7B,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;IAG3B,CAAC,EAAE,GAAG,CAAC,UAAU,KAAK,IAAI,EAAE,SAAS,WAAW,UAAU;QAE1D,OAAO;YACL,IAAI,OAAO,eAAe;YAC1B,UAAU;QACZ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,WAAW,OAAe;IAC9C,IAAI;QACF,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,4DAA4D,GAAG,CAAC;QAEzF,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,8BAA8B;QAC9B,MAAM,cAAc,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,mDAAmD,GAAG,CAAC;QACtF,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS;YACzD,OAAO,YAAY,OAAO;QAC5B;QAEA,uBAAuB;QACvB,IAAI,CAAC,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS,GAAG;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,QAAQ,QAAQ,EAAE,MAAM,SAAS,EAAE;QAExF,IAAI;YACF,qEAAqE;YACrE,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,SAAS;YAElE,MAAM,iBAAiB,uBAAuB,MAAM,aAAa,EAAE;YAEnE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,QAAQ,UAAU,EAAE,eAAe,MAAM,CAAC,WAAW,CAAC;YAE3G,4BAA4B;YAC5B,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGZ,CAAC,EAAE,GAAG,CAAC,SAAS;YAEhB,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS;YAC9D,OAAO;QACT,EAAE,OAAO,WAAW;YAClB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,iBAAiB,CAAC,EAAE;YAErE,wEAAwE;YACxE,MAAM,eAAe,CAAC;;;;OAIrB,EAAE,MAAM,aAAa,CAAC;UACnB,EAAE,QAAQ;;;;;;eAML,EAAE,qBAAqB,QAAQ,UAAU,OAAO,GAAG,iBAAiB;YAE7E,yFAAyF;YACzF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGZ,CAAC,EAAE,GAAG,CAAC,SAAS;YAEhB,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,SAAS;YAC7D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAE;QAE5D,+BAA+B;QAC/B,IAAI;YACF,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;MAGZ,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC1F,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,yCAAyC;QACzD;QAEA,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACtG;AACF;AAKO,SAAS,WAAW,OAAe;IACxC,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,oFACvB,GAAG,CAAC;IAEP,OAAO,SAAS,OAAO,OAAO,GAAG;AACnC;AAGO,SAAS,UAAU,OAAgB,EAAE,SAAkB;IAC5D,IAAI,QAAQ,CAAC;;;;;;;EAOb,CAAC;IAED,MAAM,SAAgB,EAAE;IACxB,MAAM,aAAuB,EAAE;IAE/B,IAAI,SAAS;QACX,WAAW,IAAI,CAAC;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW;QACb,WAAW,IAAI,CAAC;QAChB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW,MAAM,GAAG,GAAG;QACzB,SAAS,YAAY,WAAW,IAAI,CAAC;IACvC;IAEA,SAAS;IAET,OAAO,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;AAClC;AAGO,SAAS,YAAY,OAAe;IACzC,IAAI;QACF,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6CAA6C,GAAG,CAAC;QAE1E,IAAI,SAAS,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS,GAAG;YAC3C,6FAAA,CAAA,UAAE,CAAC,UAAU,CAAC,MAAM,SAAS;QAC/B;QAEA,yEAAyE;QACzE,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,mCAAmC,GAAG,CAAC;QAElD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAGO,SAAS,sBAAsB,OAAe,EAAE,OAAgB;IAOrE,MAAM,YAMD,EAAE;IAEP,6CAA6C;IAC7C,IAAI,iBAAiB;IACrB,IAAI,SAAS;QACX,MAAM,QAAQ,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,6CAA6C,GAAG,CAAC;QAC1E,IAAI,SAAS,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,UAAU,CAAC,aAAa;YACtE,iBAAiB,MAAM,SAAS,CAAC,OAAO,CAAC,YAAY;QACvD;IACF;IAEA,qCAAqC;IACrC,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;IAExF,IAAI,kBAAkB;IACtB,IAAI,iBAA2B,EAAE;IACjC,IAAI,eAAe,gBAAgB,UAAU;IAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,OAAO,KAAK,CAAC,EAAE;QAErB,2BAA2B;QAC3B,IAAI,KAAK,KAAK,CAAC,oBAAoB,KAAK,KAAK,CAAC,oBAAoB;YAChE,mCAAmC;YACnC,IAAI,iBAAiB;gBACnB,UAAU,IAAI,CAAC;oBACb,MAAM;oBACN,SAAS;oBACT,SAAS,eAAe,MAAM,GAAG,IAAI,iBAAiB;oBACtD,SAAS;gBACX;YACF;YAEA,qBAAqB;YACrB,kBAAkB,KAAK,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,mBAAmB;YAC/E,iBAAiB,EAAE;YAEnB,uBAAuB;YACvB,IAAI,gBAAgB,WAAW,GAAG,QAAQ,CAAC,WAAW,gBAAgB,WAAW,GAAG,QAAQ,CAAC,UAAU;gBACrG,eAAe;YACjB,OAAO,IAAI,gBAAgB,QAAQ,CAAC,YAAY,gBAAgB,QAAQ,CAAC,SAAS;gBAChF,eAAe;YACjB,OAAO;gBACL,eAAe;YACjB;QACF,OAEK,IAAI,KAAK,KAAK,CAAC,uBAAuB,KAAK,KAAK,CAAC,mBAAmB;YACvE,IAAI,iBAAiB;gBACnB,eAAe;gBACf,MAAM,SAAS,KAAK,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,kBAAkB;gBAC9E,eAAe,IAAI,CAAC;YACtB;QACF,OAEK,IAAI,mBAAmB,CAAC,KAAK,KAAK,CAAC,qBAAqB;YAC3D,mBAAmB,MAAM;QAC3B;IACF;IAEA,qBAAqB;IACrB,IAAI,iBAAiB;QACnB,UAAU,IAAI,CAAC;YACb,MAAM;YACN,SAAS;YACT,SAAS,eAAe,MAAM,GAAG,IAAI,iBAAiB;YACtD,SAAS;QACX;IACF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAAe,EAAE,QAAgB;IACnE,IAAI;QACF,MAAM,iBAAiB;YACrB;YAAS;YAAY;eAClB,MAAM,IAAI,CAAC;gBAAC,QAAQ;YAAE,GAAG,CAAC,GAAG,IAAM,CAAC,QAAQ,EAAE,IAAI,GAAG;SACzD;QAED,IAAI,CAAC,eAAe,QAAQ,CAAC,WAAW;YACtC,MAAM,IAAI,MAAM;QAClB;QAEA,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,gDAAgD,GAAG,CAAC,UAAU;QACzE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAGO,SAAS,oBAAoB,OAAe,EAAE,SAAiB;IACpE,IAAI;QACF,MAAM,SAAS,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;IAK3B,CAAC,EAAE,GAAG,CAAC,SAAS;QAEhB,IAAI,iBAAiB;QACrB,IAAI,oBAAoB;QAExB,KAAK,MAAM,SAAS,OAAQ;YAC1B,IAAI,MAAM,SAAS,KAAK,cAAc;gBACpC,sDAAsD;gBACtD,MAAM,eAAe,MAAM,SAAS,CAAC,KAAK,CAAC;gBAC3C,IAAI,cAAc;oBAChB,iBAAiB,SAAS,YAAY,CAAC,EAAE;oBACzC,oBAAoB;gBACtB;gBACA;YACF;YAEA,gCAAgC;YAChC,IAAI,CAAC,mBAAmB;gBACtB,6DAA6D;gBAC7D,IAAI,MAAM,YAAY,KAAK,GAAG;oBAC5B,oBAAoB,MAAM,EAAE,EAAE;gBAChC,OAAO;oBACL,oBAAoB,MAAM,EAAE,EAAE;gBAChC;YACF,OAAO;gBACL,sDAAsD;gBACtD,oBAAoB,MAAM,EAAE,EAAE,CAAC,QAAQ,EAAE,gBAAgB;YAC3D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAGO,SAAS,YAAY,QAAgB;IAC1C,IAAI;QACF,MAAM,QAAQ,6FAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAC1B,MAAM,QAAQ,MAAM,IAAI;QAExB,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1011, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/PRG/StudyApp/study-app/src/app/api/admin/images/%5Bid%5D/ocr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { requireAdmin } from '@/lib/auth';\nimport { processOCR } from '@/lib/upload';\nimport db from '@/lib/database';\n\nexport async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {\n  const user = await requireAdmin();\n  \n  if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n\n  try {\n    const { id } = await params;\n    const imageId = parseInt(id);\n\n    if (isNaN(imageId)) {\n      return NextResponse.json({ error: 'Invalid image ID' }, { status: 400 });\n    }\n\n    console.log(`Starting OCR processing for image ID: ${imageId}`);\n    \n    // Process OCR\n    const ocrText = await processOCR(imageId);\n    \n    return NextResponse.json({ \n      success: true, \n      text: ocrText,\n      message: 'OCR processing completed successfully'\n    });\n  } catch (error) {\n    console.error('OCR API error:', error);\n    return NextResponse.json({ \n      error: 'Failed to process OCR',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {\n  const user = await requireAdmin();\n\n  if (!user) {\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n  }\n\n  try {\n    const { id } = await params;\n    const imageId = parseInt(id);\n\n    // Get image info and OCR text\n    const imageInfo = db.prepare(`\n      SELECT\n        i.id,\n        i.original_name,\n        i.page_type,\n        i.upload_order,\n        i.book_id,\n        b.title as book_title,\n        ocr.content,\n        ocr.processed,\n        ocr.created_at\n      FROM images i\n      LEFT JOIN books b ON i.book_id = b.id\n      LEFT JOIN ocr_text ocr ON i.id = ocr.image_id\n      WHERE i.id = ?\n    `).get(imageId) as any;\n\n    if (!imageInfo) {\n      return NextResponse.json({ error: 'Image not found' }, { status: 404 });\n    }\n\n    return NextResponse.json({\n      success: true,\n      image: {\n        id: imageInfo.id,\n        name: imageInfo.original_name,\n        pageType: imageInfo.page_type,\n        pageNumber: imageInfo.upload_order,\n        bookId: imageInfo.book_id,\n        bookTitle: imageInfo.book_title\n      },\n      ocr: imageInfo.content ? {\n        text: imageInfo.content,\n        processed: imageInfo.processed,\n        processedAt: imageInfo.created_at,\n        wordCount: imageInfo.content.split(/\\s+/).length,\n        characterCount: imageInfo.content.length\n      } : null\n    });\n\n  } catch (error) {\n    console.error('OCR retrieval error:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB,EAAE,EAAE,MAAM,EAAuC;IAC9F,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,SAAS;QAEzB,IAAI,MAAM,UAAU;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS;QAE9D,cAAc;QACd,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;QAEjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,IAAI,OAAoB,EAAE,EAAE,MAAM,EAAuC;IAC7F,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD;IAE9B,IAAI,CAAC,MAAM;QACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,SAAS;QAEzB,8BAA8B;QAC9B,MAAM,YAAY,wHAAA,CAAA,UAAE,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;IAe9B,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;gBACL,IAAI,UAAU,EAAE;gBAChB,MAAM,UAAU,aAAa;gBAC7B,UAAU,UAAU,SAAS;gBAC7B,YAAY,UAAU,YAAY;gBAClC,QAAQ,UAAU,OAAO;gBACzB,WAAW,UAAU,UAAU;YACjC;YACA,KAAK,UAAU,OAAO,GAAG;gBACvB,MAAM,UAAU,OAAO;gBACvB,WAAW,UAAU,SAAS;gBAC9B,aAAa,UAAU,UAAU;gBACjC,WAAW,UAAU,OAAO,CAAC,KAAK,CAAC,OAAO,MAAM;gBAChD,gBAAgB,UAAU,OAAO,CAAC,MAAM;YAC1C,IAAI;QACN;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}