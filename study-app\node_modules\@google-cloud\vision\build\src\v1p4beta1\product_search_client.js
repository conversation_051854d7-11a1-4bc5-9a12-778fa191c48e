"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSearchClient = void 0;
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1p4beta1/product_search_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./product_search_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Manages Products and ProductSets of reference images for use in product
 *  search. It uses the following resource model:
 *
 *  - The API has a collection of
 *  {@link protos.google.cloud.vision.v1p4beta1.ProductSet|ProductSet} resources, named
 *  `projects/* /locations/* /productSets/*`, which acts as a way to put different
 *  products into groups to limit identification.
 *
 *  In parallel,
 *
 *  - The API has a collection of
 *  {@link protos.google.cloud.vision.v1p4beta1.Product|Product} resources, named
 *    `projects/* /locations/* /products/*`
 *
 *  - Each {@link protos.google.cloud.vision.v1p4beta1.Product|Product} has a collection of
 *  {@link protos.google.cloud.vision.v1p4beta1.ReferenceImage|ReferenceImage} resources,
 *  named
 *    `projects/* /locations/* /products/* /referenceImages/*`
 * @class
 * @memberof v1p4beta1
 */
class ProductSearchClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('vision');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    operationsClient;
    productSearchStub;
    /**
     * Construct an instance of ProductSearchClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new ProductSearchClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'vision.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            locationPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}'),
            productPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/products/{product}'),
            productSetPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/productSets/{product_set}'),
            referenceImagePathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listProductSets: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'productSets'),
            listProducts: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'products'),
            listReferenceImages: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'referenceImages'),
            listProductsInProductSet: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'products'),
        };
        const protoFilesRoot = this._gaxModule.protobuf.Root.fromJSON(jsonProtos);
        // This API contains "long-running operations", which return a
        // an Operation object that allows for tracking of the operation,
        // rather than holding a request open.
        const lroOptions = {
            auth: this.auth,
            grpc: 'grpc' in this._gaxGrpc ? this._gaxGrpc.grpc : undefined,
        };
        if (opts.fallback) {
            lroOptions.protoJson = protoFilesRoot;
            lroOptions.httpRules = [];
        }
        this.operationsClient = this._gaxModule
            .lro(lroOptions)
            .operationsClient(opts);
        const importProductSetsResponse = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.ImportProductSetsResponse');
        const importProductSetsMetadata = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.BatchOperationMetadata');
        const purgeProductsResponse = protoFilesRoot.lookup('.google.protobuf.Empty');
        const purgeProductsMetadata = protoFilesRoot.lookup('.google.cloud.vision.v1p4beta1.BatchOperationMetadata');
        this.descriptors.longrunning = {
            importProductSets: new this._gaxModule.LongrunningDescriptor(this.operationsClient, importProductSetsResponse.decode.bind(importProductSetsResponse), importProductSetsMetadata.decode.bind(importProductSetsMetadata)),
            purgeProducts: new this._gaxModule.LongrunningDescriptor(this.operationsClient, purgeProductsResponse.decode.bind(purgeProductsResponse), purgeProductsMetadata.decode.bind(purgeProductsMetadata)),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.vision.v1p4beta1.ProductSearch', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.productSearchStub) {
            return this.productSearchStub;
        }
        // Put together the "service stub" for
        // google.cloud.vision.v1p4beta1.ProductSearch.
        this.productSearchStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.vision.v1p4beta1.ProductSearch')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.vision.v1p4beta1.ProductSearch, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const productSearchStubMethods = [
            'createProductSet',
            'listProductSets',
            'getProductSet',
            'updateProductSet',
            'deleteProductSet',
            'createProduct',
            'listProducts',
            'getProduct',
            'updateProduct',
            'deleteProduct',
            'createReferenceImage',
            'deleteReferenceImage',
            'listReferenceImages',
            'getReferenceImage',
            'addProductToProductSet',
            'removeProductFromProductSet',
            'listProductsInProductSet',
            'importProductSets',
            'purgeProducts',
        ];
        for (const methodName of productSearchStubMethods) {
            const callPromise = this.productSearchStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] ||
                this.descriptors.longrunning[methodName] ||
                undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.productSearchStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'vision.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'vision.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return [
            'https://www.googleapis.com/auth/cloud-platform',
            'https://www.googleapis.com/auth/cloud-vision',
        ];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    createProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    getProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'product_set.name': request.productSet.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    createProduct(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createProduct request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createProduct response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createProduct(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createProduct response %j', response);
            return [response, options, rawResponse];
        });
    }
    getProduct(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getProduct request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getProduct response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getProduct(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getProduct response %j', response);
            return [response, options, rawResponse];
        });
    }
    updateProduct(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'product.name': request.product.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('updateProduct request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('updateProduct response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .updateProduct(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('updateProduct response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteProduct(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteProduct request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteProduct response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteProduct(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteProduct response %j', response);
            return [response, options, rawResponse];
        });
    }
    createReferenceImage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('createReferenceImage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('createReferenceImage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .createReferenceImage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('createReferenceImage response %j', response);
            return [response, options, rawResponse];
        });
    }
    deleteReferenceImage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('deleteReferenceImage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('deleteReferenceImage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .deleteReferenceImage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('deleteReferenceImage response %j', response);
            return [response, options, rawResponse];
        });
    }
    getReferenceImage(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('getReferenceImage request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('getReferenceImage response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .getReferenceImage(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('getReferenceImage response %j', response);
            return [response, options, rawResponse];
        });
    }
    addProductToProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('addProductToProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('addProductToProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .addProductToProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('addProductToProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    removeProductFromProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('removeProductFromProductSet request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('removeProductFromProductSet response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .removeProductFromProductSet(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('removeProductFromProductSet response %j', response);
            return [response, options, rawResponse];
        });
    }
    importProductSets(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('importProductSets response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('importProductSets request %j', request);
        return this.innerApiCalls
            .importProductSets(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('importProductSets response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `importProductSets()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.import_product_sets.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_ImportProductSets_async
     */
    async checkImportProductSetsProgress(name) {
        this._log.info('importProductSets long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.importProductSets, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    purgeProducts(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, response, rawResponse, _) => {
                this._log.info('purgeProducts response %j', rawResponse);
                callback(error, response, rawResponse, _); // We verified callback above.
            }
            : undefined;
        this._log.info('purgeProducts request %j', request);
        return this.innerApiCalls
            .purgeProducts(request, options, wrappedCallback)
            ?.then(([response, rawResponse, _]) => {
            this._log.info('purgeProducts response %j', rawResponse);
            return [response, rawResponse, _];
        });
    }
    /**
     * Check the status of the long running operation returned by `purgeProducts()`.
     * @param {String} name
     *   The operation name that will be passed.
     * @returns {Promise} - The promise which resolves to an object.
     *   The decoded operation object has result and metadata field to get information from.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#long-running-operations | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.purge_products.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_PurgeProducts_async
     */
    async checkPurgeProductsProgress(name) {
        this._log.info('purgeProducts long-running');
        const request = new this._gaxModule.operationsProtos.google.longrunning.GetOperationRequest({ name });
        const [operation] = await this.operationsClient.getOperation(request);
        const decodeOperation = new this._gaxModule.Operation(operation, this.descriptors.longrunning.purgeProducts, this._gaxModule.createDefaultBackoffSettings());
        return decodeOperation;
    }
    listProductSets(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listProductSets values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listProductSets request %j', request);
        return this.innerApiCalls
            .listProductSets(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listProductSets values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listProductSets`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project from which ProductSets should be listed.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p4beta1.ProductSet|ProductSet} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductSetsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductSetsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listProductSets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProductSets stream %j', request);
        return this.descriptors.page.listProductSets.createStream(this.innerApiCalls.listProductSets, request, callSettings);
    }
    /**
     * Equivalent to `listProductSets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project from which ProductSets should be listed.
     *
     *   Format is `projects/PROJECT_ID/locations/LOC_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p4beta1.ProductSet|ProductSet}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.list_product_sets.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_ListProductSets_async
     */
    listProductSetsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listProductSets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProductSets iterate %j', request);
        return this.descriptors.page.listProductSets.asyncIterate(this.innerApiCalls['listProductSets'], request, callSettings);
    }
    listProducts(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listProducts values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listProducts request %j', request);
        return this.innerApiCalls
            .listProducts(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listProducts values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listProducts`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project OR ProductSet from which Products should be listed.
     *
     *   Format:
     *   `projects/PROJECT_ID/locations/LOC_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p4beta1.Product|Product} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductsStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listProducts'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProducts stream %j', request);
        return this.descriptors.page.listProducts.createStream(this.innerApiCalls.listProducts, request, callSettings);
    }
    /**
     * Equivalent to `listProducts`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The project OR ProductSet from which Products should be listed.
     *
     *   Format:
     *   `projects/PROJECT_ID/locations/LOC_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p4beta1.Product|Product}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.list_products.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_ListProducts_async
     */
    listProductsAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listProducts'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProducts iterate %j', request);
        return this.descriptors.page.listProducts.asyncIterate(this.innerApiCalls['listProducts'], request, callSettings);
    }
    listReferenceImages(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listReferenceImages values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listReferenceImages request %j', request);
        return this.innerApiCalls
            .listReferenceImages(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listReferenceImages values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listReferenceImages`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product containing the reference images.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   A token identifying a page of results to be returned. This is the value
     *   of `nextPageToken` returned in a previous reference image list request.
     *
     *   Defaults to the first page if not specified.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p4beta1.ReferenceImage|ReferenceImage} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listReferenceImagesAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listReferenceImagesStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listReferenceImages'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listReferenceImages stream %j', request);
        return this.descriptors.page.listReferenceImages.createStream(this.innerApiCalls.listReferenceImages, request, callSettings);
    }
    /**
     * Equivalent to `listReferenceImages`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. Resource name of the product containing the reference images.
     *
     *   Format is
     *   `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   A token identifying a page of results to be returned. This is the value
     *   of `nextPageToken` returned in a previous reference image list request.
     *
     *   Defaults to the first page if not specified.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p4beta1.ReferenceImage|ReferenceImage}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.list_reference_images.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_ListReferenceImages_async
     */
    listReferenceImagesAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: request.parent ?? '',
            });
        const defaultCallSettings = this._defaults['listReferenceImages'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listReferenceImages iterate %j', request);
        return this.descriptors.page.listReferenceImages.asyncIterate(this.innerApiCalls['listReferenceImages'], request, callSettings);
    }
    listProductsInProductSet(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        this.initialize().catch(err => {
            throw err;
        });
        const wrappedCallback = callback
            ? (error, values, nextPageRequest, rawResponse) => {
                this._log.info('listProductsInProductSet values %j', values);
                callback(error, values, nextPageRequest, rawResponse); // We verified callback above.
            }
            : undefined;
        this._log.info('listProductsInProductSet request %j', request);
        return this.innerApiCalls
            .listProductsInProductSet(request, options, wrappedCallback)
            ?.then(([response, input, output]) => {
            this._log.info('listProductsInProductSet values %j', response);
            return [response, input, output];
        });
    }
    /**
     * Equivalent to `listProductsInProductSet`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The ProductSet resource for which to retrieve Products.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.vision.v1p4beta1.Product|Product} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listProductsInProductSetAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listProductsInProductSetStream(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        const defaultCallSettings = this._defaults['listProductsInProductSet'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProductsInProductSet stream %j', request);
        return this.descriptors.page.listProductsInProductSet.createStream(this.innerApiCalls.listProductsInProductSet, request, callSettings);
    }
    /**
     * Equivalent to `listProductsInProductSet`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Required. The ProductSet resource for which to retrieve Products.
     *
     *   Format is:
     *   `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
     * @param {number} request.pageSize
     *   The maximum number of items to return. Default 10, maximum 100.
     * @param {string} request.pageToken
     *   The next_page_token returned from a previous List request, if any.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.vision.v1p4beta1.Product|Product}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1p4beta1/product_search.list_products_in_product_set.js</caption>
     * region_tag:vision_v1p4beta1_generated_ProductSearch_ListProductsInProductSet_async
     */
    listProductsInProductSetAsync(request, options) {
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: request.name ?? '',
            });
        const defaultCallSettings = this._defaults['listProductsInProductSet'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listProductsInProductSet iterate %j', request);
        return this.descriptors.page.listProductsInProductSet.asyncIterate(this.innerApiCalls['listProductsInProductSet'], request, callSettings);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified location resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @returns {string} Resource name string.
     */
    locationPath(project, location) {
        return this.pathTemplates.locationPathTemplate.render({
            project: project,
            location: location,
        });
    }
    /**
     * Parse the project from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).project;
    }
    /**
     * Parse the location from Location resource.
     *
     * @param {string} locationName
     *   A fully-qualified path representing Location resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromLocationName(locationName) {
        return this.pathTemplates.locationPathTemplate.match(locationName).location;
    }
    /**
     * Return a fully-qualified product resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @returns {string} Resource name string.
     */
    productPath(project, location, product) {
        return this.pathTemplates.productPathTemplate.render({
            project: project,
            location: location,
            product: product,
        });
    }
    /**
     * Parse the project from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).project;
    }
    /**
     * Parse the location from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).location;
    }
    /**
     * Parse the product from Product resource.
     *
     * @param {string} productName
     *   A fully-qualified path representing Product resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromProductName(productName) {
        return this.pathTemplates.productPathTemplate.match(productName).product;
    }
    /**
     * Return a fully-qualified productSet resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product_set
     * @returns {string} Resource name string.
     */
    productSetPath(project, location, productSet) {
        return this.pathTemplates.productSetPathTemplate.render({
            project: project,
            location: location,
            product_set: productSet,
        });
    }
    /**
     * Parse the project from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .project;
    }
    /**
     * Parse the location from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .location;
    }
    /**
     * Parse the product_set from ProductSet resource.
     *
     * @param {string} productSetName
     *   A fully-qualified path representing ProductSet resource.
     * @returns {string} A string representing the product_set.
     */
    matchProductSetFromProductSetName(productSetName) {
        return this.pathTemplates.productSetPathTemplate.match(productSetName)
            .product_set;
    }
    /**
     * Return a fully-qualified referenceImage resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} product
     * @param {string} reference_image
     * @returns {string} Resource name string.
     */
    referenceImagePath(project, location, product, referenceImage) {
        return this.pathTemplates.referenceImagePathTemplate.render({
            project: project,
            location: location,
            product: product,
            reference_image: referenceImage,
        });
    }
    /**
     * Parse the project from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).project;
    }
    /**
     * Parse the location from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).location;
    }
    /**
     * Parse the product from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the product.
     */
    matchProductFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).product;
    }
    /**
     * Parse the reference_image from ReferenceImage resource.
     *
     * @param {string} referenceImageName
     *   A fully-qualified path representing ReferenceImage resource.
     * @returns {string} A string representing the reference_image.
     */
    matchReferenceImageFromReferenceImageName(referenceImageName) {
        return this.pathTemplates.referenceImagePathTemplate.match(referenceImageName).reference_image;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.productSearchStub && !this._terminated) {
            return this.productSearchStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
                void this.operationsClient.close();
            });
        }
        return Promise.resolve();
    }
}
exports.ProductSearchClient = ProductSearchClient;
//# sourceMappingURL=product_search_client.js.map