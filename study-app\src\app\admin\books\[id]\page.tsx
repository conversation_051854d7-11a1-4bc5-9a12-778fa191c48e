import { redirect } from 'next/navigation';
import { requireAdmin } from '@/lib/auth';
import { BookModel } from '@/lib/models';
import AdminLayout from '@/components/AdminLayout';
import Link from 'next/link';
import ClearPagesButton from '@/components/ClearPagesButton';
import BookImageManager from '@/components/BookImageManager';

interface BookPageProps {
  params: Promise<{ id: string }>;
}

export default async function BookPage({ params }: BookPageProps) {
  const user = await requireAdmin();
  
  if (!user) {
    redirect('/login');
  }

  const { id } = await params;
  const bookId = parseInt(id);
  
  if (isNaN(bookId)) {
    redirect('/admin/books');
  }

  const book = BookModel.getById(bookId);
  
  if (!book) {
    redirect('/admin/books');
  }

  const images = BookModel.getImages(bookId);

  return (
    <AdminLayout user={user}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/admin/books" className="hover:text-gray-700">Books</Link>
              <span>›</span>
              <span>{book.title}</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">{book.title}</h1>
            <p className="mt-1 text-sm text-gray-600">
              {book.class_name} • {book.subject_name}
            </p>
            {book.description && (
              <p className="mt-2 text-gray-700">{book.description}</p>
            )}
          </div>
          <div className="flex space-x-3">
            <ClearPagesButton bookId={book.id} />
            <Link
              href={`/admin/books/${book.id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
            >
              Edit Book
            </Link>
          </div>
        </div>

        {/* Status and Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md flex items-center justify-center ${
                    book.status === 'completed' ? 'bg-green-500' :
                    book.status === 'processing' ? 'bg-yellow-500' :
                    'bg-gray-500'
                  }`}>
                    <span className="text-white text-sm font-medium">
                      {book.status === 'completed' ? '✓' : 
                       book.status === 'processing' ? '⚡' : '📝'}
                    </span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Status</dt>
                    <dd className="text-lg font-medium text-gray-900 capitalize">{book.status}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">📄</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pages</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.uploaded_pages}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">✓</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Processed</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.processed_pages}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <span className="text-white text-sm font-medium">❓</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Questions</dt>
                    <dd className="text-lg font-medium text-gray-900">{book.total_questions}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Actions */}
        {images.length > 0 && (
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Additional Actions</h3>
              <div className="flex space-x-3">
                <Link
                  href={`/admin/books/${book.id}/ocr`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  View All OCR Text
                </Link>
                <Link
                  href={`/admin/books/${book.id}/upload`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Upload PDF (Legacy)
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Book Image Management */}
        <BookImageManager book={book} initialImages={images} />
      </div>
    </AdminLayout>
  );
}
