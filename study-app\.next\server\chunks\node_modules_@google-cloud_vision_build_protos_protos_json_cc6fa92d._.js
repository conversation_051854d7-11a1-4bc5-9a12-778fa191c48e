module.exports = {

"[project]/node_modules/@google-cloud/vision/build/protos/protos.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"options\":{\"syntax\":\"proto3\"},\"nested\":{\"google\":{\"nested\":{\"cloud\":{\"nested\":{\"vision\":{\"nested\":{\"v1\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"cloud.google.com/go/vision/v2/apiv1/visionpb;visionpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"WebDetectionProto\",\"java_package\":\"com.google.cloud.vision.v1\",\"objc_class_prefix\":\"GCVN\"},\"nested\":{\"Vertex\":{\"fields\":{\"x\":{\"type\":\"int32\",\"id\":1},\"y\":{\"type\":\"int32\",\"id\":2}}},\"NormalizedVertex\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2}}},\"BoundingPoly\":{\"fields\":{\"vertices\":{\"rule\":\"repeated\",\"type\":\"Vertex\",\"id\":1},\"normalizedVertices\":{\"rule\":\"repeated\",\"type\":\"NormalizedVertex\",\"id\":2}}},\"Position\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2},\"z\":{\"type\":\"float\",\"id\":3}}},\"ImageAnnotator\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"BatchAnnotateImages\":{\"requestType\":\"BatchAnnotateImagesRequest\",\"responseType\":\"BatchAnnotateImagesResponse\",\"options\":{\"(google.api.http).post\":\"/v1/images:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.http).additional_bindings.post\":\"/v1/{parent=projects/*}/images:annotate\",\"(google.api.http).additional_bindings.body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/images:annotate\",\"body\":\"*\",\"additional_bindings\":[{\"post\":\"/v1/{parent=projects/*/locations/*}/images:annotate\",\"body\":\"*\"},{\"post\":\"/v1/{parent=projects/*}/images:annotate\",\"body\":\"*\"}]}},{\"(google.api.method_signature)\":\"requests\"}]},\"BatchAnnotateFiles\":{\"requestType\":\"BatchAnnotateFilesRequest\",\"responseType\":\"BatchAnnotateFilesResponse\",\"options\":{\"(google.api.http).post\":\"/v1/files:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.http).additional_bindings.post\":\"/v1/{parent=projects/*}/files:annotate\",\"(google.api.http).additional_bindings.body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/files:annotate\",\"body\":\"*\",\"additional_bindings\":[{\"post\":\"/v1/{parent=projects/*/locations/*}/files:annotate\",\"body\":\"*\"},{\"post\":\"/v1/{parent=projects/*}/files:annotate\",\"body\":\"*\"}]}},{\"(google.api.method_signature)\":\"requests\"}]},\"AsyncBatchAnnotateImages\":{\"requestType\":\"AsyncBatchAnnotateImagesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1/images:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.http).additional_bindings.post\":\"/v1/{parent=projects/*}/images:asyncBatchAnnotate\",\"(google.api.http).additional_bindings.body\":\"*\",\"(google.api.method_signature)\":\"requests,output_config\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateImagesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/images:asyncBatchAnnotate\",\"body\":\"*\",\"additional_bindings\":[{\"post\":\"/v1/{parent=projects/*/locations/*}/images:asyncBatchAnnotate\",\"body\":\"*\"},{\"post\":\"/v1/{parent=projects/*}/images:asyncBatchAnnotate\",\"body\":\"*\"}]}},{\"(google.api.method_signature)\":\"requests,output_config\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateImagesResponse\",\"metadata_type\":\"OperationMetadata\"}}]},\"AsyncBatchAnnotateFiles\":{\"requestType\":\"AsyncBatchAnnotateFilesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1/files:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.http).additional_bindings.post\":\"/v1/{parent=projects/*}/files:asyncBatchAnnotate\",\"(google.api.http).additional_bindings.body\":\"*\",\"(google.api.method_signature)\":\"requests\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateFilesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/files:asyncBatchAnnotate\",\"body\":\"*\",\"additional_bindings\":[{\"post\":\"/v1/{parent=projects/*/locations/*}/files:asyncBatchAnnotate\",\"body\":\"*\"},{\"post\":\"/v1/{parent=projects/*}/files:asyncBatchAnnotate\",\"body\":\"*\"}]}},{\"(google.api.method_signature)\":\"requests\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateFilesResponse\",\"metadata_type\":\"OperationMetadata\"}}]}}},\"Likelihood\":{\"values\":{\"UNKNOWN\":0,\"VERY_UNLIKELY\":1,\"UNLIKELY\":2,\"POSSIBLE\":3,\"LIKELY\":4,\"VERY_LIKELY\":5}},\"Feature\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":1},\"maxResults\":{\"type\":\"int32\",\"id\":2},\"model\":{\"type\":\"string\",\"id\":3}},\"nested\":{\"Type\":{\"values\":{\"TYPE_UNSPECIFIED\":0,\"FACE_DETECTION\":1,\"LANDMARK_DETECTION\":2,\"LOGO_DETECTION\":3,\"LABEL_DETECTION\":4,\"TEXT_DETECTION\":5,\"DOCUMENT_TEXT_DETECTION\":11,\"SAFE_SEARCH_DETECTION\":6,\"IMAGE_PROPERTIES\":7,\"CROP_HINTS\":9,\"WEB_DETECTION\":10,\"PRODUCT_SEARCH\":12,\"OBJECT_LOCALIZATION\":19}}}},\"ImageSource\":{\"fields\":{\"gcsImageUri\":{\"type\":\"string\",\"id\":1},\"imageUri\":{\"type\":\"string\",\"id\":2}}},\"Image\":{\"fields\":{\"content\":{\"type\":\"bytes\",\"id\":1},\"source\":{\"type\":\"ImageSource\",\"id\":2}}},\"FaceAnnotation\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"fdBoundingPoly\":{\"type\":\"BoundingPoly\",\"id\":2},\"landmarks\":{\"rule\":\"repeated\",\"type\":\"Landmark\",\"id\":3},\"rollAngle\":{\"type\":\"float\",\"id\":4},\"panAngle\":{\"type\":\"float\",\"id\":5},\"tiltAngle\":{\"type\":\"float\",\"id\":6},\"detectionConfidence\":{\"type\":\"float\",\"id\":7},\"landmarkingConfidence\":{\"type\":\"float\",\"id\":8},\"joyLikelihood\":{\"type\":\"Likelihood\",\"id\":9},\"sorrowLikelihood\":{\"type\":\"Likelihood\",\"id\":10},\"angerLikelihood\":{\"type\":\"Likelihood\",\"id\":11},\"surpriseLikelihood\":{\"type\":\"Likelihood\",\"id\":12},\"underExposedLikelihood\":{\"type\":\"Likelihood\",\"id\":13},\"blurredLikelihood\":{\"type\":\"Likelihood\",\"id\":14},\"headwearLikelihood\":{\"type\":\"Likelihood\",\"id\":15}},\"nested\":{\"Landmark\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":3},\"position\":{\"type\":\"Position\",\"id\":4}},\"nested\":{\"Type\":{\"values\":{\"UNKNOWN_LANDMARK\":0,\"LEFT_EYE\":1,\"RIGHT_EYE\":2,\"LEFT_OF_LEFT_EYEBROW\":3,\"RIGHT_OF_LEFT_EYEBROW\":4,\"LEFT_OF_RIGHT_EYEBROW\":5,\"RIGHT_OF_RIGHT_EYEBROW\":6,\"MIDPOINT_BETWEEN_EYES\":7,\"NOSE_TIP\":8,\"UPPER_LIP\":9,\"LOWER_LIP\":10,\"MOUTH_LEFT\":11,\"MOUTH_RIGHT\":12,\"MOUTH_CENTER\":13,\"NOSE_BOTTOM_RIGHT\":14,\"NOSE_BOTTOM_LEFT\":15,\"NOSE_BOTTOM_CENTER\":16,\"LEFT_EYE_TOP_BOUNDARY\":17,\"LEFT_EYE_RIGHT_CORNER\":18,\"LEFT_EYE_BOTTOM_BOUNDARY\":19,\"LEFT_EYE_LEFT_CORNER\":20,\"RIGHT_EYE_TOP_BOUNDARY\":21,\"RIGHT_EYE_RIGHT_CORNER\":22,\"RIGHT_EYE_BOTTOM_BOUNDARY\":23,\"RIGHT_EYE_LEFT_CORNER\":24,\"LEFT_EYEBROW_UPPER_MIDPOINT\":25,\"RIGHT_EYEBROW_UPPER_MIDPOINT\":26,\"LEFT_EAR_TRAGION\":27,\"RIGHT_EAR_TRAGION\":28,\"LEFT_EYE_PUPIL\":29,\"RIGHT_EYE_PUPIL\":30,\"FOREHEAD_GLABELLA\":31,\"CHIN_GNATHION\":32,\"CHIN_LEFT_GONION\":33,\"CHIN_RIGHT_GONION\":34,\"LEFT_CHEEK_CENTER\":35,\"RIGHT_CHEEK_CENTER\":36}}}}}},\"LocationInfo\":{\"fields\":{\"latLng\":{\"type\":\"google.type.LatLng\",\"id\":1}}},\"Property\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2},\"uint64Value\":{\"type\":\"uint64\",\"id\":3}}},\"EntityAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"locale\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5,\"options\":{\"deprecated\":true}},\"topicality\":{\"type\":\"float\",\"id\":6},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":7},\"locations\":{\"rule\":\"repeated\",\"type\":\"LocationInfo\",\"id\":8},\"properties\":{\"rule\":\"repeated\",\"type\":\"Property\",\"id\":9}}},\"LocalizedObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":5}}},\"SafeSearchAnnotation\":{\"fields\":{\"adult\":{\"type\":\"Likelihood\",\"id\":1},\"spoof\":{\"type\":\"Likelihood\",\"id\":2},\"medical\":{\"type\":\"Likelihood\",\"id\":3},\"violence\":{\"type\":\"Likelihood\",\"id\":4},\"racy\":{\"type\":\"Likelihood\",\"id\":9}}},\"LatLongRect\":{\"fields\":{\"minLatLng\":{\"type\":\"google.type.LatLng\",\"id\":1},\"maxLatLng\":{\"type\":\"google.type.LatLng\",\"id\":2}}},\"ColorInfo\":{\"fields\":{\"color\":{\"type\":\"google.type.Color\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pixelFraction\":{\"type\":\"float\",\"id\":3}}},\"DominantColorsAnnotation\":{\"fields\":{\"colors\":{\"rule\":\"repeated\",\"type\":\"ColorInfo\",\"id\":1}}},\"ImageProperties\":{\"fields\":{\"dominantColors\":{\"type\":\"DominantColorsAnnotation\",\"id\":1}}},\"CropHint\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2},\"importanceFraction\":{\"type\":\"float\",\"id\":3}}},\"CropHintsAnnotation\":{\"fields\":{\"cropHints\":{\"rule\":\"repeated\",\"type\":\"CropHint\",\"id\":1}}},\"CropHintsParams\":{\"fields\":{\"aspectRatios\":{\"rule\":\"repeated\",\"type\":\"float\",\"id\":1}}},\"WebDetectionParams\":{\"fields\":{\"includeGeoResults\":{\"type\":\"bool\",\"id\":2,\"options\":{\"deprecated\":true}}}},\"TextDetectionParams\":{\"fields\":{\"enableTextDetectionConfidenceScore\":{\"type\":\"bool\",\"id\":9},\"advancedOcrOptions\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":11}}},\"ImageContext\":{\"fields\":{\"latLongRect\":{\"type\":\"LatLongRect\",\"id\":1},\"languageHints\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"cropHintsParams\":{\"type\":\"CropHintsParams\",\"id\":4},\"productSearchParams\":{\"type\":\"ProductSearchParams\",\"id\":5},\"webDetectionParams\":{\"type\":\"WebDetectionParams\",\"id\":6},\"textDetectionParams\":{\"type\":\"TextDetectionParams\",\"id\":12}}},\"AnnotateImageRequest\":{\"fields\":{\"image\":{\"type\":\"Image\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3}}},\"ImageAnnotationContext\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1},\"pageNumber\":{\"type\":\"int32\",\"id\":2}}},\"AnnotateImageResponse\":{\"fields\":{\"faceAnnotations\":{\"rule\":\"repeated\",\"type\":\"FaceAnnotation\",\"id\":1},\"landmarkAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":2},\"logoAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":3},\"labelAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":4},\"localizedObjectAnnotations\":{\"rule\":\"repeated\",\"type\":\"LocalizedObjectAnnotation\",\"id\":22},\"textAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":5},\"fullTextAnnotation\":{\"type\":\"TextAnnotation\",\"id\":12},\"safeSearchAnnotation\":{\"type\":\"SafeSearchAnnotation\",\"id\":6},\"imagePropertiesAnnotation\":{\"type\":\"ImageProperties\",\"id\":8},\"cropHintsAnnotation\":{\"type\":\"CropHintsAnnotation\",\"id\":11},\"webDetection\":{\"type\":\"WebDetection\",\"id\":13},\"productSearchResults\":{\"type\":\"ProductSearchResults\",\"id\":14},\"error\":{\"type\":\"google.rpc.Status\",\"id\":9},\"context\":{\"type\":\"ImageAnnotationContext\",\"id\":21}}},\"BatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"parent\":{\"type\":\"string\",\"id\":4},\"labels\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":5,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"BatchAnnotateImagesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":1}}},\"AnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"pages\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":4}}},\"AnnotateFileResponse\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":2},\"totalPages\":{\"type\":\"int32\",\"id\":3},\"error\":{\"type\":\"google.rpc.Status\",\"id\":4}}},\"BatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"parent\":{\"type\":\"string\",\"id\":3},\"labels\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":5,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"BatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateFileResponse\",\"id\":1}}},\"AsyncAnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":4}}},\"AsyncAnnotateFileResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"parent\":{\"type\":\"string\",\"id\":4},\"labels\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":5,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"AsyncBatchAnnotateImagesResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"parent\":{\"type\":\"string\",\"id\":4},\"labels\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":5,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"AsyncBatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileResponse\",\"id\":1}}},\"InputConfig\":{\"fields\":{\"gcsSource\":{\"type\":\"GcsSource\",\"id\":1},\"content\":{\"type\":\"bytes\",\"id\":3},\"mimeType\":{\"type\":\"string\",\"id\":2}}},\"OutputConfig\":{\"fields\":{\"gcsDestination\":{\"type\":\"GcsDestination\",\"id\":1},\"batchSize\":{\"type\":\"int32\",\"id\":2}}},\"GcsSource\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"GcsDestination\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"OperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"createTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":5},\"updateTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":6}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"CREATED\":1,\"RUNNING\":2,\"DONE\":3,\"CANCELLED\":4}}}},\"ProductSearchParams\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":9},\"productSet\":{\"type\":\"string\",\"id\":6,\"options\":{\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"productCategories\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":7},\"filter\":{\"type\":\"string\",\"id\":8}}},\"ProductSearchResults\":{\"fields\":{\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":5},\"productGroupedResults\":{\"rule\":\"repeated\",\"type\":\"GroupedResult\",\"id\":6}},\"nested\":{\"Result\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"image\":{\"type\":\"string\",\"id\":3}}},\"ObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4}}},\"GroupedResult\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":2},\"objectAnnotations\":{\"rule\":\"repeated\",\"type\":\"ObjectAnnotation\",\"id\":3}}}}},\"ProductSearch\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"CreateProductSet\":{\"requestType\":\"CreateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).post\":\"/v1/{parent=projects/*/locations/*}/productSets\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{parent=projects/*/locations/*}/productSets\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"}]},\"ListProductSets\":{\"requestType\":\"ListProductSetsRequest\",\"responseType\":\"ListProductSetsResponse\",\"options\":{\"(google.api.http).get\":\"/v1/{parent=projects/*/locations/*}/productSets\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{parent=projects/*/locations/*}/productSets\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProductSet\":{\"requestType\":\"GetProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).get\":\"/v1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProductSet\":{\"requestType\":\"UpdateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).patch\":\"/v1/{product_set.name=projects/*/locations/*/productSets/*}\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"product_set,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1/{product_set.name=projects/*/locations/*/productSets/*}\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"product_set,update_mask\"}]},\"DeleteProductSet\":{\"requestType\":\"DeleteProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateProduct\":{\"requestType\":\"CreateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).post\":\"/v1/{parent=projects/*/locations/*}/products\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"parent,product,product_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{parent=projects/*/locations/*}/products\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"parent,product,product_id\"}]},\"ListProducts\":{\"requestType\":\"ListProductsRequest\",\"responseType\":\"ListProductsResponse\",\"options\":{\"(google.api.http).get\":\"/v1/{parent=projects/*/locations/*}/products\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{parent=projects/*/locations/*}/products\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProduct\":{\"requestType\":\"GetProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).get\":\"/v1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProduct\":{\"requestType\":\"UpdateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).patch\":\"/v1/{product.name=projects/*/locations/*/products/*}\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"product,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1/{product.name=projects/*/locations/*/products/*}\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"product,update_mask\"}]},\"DeleteProduct\":{\"requestType\":\"DeleteProductRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateReferenceImage\":{\"requestType\":\"CreateReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).post\":\"/v1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.http).body\":\"reference_image\",\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"body\":\"reference_image\"}},{\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"}]},\"DeleteReferenceImage\":{\"requestType\":\"DeleteReferenceImageRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ListReferenceImages\":{\"requestType\":\"ListReferenceImagesRequest\",\"responseType\":\"ListReferenceImagesResponse\",\"options\":{\"(google.api.http).get\":\"/v1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{parent=projects/*/locations/*/products/*}/referenceImages\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetReferenceImage\":{\"requestType\":\"GetReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).get\":\"/v1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"AddProductToProductSet\":{\"requestType\":\"AddProductToProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"RemoveProductFromProductSet\":{\"requestType\":\"RemoveProductFromProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"ListProductsInProductSet\":{\"requestType\":\"ListProductsInProductSetRequest\",\"responseType\":\"ListProductsInProductSetResponse\",\"options\":{\"(google.api.http).get\":\"/v1/{name=projects/*/locations/*/productSets/*}/products\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=projects/*/locations/*/productSets/*}/products\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ImportProductSets\":{\"requestType\":\"ImportProductSetsRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1/{parent=projects/*/locations/*}/productSets:import\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"parent,input_config\",\"(google.longrunning.operation_info).response_type\":\"ImportProductSetsResponse\",\"(google.longrunning.operation_info).metadata_type\":\"BatchOperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{parent=projects/*/locations/*}/productSets:import\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"parent,input_config\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"ImportProductSetsResponse\",\"metadata_type\":\"BatchOperationMetadata\"}}]},\"PurgeProducts\":{\"requestType\":\"PurgeProductsRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1/{parent=projects/*/locations/*}/products:purge\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"parent\",\"(google.longrunning.operation_info).response_type\":\"google.protobuf.Empty\",\"(google.longrunning.operation_info).metadata_type\":\"BatchOperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{parent=projects/*/locations/*}/products:purge\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"parent\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"google.protobuf.Empty\",\"metadata_type\":\"BatchOperationMetadata\"}}]}}},\"Product\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/Product\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"productCategory\":{\"type\":\"string\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"IMMUTABLE\"}},\"productLabels\":{\"rule\":\"repeated\",\"type\":\"KeyValue\",\"id\":5}},\"nested\":{\"KeyValue\":{\"fields\":{\"key\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2}}}}},\"ProductSet\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ProductSet\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/productSets/{product_set}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}},\"indexError\":{\"type\":\"google.rpc.Status\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}}}},\"ReferenceImage\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ReferenceImage\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"uri\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"boundingPolys\":{\"rule\":\"repeated\",\"type\":\"BoundingPoly\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"CreateProductRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"product\":{\"type\":\"Product\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productId\":{\"type\":\"string\",\"id\":3}}},\"ListProductsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"UpdateProductRequest\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"CreateProductSetRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"productSet\":{\"type\":\"ProductSet\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productSetId\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsResponse\":{\"fields\":{\"productSets\":{\"rule\":\"repeated\",\"type\":\"ProductSet\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"UpdateProductSetRequest\":{\"fields\":{\"productSet\":{\"type\":\"ProductSet\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"CreateReferenceImageRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"referenceImage\":{\"type\":\"ReferenceImage\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"referenceImageId\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"nextPageToken\":{\"type\":\"string\",\"id\":3}}},\"GetReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"DeleteReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"AddProductToProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"RemoveProductFromProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"ListProductsInProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsInProductSetResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"ImportProductSetsGcsSource\":{\"fields\":{\"csvFileUri\":{\"type\":\"string\",\"id\":1}}},\"ImportProductSetsInputConfig\":{\"oneofs\":{\"source\":{\"oneof\":[\"gcsSource\"]}},\"fields\":{\"gcsSource\":{\"type\":\"ImportProductSetsGcsSource\",\"id\":1}}},\"ImportProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"inputConfig\":{\"type\":\"ImportProductSetsInputConfig\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"ImportProductSetsResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"statuses\":{\"rule\":\"repeated\",\"type\":\"google.rpc.Status\",\"id\":2}}},\"BatchOperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"submitTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"endTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"PROCESSING\":1,\"SUCCESSFUL\":2,\"FAILED\":3,\"CANCELLED\":4}}}},\"ProductSetPurgeConfig\":{\"fields\":{\"productSetId\":{\"type\":\"string\",\"id\":1}}},\"PurgeProductsRequest\":{\"oneofs\":{\"target\":{\"oneof\":[\"productSetPurgeConfig\",\"deleteOrphanProducts\"]}},\"fields\":{\"productSetPurgeConfig\":{\"type\":\"ProductSetPurgeConfig\",\"id\":2},\"deleteOrphanProducts\":{\"type\":\"bool\",\"id\":3},\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"force\":{\"type\":\"bool\",\"id\":4}}},\"TextAnnotation\":{\"fields\":{\"pages\":{\"rule\":\"repeated\",\"type\":\"Page\",\"id\":1},\"text\":{\"type\":\"string\",\"id\":2}},\"nested\":{\"DetectedLanguage\":{\"fields\":{\"languageCode\":{\"type\":\"string\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"DetectedBreak\":{\"fields\":{\"type\":{\"type\":\"BreakType\",\"id\":1},\"isPrefix\":{\"type\":\"bool\",\"id\":2}},\"nested\":{\"BreakType\":{\"values\":{\"UNKNOWN\":0,\"SPACE\":1,\"SURE_SPACE\":2,\"EOL_SURE_SPACE\":3,\"HYPHEN\":4,\"LINE_BREAK\":5}}}},\"TextProperty\":{\"fields\":{\"detectedLanguages\":{\"rule\":\"repeated\",\"type\":\"DetectedLanguage\",\"id\":1},\"detectedBreak\":{\"type\":\"DetectedBreak\",\"id\":2}}}}},\"Page\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"width\":{\"type\":\"int32\",\"id\":2},\"height\":{\"type\":\"int32\",\"id\":3},\"blocks\":{\"rule\":\"repeated\",\"type\":\"Block\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}}},\"Block\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"paragraphs\":{\"rule\":\"repeated\",\"type\":\"Paragraph\",\"id\":3},\"blockType\":{\"type\":\"BlockType\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}},\"nested\":{\"BlockType\":{\"values\":{\"UNKNOWN\":0,\"TEXT\":1,\"TABLE\":2,\"PICTURE\":3,\"RULER\":4,\"BARCODE\":5}}}},\"Paragraph\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"words\":{\"rule\":\"repeated\",\"type\":\"Word\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Word\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"symbols\":{\"rule\":\"repeated\",\"type\":\"Symbol\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Symbol\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"text\":{\"type\":\"string\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"WebDetection\":{\"fields\":{\"webEntities\":{\"rule\":\"repeated\",\"type\":\"WebEntity\",\"id\":1},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":2},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":3},\"pagesWithMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebPage\",\"id\":4},\"visuallySimilarImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":6},\"bestGuessLabels\":{\"rule\":\"repeated\",\"type\":\"WebLabel\",\"id\":8}},\"nested\":{\"WebEntity\":{\"fields\":{\"entityId\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"WebImage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2}}},\"WebPage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pageTitle\":{\"type\":\"string\",\"id\":3},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":4},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":5}}},\"WebLabel\":{\"fields\":{\"label\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2}}}}}}},\"v1p1beta1\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"cloud.google.com/go/vision/v2/apiv1p1beta1/visionpb;visionpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"WebDetectionProto\",\"java_package\":\"com.google.cloud.vision.v1p1beta1\"},\"nested\":{\"Vertex\":{\"fields\":{\"x\":{\"type\":\"int32\",\"id\":1},\"y\":{\"type\":\"int32\",\"id\":2}}},\"BoundingPoly\":{\"fields\":{\"vertices\":{\"rule\":\"repeated\",\"type\":\"Vertex\",\"id\":1}}},\"Position\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2},\"z\":{\"type\":\"float\",\"id\":3}}},\"ImageAnnotator\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"BatchAnnotateImages\":{\"requestType\":\"BatchAnnotateImagesRequest\",\"responseType\":\"BatchAnnotateImagesResponse\",\"options\":{\"(google.api.http).post\":\"/v1p1beta1/images:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p1beta1/images:annotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"}]}}},\"Feature\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":1},\"maxResults\":{\"type\":\"int32\",\"id\":2},\"model\":{\"type\":\"string\",\"id\":3}},\"nested\":{\"Type\":{\"values\":{\"TYPE_UNSPECIFIED\":0,\"FACE_DETECTION\":1,\"LANDMARK_DETECTION\":2,\"LOGO_DETECTION\":3,\"LABEL_DETECTION\":4,\"TEXT_DETECTION\":5,\"DOCUMENT_TEXT_DETECTION\":11,\"SAFE_SEARCH_DETECTION\":6,\"IMAGE_PROPERTIES\":7,\"CROP_HINTS\":9,\"WEB_DETECTION\":10}}}},\"ImageSource\":{\"fields\":{\"gcsImageUri\":{\"type\":\"string\",\"id\":1},\"imageUri\":{\"type\":\"string\",\"id\":2}}},\"Image\":{\"fields\":{\"content\":{\"type\":\"bytes\",\"id\":1},\"source\":{\"type\":\"ImageSource\",\"id\":2}}},\"FaceAnnotation\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"fdBoundingPoly\":{\"type\":\"BoundingPoly\",\"id\":2},\"landmarks\":{\"rule\":\"repeated\",\"type\":\"Landmark\",\"id\":3},\"rollAngle\":{\"type\":\"float\",\"id\":4},\"panAngle\":{\"type\":\"float\",\"id\":5},\"tiltAngle\":{\"type\":\"float\",\"id\":6},\"detectionConfidence\":{\"type\":\"float\",\"id\":7},\"landmarkingConfidence\":{\"type\":\"float\",\"id\":8},\"joyLikelihood\":{\"type\":\"Likelihood\",\"id\":9},\"sorrowLikelihood\":{\"type\":\"Likelihood\",\"id\":10},\"angerLikelihood\":{\"type\":\"Likelihood\",\"id\":11},\"surpriseLikelihood\":{\"type\":\"Likelihood\",\"id\":12},\"underExposedLikelihood\":{\"type\":\"Likelihood\",\"id\":13},\"blurredLikelihood\":{\"type\":\"Likelihood\",\"id\":14},\"headwearLikelihood\":{\"type\":\"Likelihood\",\"id\":15}},\"nested\":{\"Landmark\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":3},\"position\":{\"type\":\"Position\",\"id\":4}},\"nested\":{\"Type\":{\"values\":{\"UNKNOWN_LANDMARK\":0,\"LEFT_EYE\":1,\"RIGHT_EYE\":2,\"LEFT_OF_LEFT_EYEBROW\":3,\"RIGHT_OF_LEFT_EYEBROW\":4,\"LEFT_OF_RIGHT_EYEBROW\":5,\"RIGHT_OF_RIGHT_EYEBROW\":6,\"MIDPOINT_BETWEEN_EYES\":7,\"NOSE_TIP\":8,\"UPPER_LIP\":9,\"LOWER_LIP\":10,\"MOUTH_LEFT\":11,\"MOUTH_RIGHT\":12,\"MOUTH_CENTER\":13,\"NOSE_BOTTOM_RIGHT\":14,\"NOSE_BOTTOM_LEFT\":15,\"NOSE_BOTTOM_CENTER\":16,\"LEFT_EYE_TOP_BOUNDARY\":17,\"LEFT_EYE_RIGHT_CORNER\":18,\"LEFT_EYE_BOTTOM_BOUNDARY\":19,\"LEFT_EYE_LEFT_CORNER\":20,\"RIGHT_EYE_TOP_BOUNDARY\":21,\"RIGHT_EYE_RIGHT_CORNER\":22,\"RIGHT_EYE_BOTTOM_BOUNDARY\":23,\"RIGHT_EYE_LEFT_CORNER\":24,\"LEFT_EYEBROW_UPPER_MIDPOINT\":25,\"RIGHT_EYEBROW_UPPER_MIDPOINT\":26,\"LEFT_EAR_TRAGION\":27,\"RIGHT_EAR_TRAGION\":28,\"LEFT_EYE_PUPIL\":29,\"RIGHT_EYE_PUPIL\":30,\"FOREHEAD_GLABELLA\":31,\"CHIN_GNATHION\":32,\"CHIN_LEFT_GONION\":33,\"CHIN_RIGHT_GONION\":34}}}}}},\"LocationInfo\":{\"fields\":{\"latLng\":{\"type\":\"google.type.LatLng\",\"id\":1}}},\"Property\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2},\"uint64Value\":{\"type\":\"uint64\",\"id\":3}}},\"EntityAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"locale\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5},\"topicality\":{\"type\":\"float\",\"id\":6},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":7},\"locations\":{\"rule\":\"repeated\",\"type\":\"LocationInfo\",\"id\":8},\"properties\":{\"rule\":\"repeated\",\"type\":\"Property\",\"id\":9}}},\"SafeSearchAnnotation\":{\"fields\":{\"adult\":{\"type\":\"Likelihood\",\"id\":1},\"spoof\":{\"type\":\"Likelihood\",\"id\":2},\"medical\":{\"type\":\"Likelihood\",\"id\":3},\"violence\":{\"type\":\"Likelihood\",\"id\":4},\"racy\":{\"type\":\"Likelihood\",\"id\":9}}},\"LatLongRect\":{\"fields\":{\"minLatLng\":{\"type\":\"google.type.LatLng\",\"id\":1},\"maxLatLng\":{\"type\":\"google.type.LatLng\",\"id\":2}}},\"ColorInfo\":{\"fields\":{\"color\":{\"type\":\"google.type.Color\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pixelFraction\":{\"type\":\"float\",\"id\":3}}},\"DominantColorsAnnotation\":{\"fields\":{\"colors\":{\"rule\":\"repeated\",\"type\":\"ColorInfo\",\"id\":1}}},\"ImageProperties\":{\"fields\":{\"dominantColors\":{\"type\":\"DominantColorsAnnotation\",\"id\":1}}},\"CropHint\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2},\"importanceFraction\":{\"type\":\"float\",\"id\":3}}},\"CropHintsAnnotation\":{\"fields\":{\"cropHints\":{\"rule\":\"repeated\",\"type\":\"CropHint\",\"id\":1}}},\"CropHintsParams\":{\"fields\":{\"aspectRatios\":{\"rule\":\"repeated\",\"type\":\"float\",\"id\":1}}},\"WebDetectionParams\":{\"fields\":{\"includeGeoResults\":{\"type\":\"bool\",\"id\":2}}},\"TextDetectionParams\":{\"fields\":{\"enableTextDetectionConfidenceScore\":{\"type\":\"bool\",\"id\":9},\"advancedOcrOptions\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":11}}},\"ImageContext\":{\"fields\":{\"latLongRect\":{\"type\":\"LatLongRect\",\"id\":1},\"languageHints\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"cropHintsParams\":{\"type\":\"CropHintsParams\",\"id\":4},\"webDetectionParams\":{\"type\":\"WebDetectionParams\",\"id\":6},\"textDetectionParams\":{\"type\":\"TextDetectionParams\",\"id\":12}}},\"AnnotateImageRequest\":{\"fields\":{\"image\":{\"type\":\"Image\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3}}},\"AnnotateImageResponse\":{\"fields\":{\"faceAnnotations\":{\"rule\":\"repeated\",\"type\":\"FaceAnnotation\",\"id\":1},\"landmarkAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":2},\"logoAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":3},\"labelAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":4},\"textAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":5},\"fullTextAnnotation\":{\"type\":\"TextAnnotation\",\"id\":12},\"safeSearchAnnotation\":{\"type\":\"SafeSearchAnnotation\",\"id\":6},\"imagePropertiesAnnotation\":{\"type\":\"ImageProperties\",\"id\":8},\"cropHintsAnnotation\":{\"type\":\"CropHintsAnnotation\",\"id\":11},\"webDetection\":{\"type\":\"WebDetection\",\"id\":13},\"error\":{\"type\":\"google.rpc.Status\",\"id\":9}}},\"BatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"BatchAnnotateImagesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":1}}},\"Likelihood\":{\"values\":{\"UNKNOWN\":0,\"VERY_UNLIKELY\":1,\"UNLIKELY\":2,\"POSSIBLE\":3,\"LIKELY\":4,\"VERY_LIKELY\":5}},\"TextAnnotation\":{\"fields\":{\"pages\":{\"rule\":\"repeated\",\"type\":\"Page\",\"id\":1},\"text\":{\"type\":\"string\",\"id\":2}},\"nested\":{\"DetectedLanguage\":{\"fields\":{\"languageCode\":{\"type\":\"string\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"DetectedBreak\":{\"fields\":{\"type\":{\"type\":\"BreakType\",\"id\":1},\"isPrefix\":{\"type\":\"bool\",\"id\":2}},\"nested\":{\"BreakType\":{\"values\":{\"UNKNOWN\":0,\"SPACE\":1,\"SURE_SPACE\":2,\"EOL_SURE_SPACE\":3,\"HYPHEN\":4,\"LINE_BREAK\":5}}}},\"TextProperty\":{\"fields\":{\"detectedLanguages\":{\"rule\":\"repeated\",\"type\":\"DetectedLanguage\",\"id\":1},\"detectedBreak\":{\"type\":\"DetectedBreak\",\"id\":2}}}}},\"Page\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"width\":{\"type\":\"int32\",\"id\":2},\"height\":{\"type\":\"int32\",\"id\":3},\"blocks\":{\"rule\":\"repeated\",\"type\":\"Block\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}}},\"Block\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"paragraphs\":{\"rule\":\"repeated\",\"type\":\"Paragraph\",\"id\":3},\"blockType\":{\"type\":\"BlockType\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}},\"nested\":{\"BlockType\":{\"values\":{\"UNKNOWN\":0,\"TEXT\":1,\"TABLE\":2,\"PICTURE\":3,\"RULER\":4,\"BARCODE\":5}}}},\"Paragraph\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"words\":{\"rule\":\"repeated\",\"type\":\"Word\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Word\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"symbols\":{\"rule\":\"repeated\",\"type\":\"Symbol\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Symbol\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"text\":{\"type\":\"string\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"WebDetection\":{\"fields\":{\"webEntities\":{\"rule\":\"repeated\",\"type\":\"WebEntity\",\"id\":1},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":2},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":3},\"pagesWithMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebPage\",\"id\":4},\"visuallySimilarImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":6},\"bestGuessLabels\":{\"rule\":\"repeated\",\"type\":\"WebLabel\",\"id\":8}},\"nested\":{\"WebEntity\":{\"fields\":{\"entityId\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"WebImage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2}}},\"WebPage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pageTitle\":{\"type\":\"string\",\"id\":3},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":4},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":5}}},\"WebLabel\":{\"fields\":{\"label\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2}}}}}}},\"v1p2beta1\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"cloud.google.com/go/vision/apiv1p2beta1/visionpb;visionpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"WebDetectionProto\",\"java_package\":\"com.google.cloud.vision.v1p2beta1\"},\"nested\":{\"Vertex\":{\"fields\":{\"x\":{\"type\":\"int32\",\"id\":1},\"y\":{\"type\":\"int32\",\"id\":2}}},\"NormalizedVertex\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2}}},\"BoundingPoly\":{\"fields\":{\"vertices\":{\"rule\":\"repeated\",\"type\":\"Vertex\",\"id\":1},\"normalizedVertices\":{\"rule\":\"repeated\",\"type\":\"NormalizedVertex\",\"id\":2}}},\"Position\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2},\"z\":{\"type\":\"float\",\"id\":3}}},\"ImageAnnotator\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"BatchAnnotateImages\":{\"requestType\":\"BatchAnnotateImagesRequest\",\"responseType\":\"BatchAnnotateImagesResponse\",\"options\":{\"(google.api.http).post\":\"/v1p2beta1/images:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p2beta1/images:annotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"}]},\"AsyncBatchAnnotateFiles\":{\"requestType\":\"AsyncBatchAnnotateFilesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p2beta1/files:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateFilesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p2beta1/files:asyncBatchAnnotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateFilesResponse\",\"metadata_type\":\"OperationMetadata\"}}]}}},\"Feature\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":1},\"maxResults\":{\"type\":\"int32\",\"id\":2},\"model\":{\"type\":\"string\",\"id\":3}},\"nested\":{\"Type\":{\"values\":{\"TYPE_UNSPECIFIED\":0,\"FACE_DETECTION\":1,\"LANDMARK_DETECTION\":2,\"LOGO_DETECTION\":3,\"LABEL_DETECTION\":4,\"TEXT_DETECTION\":5,\"DOCUMENT_TEXT_DETECTION\":11,\"SAFE_SEARCH_DETECTION\":6,\"IMAGE_PROPERTIES\":7,\"CROP_HINTS\":9,\"WEB_DETECTION\":10}}}},\"ImageSource\":{\"fields\":{\"gcsImageUri\":{\"type\":\"string\",\"id\":1},\"imageUri\":{\"type\":\"string\",\"id\":2}}},\"Image\":{\"fields\":{\"content\":{\"type\":\"bytes\",\"id\":1},\"source\":{\"type\":\"ImageSource\",\"id\":2}}},\"FaceAnnotation\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"fdBoundingPoly\":{\"type\":\"BoundingPoly\",\"id\":2},\"landmarks\":{\"rule\":\"repeated\",\"type\":\"Landmark\",\"id\":3},\"rollAngle\":{\"type\":\"float\",\"id\":4},\"panAngle\":{\"type\":\"float\",\"id\":5},\"tiltAngle\":{\"type\":\"float\",\"id\":6},\"detectionConfidence\":{\"type\":\"float\",\"id\":7},\"landmarkingConfidence\":{\"type\":\"float\",\"id\":8},\"joyLikelihood\":{\"type\":\"Likelihood\",\"id\":9},\"sorrowLikelihood\":{\"type\":\"Likelihood\",\"id\":10},\"angerLikelihood\":{\"type\":\"Likelihood\",\"id\":11},\"surpriseLikelihood\":{\"type\":\"Likelihood\",\"id\":12},\"underExposedLikelihood\":{\"type\":\"Likelihood\",\"id\":13},\"blurredLikelihood\":{\"type\":\"Likelihood\",\"id\":14},\"headwearLikelihood\":{\"type\":\"Likelihood\",\"id\":15}},\"nested\":{\"Landmark\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":3},\"position\":{\"type\":\"Position\",\"id\":4}},\"nested\":{\"Type\":{\"values\":{\"UNKNOWN_LANDMARK\":0,\"LEFT_EYE\":1,\"RIGHT_EYE\":2,\"LEFT_OF_LEFT_EYEBROW\":3,\"RIGHT_OF_LEFT_EYEBROW\":4,\"LEFT_OF_RIGHT_EYEBROW\":5,\"RIGHT_OF_RIGHT_EYEBROW\":6,\"MIDPOINT_BETWEEN_EYES\":7,\"NOSE_TIP\":8,\"UPPER_LIP\":9,\"LOWER_LIP\":10,\"MOUTH_LEFT\":11,\"MOUTH_RIGHT\":12,\"MOUTH_CENTER\":13,\"NOSE_BOTTOM_RIGHT\":14,\"NOSE_BOTTOM_LEFT\":15,\"NOSE_BOTTOM_CENTER\":16,\"LEFT_EYE_TOP_BOUNDARY\":17,\"LEFT_EYE_RIGHT_CORNER\":18,\"LEFT_EYE_BOTTOM_BOUNDARY\":19,\"LEFT_EYE_LEFT_CORNER\":20,\"RIGHT_EYE_TOP_BOUNDARY\":21,\"RIGHT_EYE_RIGHT_CORNER\":22,\"RIGHT_EYE_BOTTOM_BOUNDARY\":23,\"RIGHT_EYE_LEFT_CORNER\":24,\"LEFT_EYEBROW_UPPER_MIDPOINT\":25,\"RIGHT_EYEBROW_UPPER_MIDPOINT\":26,\"LEFT_EAR_TRAGION\":27,\"RIGHT_EAR_TRAGION\":28,\"LEFT_EYE_PUPIL\":29,\"RIGHT_EYE_PUPIL\":30,\"FOREHEAD_GLABELLA\":31,\"CHIN_GNATHION\":32,\"CHIN_LEFT_GONION\":33,\"CHIN_RIGHT_GONION\":34}}}}}},\"LocationInfo\":{\"fields\":{\"latLng\":{\"type\":\"google.type.LatLng\",\"id\":1}}},\"Property\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2},\"uint64Value\":{\"type\":\"uint64\",\"id\":3}}},\"EntityAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"locale\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5},\"topicality\":{\"type\":\"float\",\"id\":6},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":7},\"locations\":{\"rule\":\"repeated\",\"type\":\"LocationInfo\",\"id\":8},\"properties\":{\"rule\":\"repeated\",\"type\":\"Property\",\"id\":9}}},\"SafeSearchAnnotation\":{\"fields\":{\"adult\":{\"type\":\"Likelihood\",\"id\":1},\"spoof\":{\"type\":\"Likelihood\",\"id\":2},\"medical\":{\"type\":\"Likelihood\",\"id\":3},\"violence\":{\"type\":\"Likelihood\",\"id\":4},\"racy\":{\"type\":\"Likelihood\",\"id\":9}}},\"LatLongRect\":{\"fields\":{\"minLatLng\":{\"type\":\"google.type.LatLng\",\"id\":1},\"maxLatLng\":{\"type\":\"google.type.LatLng\",\"id\":2}}},\"ColorInfo\":{\"fields\":{\"color\":{\"type\":\"google.type.Color\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pixelFraction\":{\"type\":\"float\",\"id\":3}}},\"DominantColorsAnnotation\":{\"fields\":{\"colors\":{\"rule\":\"repeated\",\"type\":\"ColorInfo\",\"id\":1}}},\"ImageProperties\":{\"fields\":{\"dominantColors\":{\"type\":\"DominantColorsAnnotation\",\"id\":1}}},\"CropHint\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2},\"importanceFraction\":{\"type\":\"float\",\"id\":3}}},\"CropHintsAnnotation\":{\"fields\":{\"cropHints\":{\"rule\":\"repeated\",\"type\":\"CropHint\",\"id\":1}}},\"CropHintsParams\":{\"fields\":{\"aspectRatios\":{\"rule\":\"repeated\",\"type\":\"float\",\"id\":1}}},\"WebDetectionParams\":{\"fields\":{\"includeGeoResults\":{\"type\":\"bool\",\"id\":2}}},\"TextDetectionParams\":{\"fields\":{\"enableTextDetectionConfidenceScore\":{\"type\":\"bool\",\"id\":9},\"advancedOcrOptions\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":11}}},\"ImageContext\":{\"fields\":{\"latLongRect\":{\"type\":\"LatLongRect\",\"id\":1},\"languageHints\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"cropHintsParams\":{\"type\":\"CropHintsParams\",\"id\":4},\"webDetectionParams\":{\"type\":\"WebDetectionParams\",\"id\":6},\"textDetectionParams\":{\"type\":\"TextDetectionParams\",\"id\":12}}},\"AnnotateImageRequest\":{\"fields\":{\"image\":{\"type\":\"Image\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3}}},\"ImageAnnotationContext\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1},\"pageNumber\":{\"type\":\"int32\",\"id\":2}}},\"AnnotateImageResponse\":{\"fields\":{\"faceAnnotations\":{\"rule\":\"repeated\",\"type\":\"FaceAnnotation\",\"id\":1},\"landmarkAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":2},\"logoAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":3},\"labelAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":4},\"textAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":5},\"fullTextAnnotation\":{\"type\":\"TextAnnotation\",\"id\":12},\"safeSearchAnnotation\":{\"type\":\"SafeSearchAnnotation\",\"id\":6},\"imagePropertiesAnnotation\":{\"type\":\"ImageProperties\",\"id\":8},\"cropHintsAnnotation\":{\"type\":\"CropHintsAnnotation\",\"id\":11},\"webDetection\":{\"type\":\"WebDetection\",\"id\":13},\"error\":{\"type\":\"google.rpc.Status\",\"id\":9},\"context\":{\"type\":\"ImageAnnotationContext\",\"id\":21}}},\"AnnotateFileResponse\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":2}}},\"BatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"BatchAnnotateImagesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":1}}},\"AsyncAnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":4}}},\"AsyncAnnotateFileResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"AsyncBatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileResponse\",\"id\":1}}},\"InputConfig\":{\"fields\":{\"gcsSource\":{\"type\":\"GcsSource\",\"id\":1},\"mimeType\":{\"type\":\"string\",\"id\":2}}},\"OutputConfig\":{\"fields\":{\"gcsDestination\":{\"type\":\"GcsDestination\",\"id\":1},\"batchSize\":{\"type\":\"int32\",\"id\":2}}},\"GcsSource\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"GcsDestination\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"OperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"createTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":5},\"updateTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":6}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"CREATED\":1,\"RUNNING\":2,\"DONE\":3,\"CANCELLED\":4}}}},\"Likelihood\":{\"values\":{\"UNKNOWN\":0,\"VERY_UNLIKELY\":1,\"UNLIKELY\":2,\"POSSIBLE\":3,\"LIKELY\":4,\"VERY_LIKELY\":5}},\"TextAnnotation\":{\"fields\":{\"pages\":{\"rule\":\"repeated\",\"type\":\"Page\",\"id\":1},\"text\":{\"type\":\"string\",\"id\":2}},\"nested\":{\"DetectedLanguage\":{\"fields\":{\"languageCode\":{\"type\":\"string\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"DetectedBreak\":{\"fields\":{\"type\":{\"type\":\"BreakType\",\"id\":1},\"isPrefix\":{\"type\":\"bool\",\"id\":2}},\"nested\":{\"BreakType\":{\"values\":{\"UNKNOWN\":0,\"SPACE\":1,\"SURE_SPACE\":2,\"EOL_SURE_SPACE\":3,\"HYPHEN\":4,\"LINE_BREAK\":5}}}},\"TextProperty\":{\"fields\":{\"detectedLanguages\":{\"rule\":\"repeated\",\"type\":\"DetectedLanguage\",\"id\":1},\"detectedBreak\":{\"type\":\"DetectedBreak\",\"id\":2}}}}},\"Page\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"width\":{\"type\":\"int32\",\"id\":2},\"height\":{\"type\":\"int32\",\"id\":3},\"blocks\":{\"rule\":\"repeated\",\"type\":\"Block\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}}},\"Block\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"paragraphs\":{\"rule\":\"repeated\",\"type\":\"Paragraph\",\"id\":3},\"blockType\":{\"type\":\"BlockType\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}},\"nested\":{\"BlockType\":{\"values\":{\"UNKNOWN\":0,\"TEXT\":1,\"TABLE\":2,\"PICTURE\":3,\"RULER\":4,\"BARCODE\":5}}}},\"Paragraph\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"words\":{\"rule\":\"repeated\",\"type\":\"Word\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Word\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"symbols\":{\"rule\":\"repeated\",\"type\":\"Symbol\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Symbol\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"text\":{\"type\":\"string\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"WebDetection\":{\"fields\":{\"webEntities\":{\"rule\":\"repeated\",\"type\":\"WebEntity\",\"id\":1},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":2},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":3},\"pagesWithMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebPage\",\"id\":4},\"visuallySimilarImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":6},\"bestGuessLabels\":{\"rule\":\"repeated\",\"type\":\"WebLabel\",\"id\":8}},\"nested\":{\"WebEntity\":{\"fields\":{\"entityId\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"WebImage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2}}},\"WebPage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pageTitle\":{\"type\":\"string\",\"id\":3},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":4},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":5}}},\"WebLabel\":{\"fields\":{\"label\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2}}}}}}},\"v1p3beta1\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"cloud.google.com/go/vision/apiv1p3beta1/visionpb;visionpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"WebDetectionProto\",\"java_package\":\"com.google.cloud.vision.v1p3beta1\",\"objc_class_prefix\":\"GCVN\"},\"nested\":{\"Vertex\":{\"fields\":{\"x\":{\"type\":\"int32\",\"id\":1},\"y\":{\"type\":\"int32\",\"id\":2}}},\"NormalizedVertex\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2}}},\"BoundingPoly\":{\"fields\":{\"vertices\":{\"rule\":\"repeated\",\"type\":\"Vertex\",\"id\":1},\"normalizedVertices\":{\"rule\":\"repeated\",\"type\":\"NormalizedVertex\",\"id\":2}}},\"Position\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2},\"z\":{\"type\":\"float\",\"id\":3}}},\"ImageAnnotator\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"BatchAnnotateImages\":{\"requestType\":\"BatchAnnotateImagesRequest\",\"responseType\":\"BatchAnnotateImagesResponse\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/images:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/images:annotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"}]},\"AsyncBatchAnnotateFiles\":{\"requestType\":\"AsyncBatchAnnotateFilesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/files:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateFilesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/files:asyncBatchAnnotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateFilesResponse\",\"metadata_type\":\"OperationMetadata\"}}]}}},\"Feature\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":1},\"maxResults\":{\"type\":\"int32\",\"id\":2},\"model\":{\"type\":\"string\",\"id\":3}},\"nested\":{\"Type\":{\"values\":{\"TYPE_UNSPECIFIED\":0,\"FACE_DETECTION\":1,\"LANDMARK_DETECTION\":2,\"LOGO_DETECTION\":3,\"LABEL_DETECTION\":4,\"TEXT_DETECTION\":5,\"DOCUMENT_TEXT_DETECTION\":11,\"SAFE_SEARCH_DETECTION\":6,\"IMAGE_PROPERTIES\":7,\"CROP_HINTS\":9,\"WEB_DETECTION\":10,\"PRODUCT_SEARCH\":12,\"OBJECT_LOCALIZATION\":19}}}},\"ImageSource\":{\"fields\":{\"gcsImageUri\":{\"type\":\"string\",\"id\":1},\"imageUri\":{\"type\":\"string\",\"id\":2}}},\"Image\":{\"fields\":{\"content\":{\"type\":\"bytes\",\"id\":1},\"source\":{\"type\":\"ImageSource\",\"id\":2}}},\"FaceAnnotation\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"fdBoundingPoly\":{\"type\":\"BoundingPoly\",\"id\":2},\"landmarks\":{\"rule\":\"repeated\",\"type\":\"Landmark\",\"id\":3},\"rollAngle\":{\"type\":\"float\",\"id\":4},\"panAngle\":{\"type\":\"float\",\"id\":5},\"tiltAngle\":{\"type\":\"float\",\"id\":6},\"detectionConfidence\":{\"type\":\"float\",\"id\":7},\"landmarkingConfidence\":{\"type\":\"float\",\"id\":8},\"joyLikelihood\":{\"type\":\"Likelihood\",\"id\":9},\"sorrowLikelihood\":{\"type\":\"Likelihood\",\"id\":10},\"angerLikelihood\":{\"type\":\"Likelihood\",\"id\":11},\"surpriseLikelihood\":{\"type\":\"Likelihood\",\"id\":12},\"underExposedLikelihood\":{\"type\":\"Likelihood\",\"id\":13},\"blurredLikelihood\":{\"type\":\"Likelihood\",\"id\":14},\"headwearLikelihood\":{\"type\":\"Likelihood\",\"id\":15}},\"nested\":{\"Landmark\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":3},\"position\":{\"type\":\"Position\",\"id\":4}},\"nested\":{\"Type\":{\"values\":{\"UNKNOWN_LANDMARK\":0,\"LEFT_EYE\":1,\"RIGHT_EYE\":2,\"LEFT_OF_LEFT_EYEBROW\":3,\"RIGHT_OF_LEFT_EYEBROW\":4,\"LEFT_OF_RIGHT_EYEBROW\":5,\"RIGHT_OF_RIGHT_EYEBROW\":6,\"MIDPOINT_BETWEEN_EYES\":7,\"NOSE_TIP\":8,\"UPPER_LIP\":9,\"LOWER_LIP\":10,\"MOUTH_LEFT\":11,\"MOUTH_RIGHT\":12,\"MOUTH_CENTER\":13,\"NOSE_BOTTOM_RIGHT\":14,\"NOSE_BOTTOM_LEFT\":15,\"NOSE_BOTTOM_CENTER\":16,\"LEFT_EYE_TOP_BOUNDARY\":17,\"LEFT_EYE_RIGHT_CORNER\":18,\"LEFT_EYE_BOTTOM_BOUNDARY\":19,\"LEFT_EYE_LEFT_CORNER\":20,\"RIGHT_EYE_TOP_BOUNDARY\":21,\"RIGHT_EYE_RIGHT_CORNER\":22,\"RIGHT_EYE_BOTTOM_BOUNDARY\":23,\"RIGHT_EYE_LEFT_CORNER\":24,\"LEFT_EYEBROW_UPPER_MIDPOINT\":25,\"RIGHT_EYEBROW_UPPER_MIDPOINT\":26,\"LEFT_EAR_TRAGION\":27,\"RIGHT_EAR_TRAGION\":28,\"LEFT_EYE_PUPIL\":29,\"RIGHT_EYE_PUPIL\":30,\"FOREHEAD_GLABELLA\":31,\"CHIN_GNATHION\":32,\"CHIN_LEFT_GONION\":33,\"CHIN_RIGHT_GONION\":34}}}}}},\"LocationInfo\":{\"fields\":{\"latLng\":{\"type\":\"google.type.LatLng\",\"id\":1}}},\"Property\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2},\"uint64Value\":{\"type\":\"uint64\",\"id\":3}}},\"EntityAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"locale\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5},\"topicality\":{\"type\":\"float\",\"id\":6},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":7},\"locations\":{\"rule\":\"repeated\",\"type\":\"LocationInfo\",\"id\":8},\"properties\":{\"rule\":\"repeated\",\"type\":\"Property\",\"id\":9}}},\"LocalizedObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":5}}},\"SafeSearchAnnotation\":{\"fields\":{\"adult\":{\"type\":\"Likelihood\",\"id\":1},\"spoof\":{\"type\":\"Likelihood\",\"id\":2},\"medical\":{\"type\":\"Likelihood\",\"id\":3},\"violence\":{\"type\":\"Likelihood\",\"id\":4},\"racy\":{\"type\":\"Likelihood\",\"id\":9}}},\"LatLongRect\":{\"fields\":{\"minLatLng\":{\"type\":\"google.type.LatLng\",\"id\":1},\"maxLatLng\":{\"type\":\"google.type.LatLng\",\"id\":2}}},\"ColorInfo\":{\"fields\":{\"color\":{\"type\":\"google.type.Color\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pixelFraction\":{\"type\":\"float\",\"id\":3}}},\"DominantColorsAnnotation\":{\"fields\":{\"colors\":{\"rule\":\"repeated\",\"type\":\"ColorInfo\",\"id\":1}}},\"ImageProperties\":{\"fields\":{\"dominantColors\":{\"type\":\"DominantColorsAnnotation\",\"id\":1}}},\"CropHint\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2},\"importanceFraction\":{\"type\":\"float\",\"id\":3}}},\"CropHintsAnnotation\":{\"fields\":{\"cropHints\":{\"rule\":\"repeated\",\"type\":\"CropHint\",\"id\":1}}},\"CropHintsParams\":{\"fields\":{\"aspectRatios\":{\"rule\":\"repeated\",\"type\":\"float\",\"id\":1}}},\"WebDetectionParams\":{\"fields\":{\"includeGeoResults\":{\"type\":\"bool\",\"id\":2}}},\"TextDetectionParams\":{\"fields\":{\"enableTextDetectionConfidenceScore\":{\"type\":\"bool\",\"id\":9},\"advancedOcrOptions\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":11}}},\"ImageContext\":{\"fields\":{\"latLongRect\":{\"type\":\"LatLongRect\",\"id\":1},\"languageHints\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"cropHintsParams\":{\"type\":\"CropHintsParams\",\"id\":4},\"productSearchParams\":{\"type\":\"google.cloud.vision.v1p3beta1.ProductSearchParams\",\"id\":5},\"webDetectionParams\":{\"type\":\"WebDetectionParams\",\"id\":6},\"textDetectionParams\":{\"type\":\"TextDetectionParams\",\"id\":12}}},\"AnnotateImageRequest\":{\"fields\":{\"image\":{\"type\":\"Image\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3}}},\"ImageAnnotationContext\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1},\"pageNumber\":{\"type\":\"int32\",\"id\":2}}},\"AnnotateImageResponse\":{\"fields\":{\"faceAnnotations\":{\"rule\":\"repeated\",\"type\":\"FaceAnnotation\",\"id\":1},\"landmarkAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":2},\"logoAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":3},\"labelAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":4},\"localizedObjectAnnotations\":{\"rule\":\"repeated\",\"type\":\"LocalizedObjectAnnotation\",\"id\":22},\"textAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":5},\"fullTextAnnotation\":{\"type\":\"TextAnnotation\",\"id\":12},\"safeSearchAnnotation\":{\"type\":\"SafeSearchAnnotation\",\"id\":6},\"imagePropertiesAnnotation\":{\"type\":\"ImageProperties\",\"id\":8},\"cropHintsAnnotation\":{\"type\":\"CropHintsAnnotation\",\"id\":11},\"webDetection\":{\"type\":\"WebDetection\",\"id\":13},\"productSearchResults\":{\"type\":\"google.cloud.vision.v1p3beta1.ProductSearchResults\",\"id\":14},\"error\":{\"type\":\"google.rpc.Status\",\"id\":9},\"context\":{\"type\":\"ImageAnnotationContext\",\"id\":21}}},\"AnnotateFileResponse\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":2}}},\"BatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"BatchAnnotateImagesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":1}}},\"AsyncAnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":4}}},\"AsyncAnnotateFileResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"AsyncBatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileResponse\",\"id\":1}}},\"InputConfig\":{\"fields\":{\"gcsSource\":{\"type\":\"GcsSource\",\"id\":1},\"mimeType\":{\"type\":\"string\",\"id\":2}}},\"OutputConfig\":{\"fields\":{\"gcsDestination\":{\"type\":\"GcsDestination\",\"id\":1},\"batchSize\":{\"type\":\"int32\",\"id\":2}}},\"GcsSource\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"GcsDestination\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"Likelihood\":{\"values\":{\"UNKNOWN\":0,\"VERY_UNLIKELY\":1,\"UNLIKELY\":2,\"POSSIBLE\":3,\"LIKELY\":4,\"VERY_LIKELY\":5}},\"OperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"createTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":5},\"updateTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":6}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"CREATED\":1,\"RUNNING\":2,\"DONE\":3,\"CANCELLED\":4}}}},\"ProductSearchParams\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":9},\"productSet\":{\"type\":\"string\",\"id\":6,\"options\":{\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"productCategories\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":7},\"filter\":{\"type\":\"string\",\"id\":8}}},\"ProductSearchResults\":{\"fields\":{\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":5},\"productGroupedResults\":{\"rule\":\"repeated\",\"type\":\"GroupedResult\",\"id\":6}},\"nested\":{\"Result\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"image\":{\"type\":\"string\",\"id\":3}}},\"ObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4}}},\"GroupedResult\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":2},\"objectAnnotations\":{\"rule\":\"repeated\",\"type\":\"ObjectAnnotation\",\"id\":3}}}}},\"ProductSearch\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"CreateProductSet\":{\"requestType\":\"CreateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"}]},\"ListProductSets\":{\"requestType\":\"ListProductSetsRequest\",\"responseType\":\"ListProductSetsResponse\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProductSet\":{\"requestType\":\"GetProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProductSet\":{\"requestType\":\"UpdateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).patch\":\"/v1p3beta1/{product_set.name=projects/*/locations/*/productSets/*}\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"product_set,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1p3beta1/{product_set.name=projects/*/locations/*/productSets/*}\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"product_set,update_mask\"}]},\"DeleteProductSet\":{\"requestType\":\"DeleteProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateProduct\":{\"requestType\":\"CreateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{parent=projects/*/locations/*}/products\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"parent,product,product_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{parent=projects/*/locations/*}/products\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"parent,product,product_id\"}]},\"ListProducts\":{\"requestType\":\"ListProductsRequest\",\"responseType\":\"ListProductsResponse\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{parent=projects/*/locations/*}/products\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{parent=projects/*/locations/*}/products\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProduct\":{\"requestType\":\"GetProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProduct\":{\"requestType\":\"UpdateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).patch\":\"/v1p3beta1/{product.name=projects/*/locations/*/products/*}\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"product,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1p3beta1/{product.name=projects/*/locations/*/products/*}\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"product,update_mask\"}]},\"DeleteProduct\":{\"requestType\":\"DeleteProductRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p3beta1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p3beta1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateReferenceImage\":{\"requestType\":\"CreateReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.http).body\":\"reference_image\",\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"body\":\"reference_image\"}},{\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"}]},\"DeleteReferenceImage\":{\"requestType\":\"DeleteReferenceImageRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p3beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p3beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ListReferenceImages\":{\"requestType\":\"ListReferenceImagesRequest\",\"responseType\":\"ListReferenceImagesResponse\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{parent=projects/*/locations/*/products/*}/referenceImages\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetReferenceImage\":{\"requestType\":\"GetReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"AddProductToProductSet\":{\"requestType\":\"AddProductToProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"RemoveProductFromProductSet\":{\"requestType\":\"RemoveProductFromProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"ListProductsInProductSet\":{\"requestType\":\"ListProductsInProductSetRequest\",\"responseType\":\"ListProductsInProductSetResponse\",\"options\":{\"(google.api.http).get\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}/products\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p3beta1/{name=projects/*/locations/*/productSets/*}/products\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ImportProductSets\":{\"requestType\":\"ImportProductSetsRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets:import\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"parent,input_config\",\"(google.longrunning.operation_info).response_type\":\"ImportProductSetsResponse\",\"(google.longrunning.operation_info).metadata_type\":\"BatchOperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p3beta1/{parent=projects/*/locations/*}/productSets:import\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"parent,input_config\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"ImportProductSetsResponse\",\"metadata_type\":\"BatchOperationMetadata\"}}]}}},\"Product\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/Product\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"productCategory\":{\"type\":\"string\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"IMMUTABLE\"}},\"productLabels\":{\"rule\":\"repeated\",\"type\":\"KeyValue\",\"id\":5}},\"nested\":{\"KeyValue\":{\"fields\":{\"key\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2}}}}},\"ProductSet\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ProductSet\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/productSets/{product_set}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}},\"indexError\":{\"type\":\"google.rpc.Status\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}}}},\"ReferenceImage\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ReferenceImage\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"uri\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"boundingPolys\":{\"rule\":\"repeated\",\"type\":\"BoundingPoly\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"CreateProductRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"product\":{\"type\":\"Product\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productId\":{\"type\":\"string\",\"id\":3}}},\"ListProductsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"UpdateProductRequest\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"CreateProductSetRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"productSet\":{\"type\":\"ProductSet\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productSetId\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsResponse\":{\"fields\":{\"productSets\":{\"rule\":\"repeated\",\"type\":\"ProductSet\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"UpdateProductSetRequest\":{\"fields\":{\"productSet\":{\"type\":\"ProductSet\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"CreateReferenceImageRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"referenceImage\":{\"type\":\"ReferenceImage\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"referenceImageId\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"nextPageToken\":{\"type\":\"string\",\"id\":3}}},\"GetReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"DeleteReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"AddProductToProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"RemoveProductFromProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"ListProductsInProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsInProductSetResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"ImportProductSetsGcsSource\":{\"fields\":{\"csvFileUri\":{\"type\":\"string\",\"id\":1}}},\"ImportProductSetsInputConfig\":{\"oneofs\":{\"source\":{\"oneof\":[\"gcsSource\"]}},\"fields\":{\"gcsSource\":{\"type\":\"ImportProductSetsGcsSource\",\"id\":1}}},\"ImportProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"inputConfig\":{\"type\":\"ImportProductSetsInputConfig\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"ImportProductSetsResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"statuses\":{\"rule\":\"repeated\",\"type\":\"google.rpc.Status\",\"id\":2}}},\"BatchOperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"submitTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"endTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"PROCESSING\":1,\"SUCCESSFUL\":2,\"FAILED\":3,\"CANCELLED\":4}}}},\"TextAnnotation\":{\"fields\":{\"pages\":{\"rule\":\"repeated\",\"type\":\"Page\",\"id\":1},\"text\":{\"type\":\"string\",\"id\":2}},\"nested\":{\"DetectedLanguage\":{\"fields\":{\"languageCode\":{\"type\":\"string\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"DetectedBreak\":{\"fields\":{\"type\":{\"type\":\"BreakType\",\"id\":1},\"isPrefix\":{\"type\":\"bool\",\"id\":2}},\"nested\":{\"BreakType\":{\"values\":{\"UNKNOWN\":0,\"SPACE\":1,\"SURE_SPACE\":2,\"EOL_SURE_SPACE\":3,\"HYPHEN\":4,\"LINE_BREAK\":5}}}},\"TextProperty\":{\"fields\":{\"detectedLanguages\":{\"rule\":\"repeated\",\"type\":\"DetectedLanguage\",\"id\":1},\"detectedBreak\":{\"type\":\"DetectedBreak\",\"id\":2}}}}},\"Page\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"width\":{\"type\":\"int32\",\"id\":2},\"height\":{\"type\":\"int32\",\"id\":3},\"blocks\":{\"rule\":\"repeated\",\"type\":\"Block\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}}},\"Block\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"paragraphs\":{\"rule\":\"repeated\",\"type\":\"Paragraph\",\"id\":3},\"blockType\":{\"type\":\"BlockType\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}},\"nested\":{\"BlockType\":{\"values\":{\"UNKNOWN\":0,\"TEXT\":1,\"TABLE\":2,\"PICTURE\":3,\"RULER\":4,\"BARCODE\":5}}}},\"Paragraph\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"words\":{\"rule\":\"repeated\",\"type\":\"Word\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Word\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"symbols\":{\"rule\":\"repeated\",\"type\":\"Symbol\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Symbol\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"text\":{\"type\":\"string\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"WebDetection\":{\"fields\":{\"webEntities\":{\"rule\":\"repeated\",\"type\":\"WebEntity\",\"id\":1},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":2},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":3},\"pagesWithMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebPage\",\"id\":4},\"visuallySimilarImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":6},\"bestGuessLabels\":{\"rule\":\"repeated\",\"type\":\"WebLabel\",\"id\":8}},\"nested\":{\"WebEntity\":{\"fields\":{\"entityId\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"WebImage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2}}},\"WebPage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pageTitle\":{\"type\":\"string\",\"id\":3},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":4},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":5}}},\"WebLabel\":{\"fields\":{\"label\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2}}}}}}},\"v1p4beta1\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"cloud.google.com/go/vision/apiv1p4beta1/visionpb;visionpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"WebDetectionProto\",\"java_package\":\"com.google.cloud.vision.v1p4beta1\",\"objc_class_prefix\":\"GCVN\"},\"nested\":{\"FaceRecognitionParams\":{\"fields\":{\"celebritySet\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":1}}},\"Celebrity\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"FaceRecognitionResult\":{\"fields\":{\"celebrity\":{\"type\":\"Celebrity\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"Vertex\":{\"fields\":{\"x\":{\"type\":\"int32\",\"id\":1},\"y\":{\"type\":\"int32\",\"id\":2}}},\"NormalizedVertex\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2}}},\"BoundingPoly\":{\"fields\":{\"vertices\":{\"rule\":\"repeated\",\"type\":\"Vertex\",\"id\":1},\"normalizedVertices\":{\"rule\":\"repeated\",\"type\":\"NormalizedVertex\",\"id\":2}}},\"Position\":{\"fields\":{\"x\":{\"type\":\"float\",\"id\":1},\"y\":{\"type\":\"float\",\"id\":2},\"z\":{\"type\":\"float\",\"id\":3}}},\"ImageAnnotator\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"BatchAnnotateImages\":{\"requestType\":\"BatchAnnotateImagesRequest\",\"responseType\":\"BatchAnnotateImagesResponse\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/images:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/images:annotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"}]},\"BatchAnnotateFiles\":{\"requestType\":\"BatchAnnotateFilesRequest\",\"responseType\":\"BatchAnnotateFilesResponse\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/files:annotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/files:annotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"}]},\"AsyncBatchAnnotateImages\":{\"requestType\":\"AsyncBatchAnnotateImagesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/images:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests,output_config\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateImagesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/images:asyncBatchAnnotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests,output_config\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateImagesResponse\",\"metadata_type\":\"OperationMetadata\"}}]},\"AsyncBatchAnnotateFiles\":{\"requestType\":\"AsyncBatchAnnotateFilesRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/files:asyncBatchAnnotate\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"requests\",\"(google.longrunning.operation_info).response_type\":\"AsyncBatchAnnotateFilesResponse\",\"(google.longrunning.operation_info).metadata_type\":\"OperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/files:asyncBatchAnnotate\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"requests\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"AsyncBatchAnnotateFilesResponse\",\"metadata_type\":\"OperationMetadata\"}}]}}},\"Feature\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":1},\"maxResults\":{\"type\":\"int32\",\"id\":2},\"model\":{\"type\":\"string\",\"id\":3}},\"nested\":{\"Type\":{\"values\":{\"TYPE_UNSPECIFIED\":0,\"FACE_DETECTION\":1,\"LANDMARK_DETECTION\":2,\"LOGO_DETECTION\":3,\"LABEL_DETECTION\":4,\"TEXT_DETECTION\":5,\"DOCUMENT_TEXT_DETECTION\":11,\"SAFE_SEARCH_DETECTION\":6,\"IMAGE_PROPERTIES\":7,\"CROP_HINTS\":9,\"WEB_DETECTION\":10,\"PRODUCT_SEARCH\":12,\"OBJECT_LOCALIZATION\":19}}}},\"ImageSource\":{\"fields\":{\"gcsImageUri\":{\"type\":\"string\",\"id\":1},\"imageUri\":{\"type\":\"string\",\"id\":2}}},\"Image\":{\"fields\":{\"content\":{\"type\":\"bytes\",\"id\":1},\"source\":{\"type\":\"ImageSource\",\"id\":2}}},\"Likelihood\":{\"values\":{\"UNKNOWN\":0,\"VERY_UNLIKELY\":1,\"UNLIKELY\":2,\"POSSIBLE\":3,\"LIKELY\":4,\"VERY_LIKELY\":5}},\"FaceAnnotation\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"fdBoundingPoly\":{\"type\":\"BoundingPoly\",\"id\":2},\"landmarks\":{\"rule\":\"repeated\",\"type\":\"Landmark\",\"id\":3},\"rollAngle\":{\"type\":\"float\",\"id\":4},\"panAngle\":{\"type\":\"float\",\"id\":5},\"tiltAngle\":{\"type\":\"float\",\"id\":6},\"detectionConfidence\":{\"type\":\"float\",\"id\":7},\"landmarkingConfidence\":{\"type\":\"float\",\"id\":8},\"joyLikelihood\":{\"type\":\"Likelihood\",\"id\":9},\"sorrowLikelihood\":{\"type\":\"Likelihood\",\"id\":10},\"angerLikelihood\":{\"type\":\"Likelihood\",\"id\":11},\"surpriseLikelihood\":{\"type\":\"Likelihood\",\"id\":12},\"underExposedLikelihood\":{\"type\":\"Likelihood\",\"id\":13},\"blurredLikelihood\":{\"type\":\"Likelihood\",\"id\":14},\"headwearLikelihood\":{\"type\":\"Likelihood\",\"id\":15},\"recognitionResult\":{\"rule\":\"repeated\",\"type\":\"FaceRecognitionResult\",\"id\":16}},\"nested\":{\"Landmark\":{\"fields\":{\"type\":{\"type\":\"Type\",\"id\":3},\"position\":{\"type\":\"Position\",\"id\":4}},\"nested\":{\"Type\":{\"values\":{\"UNKNOWN_LANDMARK\":0,\"LEFT_EYE\":1,\"RIGHT_EYE\":2,\"LEFT_OF_LEFT_EYEBROW\":3,\"RIGHT_OF_LEFT_EYEBROW\":4,\"LEFT_OF_RIGHT_EYEBROW\":5,\"RIGHT_OF_RIGHT_EYEBROW\":6,\"MIDPOINT_BETWEEN_EYES\":7,\"NOSE_TIP\":8,\"UPPER_LIP\":9,\"LOWER_LIP\":10,\"MOUTH_LEFT\":11,\"MOUTH_RIGHT\":12,\"MOUTH_CENTER\":13,\"NOSE_BOTTOM_RIGHT\":14,\"NOSE_BOTTOM_LEFT\":15,\"NOSE_BOTTOM_CENTER\":16,\"LEFT_EYE_TOP_BOUNDARY\":17,\"LEFT_EYE_RIGHT_CORNER\":18,\"LEFT_EYE_BOTTOM_BOUNDARY\":19,\"LEFT_EYE_LEFT_CORNER\":20,\"RIGHT_EYE_TOP_BOUNDARY\":21,\"RIGHT_EYE_RIGHT_CORNER\":22,\"RIGHT_EYE_BOTTOM_BOUNDARY\":23,\"RIGHT_EYE_LEFT_CORNER\":24,\"LEFT_EYEBROW_UPPER_MIDPOINT\":25,\"RIGHT_EYEBROW_UPPER_MIDPOINT\":26,\"LEFT_EAR_TRAGION\":27,\"RIGHT_EAR_TRAGION\":28,\"LEFT_EYE_PUPIL\":29,\"RIGHT_EYE_PUPIL\":30,\"FOREHEAD_GLABELLA\":31,\"CHIN_GNATHION\":32,\"CHIN_LEFT_GONION\":33,\"CHIN_RIGHT_GONION\":34}}}}}},\"LocationInfo\":{\"fields\":{\"latLng\":{\"type\":\"google.type.LatLng\",\"id\":1}}},\"Property\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2},\"uint64Value\":{\"type\":\"uint64\",\"id\":3}}},\"EntityAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"locale\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5,\"options\":{\"deprecated\":true}},\"topicality\":{\"type\":\"float\",\"id\":6},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":7},\"locations\":{\"rule\":\"repeated\",\"type\":\"LocationInfo\",\"id\":8},\"properties\":{\"rule\":\"repeated\",\"type\":\"Property\",\"id\":9}}},\"LocalizedObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4},\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":5}}},\"SafeSearchAnnotation\":{\"fields\":{\"adult\":{\"type\":\"Likelihood\",\"id\":1},\"spoof\":{\"type\":\"Likelihood\",\"id\":2},\"medical\":{\"type\":\"Likelihood\",\"id\":3},\"violence\":{\"type\":\"Likelihood\",\"id\":4},\"racy\":{\"type\":\"Likelihood\",\"id\":9}}},\"LatLongRect\":{\"fields\":{\"minLatLng\":{\"type\":\"google.type.LatLng\",\"id\":1},\"maxLatLng\":{\"type\":\"google.type.LatLng\",\"id\":2}}},\"ColorInfo\":{\"fields\":{\"color\":{\"type\":\"google.type.Color\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pixelFraction\":{\"type\":\"float\",\"id\":3}}},\"DominantColorsAnnotation\":{\"fields\":{\"colors\":{\"rule\":\"repeated\",\"type\":\"ColorInfo\",\"id\":1}}},\"ImageProperties\":{\"fields\":{\"dominantColors\":{\"type\":\"DominantColorsAnnotation\",\"id\":1}}},\"CropHint\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2},\"importanceFraction\":{\"type\":\"float\",\"id\":3}}},\"CropHintsAnnotation\":{\"fields\":{\"cropHints\":{\"rule\":\"repeated\",\"type\":\"CropHint\",\"id\":1}}},\"CropHintsParams\":{\"fields\":{\"aspectRatios\":{\"rule\":\"repeated\",\"type\":\"float\",\"id\":1}}},\"WebDetectionParams\":{\"fields\":{\"includeGeoResults\":{\"type\":\"bool\",\"id\":2}}},\"TextDetectionParams\":{\"fields\":{\"enableTextDetectionConfidenceScore\":{\"type\":\"bool\",\"id\":9},\"advancedOcrOptions\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":11}}},\"ImageContext\":{\"fields\":{\"latLongRect\":{\"type\":\"LatLongRect\",\"id\":1},\"languageHints\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"cropHintsParams\":{\"type\":\"CropHintsParams\",\"id\":4},\"faceRecognitionParams\":{\"type\":\"FaceRecognitionParams\",\"id\":10},\"productSearchParams\":{\"type\":\"ProductSearchParams\",\"id\":5},\"webDetectionParams\":{\"type\":\"WebDetectionParams\",\"id\":6},\"textDetectionParams\":{\"type\":\"TextDetectionParams\",\"id\":12}}},\"AnnotateImageRequest\":{\"fields\":{\"image\":{\"type\":\"Image\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3}}},\"ImageAnnotationContext\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1},\"pageNumber\":{\"type\":\"int32\",\"id\":2}}},\"AnnotateImageResponse\":{\"fields\":{\"faceAnnotations\":{\"rule\":\"repeated\",\"type\":\"FaceAnnotation\",\"id\":1},\"landmarkAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":2},\"logoAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":3},\"labelAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":4},\"localizedObjectAnnotations\":{\"rule\":\"repeated\",\"type\":\"LocalizedObjectAnnotation\",\"id\":22},\"textAnnotations\":{\"rule\":\"repeated\",\"type\":\"EntityAnnotation\",\"id\":5},\"fullTextAnnotation\":{\"type\":\"TextAnnotation\",\"id\":12},\"safeSearchAnnotation\":{\"type\":\"SafeSearchAnnotation\",\"id\":6},\"imagePropertiesAnnotation\":{\"type\":\"ImageProperties\",\"id\":8},\"cropHintsAnnotation\":{\"type\":\"CropHintsAnnotation\",\"id\":11},\"webDetection\":{\"type\":\"WebDetection\",\"id\":13},\"productSearchResults\":{\"type\":\"ProductSearchResults\",\"id\":14},\"error\":{\"type\":\"google.rpc.Status\",\"id\":9},\"context\":{\"type\":\"ImageAnnotationContext\",\"id\":21}}},\"BatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"BatchAnnotateImagesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":1}}},\"AnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"pages\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":4}}},\"AnnotateFileResponse\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageResponse\",\"id\":2},\"totalPages\":{\"type\":\"int32\",\"id\":3},\"error\":{\"type\":\"google.rpc.Status\",\"id\":4}}},\"BatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"BatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AnnotateFileResponse\",\"id\":1}}},\"AsyncAnnotateFileRequest\":{\"fields\":{\"inputConfig\":{\"type\":\"InputConfig\",\"id\":1},\"features\":{\"rule\":\"repeated\",\"type\":\"Feature\",\"id\":2},\"imageContext\":{\"type\":\"ImageContext\",\"id\":3},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":4}}},\"AsyncAnnotateFileResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateImagesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AnnotateImageRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"AsyncBatchAnnotateImagesResponse\":{\"fields\":{\"outputConfig\":{\"type\":\"OutputConfig\",\"id\":1}}},\"AsyncBatchAnnotateFilesRequest\":{\"fields\":{\"requests\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileRequest\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"AsyncBatchAnnotateFilesResponse\":{\"fields\":{\"responses\":{\"rule\":\"repeated\",\"type\":\"AsyncAnnotateFileResponse\",\"id\":1}}},\"InputConfig\":{\"fields\":{\"gcsSource\":{\"type\":\"GcsSource\",\"id\":1},\"content\":{\"type\":\"bytes\",\"id\":3},\"mimeType\":{\"type\":\"string\",\"id\":2}}},\"OutputConfig\":{\"fields\":{\"gcsDestination\":{\"type\":\"GcsDestination\",\"id\":1},\"batchSize\":{\"type\":\"int32\",\"id\":2}}},\"GcsSource\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"GcsDestination\":{\"fields\":{\"uri\":{\"type\":\"string\",\"id\":1}}},\"OperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"createTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":5},\"updateTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":6}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"CREATED\":1,\"RUNNING\":2,\"DONE\":3,\"CANCELLED\":4}}}},\"ProductSearchParams\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":9},\"productSet\":{\"type\":\"string\",\"id\":6,\"options\":{\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"productCategories\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":7},\"filter\":{\"type\":\"string\",\"id\":8}}},\"ProductSearchResults\":{\"fields\":{\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":5},\"productGroupedResults\":{\"rule\":\"repeated\",\"type\":\"GroupedResult\",\"id\":6}},\"nested\":{\"Result\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"image\":{\"type\":\"string\",\"id\":3}}},\"ObjectAnnotation\":{\"fields\":{\"mid\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2},\"name\":{\"type\":\"string\",\"id\":3},\"score\":{\"type\":\"float\",\"id\":4}}},\"GroupedResult\":{\"fields\":{\"boundingPoly\":{\"type\":\"BoundingPoly\",\"id\":1},\"results\":{\"rule\":\"repeated\",\"type\":\"Result\",\"id\":2},\"objectAnnotations\":{\"rule\":\"repeated\",\"type\":\"ObjectAnnotation\",\"id\":3}}}}},\"ProductSearch\":{\"options\":{\"(google.api.default_host)\":\"vision.googleapis.com\",\"(google.api.oauth_scopes)\":\"https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/cloud-vision\"},\"methods\":{\"CreateProductSet\":{\"requestType\":\"CreateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"parent,product_set,product_set_id\"}]},\"ListProductSets\":{\"requestType\":\"ListProductSetsRequest\",\"responseType\":\"ListProductSetsResponse\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProductSet\":{\"requestType\":\"GetProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProductSet\":{\"requestType\":\"UpdateProductSetRequest\",\"responseType\":\"ProductSet\",\"options\":{\"(google.api.http).patch\":\"/v1p4beta1/{product_set.name=projects/*/locations/*/productSets/*}\",\"(google.api.http).body\":\"product_set\",\"(google.api.method_signature)\":\"product_set,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1p4beta1/{product_set.name=projects/*/locations/*/productSets/*}\",\"body\":\"product_set\"}},{\"(google.api.method_signature)\":\"product_set,update_mask\"}]},\"DeleteProductSet\":{\"requestType\":\"DeleteProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateProduct\":{\"requestType\":\"CreateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{parent=projects/*/locations/*}/products\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"parent,product,product_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{parent=projects/*/locations/*}/products\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"parent,product,product_id\"}]},\"ListProducts\":{\"requestType\":\"ListProductsRequest\",\"responseType\":\"ListProductsResponse\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{parent=projects/*/locations/*}/products\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{parent=projects/*/locations/*}/products\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetProduct\":{\"requestType\":\"GetProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"UpdateProduct\":{\"requestType\":\"UpdateProductRequest\",\"responseType\":\"Product\",\"options\":{\"(google.api.http).patch\":\"/v1p4beta1/{product.name=projects/*/locations/*/products/*}\",\"(google.api.http).body\":\"product\",\"(google.api.method_signature)\":\"product,update_mask\"},\"parsedOptions\":[{\"(google.api.http)\":{\"patch\":\"/v1p4beta1/{product.name=projects/*/locations/*/products/*}\",\"body\":\"product\"}},{\"(google.api.method_signature)\":\"product,update_mask\"}]},\"DeleteProduct\":{\"requestType\":\"DeleteProductRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p4beta1/{name=projects/*/locations/*/products/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p4beta1/{name=projects/*/locations/*/products/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CreateReferenceImage\":{\"requestType\":\"CreateReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.http).body\":\"reference_image\",\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"body\":\"reference_image\"}},{\"(google.api.method_signature)\":\"parent,reference_image,reference_image_id\"}]},\"DeleteReferenceImage\":{\"requestType\":\"DeleteReferenceImageRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1p4beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1p4beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ListReferenceImages\":{\"requestType\":\"ListReferenceImagesRequest\",\"responseType\":\"ListReferenceImagesResponse\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{parent=projects/*/locations/*/products/*}/referenceImages\",\"(google.api.method_signature)\":\"parent\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{parent=projects/*/locations/*/products/*}/referenceImages\"}},{\"(google.api.method_signature)\":\"parent\"}]},\"GetReferenceImage\":{\"requestType\":\"GetReferenceImageRequest\",\"responseType\":\"ReferenceImage\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{name=projects/*/locations/*/products/*/referenceImages/*}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"AddProductToProductSet\":{\"requestType\":\"AddProductToProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}:addProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"RemoveProductFromProductSet\":{\"requestType\":\"RemoveProductFromProductSetRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name,product\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}:removeProduct\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name,product\"}]},\"ListProductsInProductSet\":{\"requestType\":\"ListProductsInProductSetRequest\",\"responseType\":\"ListProductsInProductSetResponse\",\"options\":{\"(google.api.http).get\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}/products\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1p4beta1/{name=projects/*/locations/*/productSets/*}/products\"}},{\"(google.api.method_signature)\":\"name\"}]},\"ImportProductSets\":{\"requestType\":\"ImportProductSetsRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets:import\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"parent,input_config\",\"(google.longrunning.operation_info).response_type\":\"ImportProductSetsResponse\",\"(google.longrunning.operation_info).metadata_type\":\"BatchOperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{parent=projects/*/locations/*}/productSets:import\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"parent,input_config\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"ImportProductSetsResponse\",\"metadata_type\":\"BatchOperationMetadata\"}}]},\"PurgeProducts\":{\"requestType\":\"PurgeProductsRequest\",\"responseType\":\"google.longrunning.Operation\",\"options\":{\"(google.api.http).post\":\"/v1p4beta1/{parent=projects/*/locations/*}/products:purge\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"parent\",\"(google.longrunning.operation_info).response_type\":\"google.protobuf.Empty\",\"(google.longrunning.operation_info).metadata_type\":\"BatchOperationMetadata\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1p4beta1/{parent=projects/*/locations/*}/products:purge\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"parent\"},{\"(google.longrunning.operation_info)\":{\"response_type\":\"google.protobuf.Empty\",\"metadata_type\":\"BatchOperationMetadata\"}}]}}},\"Product\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/Product\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3},\"productCategory\":{\"type\":\"string\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"IMMUTABLE\"}},\"productLabels\":{\"rule\":\"repeated\",\"type\":\"KeyValue\",\"id\":5}},\"nested\":{\"KeyValue\":{\"fields\":{\"key\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"string\",\"id\":2}}}}},\"ProductSet\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ProductSet\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/productSets/{product_set}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"displayName\":{\"type\":\"string\",\"id\":2},\"indexTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}},\"indexError\":{\"type\":\"google.rpc.Status\",\"id\":4,\"options\":{\"(google.api.field_behavior)\":\"OUTPUT_ONLY\"}}}},\"ReferenceImage\":{\"options\":{\"(google.api.resource).type\":\"vision.googleapis.com/ReferenceImage\",\"(google.api.resource).pattern\":\"projects/{project}/locations/{location}/products/{product}/referenceImages/{reference_image}\"},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"uri\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"boundingPolys\":{\"rule\":\"repeated\",\"type\":\"BoundingPoly\",\"id\":3,\"options\":{\"(google.api.field_behavior)\":\"OPTIONAL\"}}}},\"CreateProductRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"product\":{\"type\":\"Product\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productId\":{\"type\":\"string\",\"id\":3}}},\"ListProductsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"UpdateProductRequest\":{\"fields\":{\"product\":{\"type\":\"Product\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"CreateProductSetRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"productSet\":{\"type\":\"ProductSet\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"productSetId\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductSetsResponse\":{\"fields\":{\"productSets\":{\"rule\":\"repeated\",\"type\":\"ProductSet\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"GetProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"UpdateProductSetRequest\":{\"fields\":{\"productSet\":{\"type\":\"ProductSet\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"updateMask\":{\"type\":\"google.protobuf.FieldMask\",\"id\":2}}},\"DeleteProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}}}},\"CreateReferenceImageRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"referenceImage\":{\"type\":\"ReferenceImage\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}},\"referenceImageId\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListReferenceImagesResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"nextPageToken\":{\"type\":\"string\",\"id\":3}}},\"GetReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"DeleteReferenceImageRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ReferenceImage\"}}}},\"AddProductToProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"RemoveProductFromProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"product\":{\"type\":\"string\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/Product\"}}}},\"ListProductsInProductSetRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"vision.googleapis.com/ProductSet\"}},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListProductsInProductSetResponse\":{\"fields\":{\"products\":{\"rule\":\"repeated\",\"type\":\"Product\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"ImportProductSetsGcsSource\":{\"fields\":{\"csvFileUri\":{\"type\":\"string\",\"id\":1}}},\"ImportProductSetsInputConfig\":{\"oneofs\":{\"source\":{\"oneof\":[\"gcsSource\"]}},\"fields\":{\"gcsSource\":{\"type\":\"ImportProductSetsGcsSource\",\"id\":1}}},\"ImportProductSetsRequest\":{\"fields\":{\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"inputConfig\":{\"type\":\"ImportProductSetsInputConfig\",\"id\":2,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\"}}}},\"ImportProductSetsResponse\":{\"fields\":{\"referenceImages\":{\"rule\":\"repeated\",\"type\":\"ReferenceImage\",\"id\":1},\"statuses\":{\"rule\":\"repeated\",\"type\":\"google.rpc.Status\",\"id\":2}}},\"BatchOperationMetadata\":{\"fields\":{\"state\":{\"type\":\"State\",\"id\":1},\"submitTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":2},\"endTime\":{\"type\":\"google.protobuf.Timestamp\",\"id\":3}},\"nested\":{\"State\":{\"values\":{\"STATE_UNSPECIFIED\":0,\"PROCESSING\":1,\"SUCCESSFUL\":2,\"FAILED\":3,\"CANCELLED\":4}}}},\"ProductSetPurgeConfig\":{\"fields\":{\"productSetId\":{\"type\":\"string\",\"id\":1}}},\"PurgeProductsRequest\":{\"oneofs\":{\"target\":{\"oneof\":[\"productSetPurgeConfig\",\"deleteOrphanProducts\"]}},\"fields\":{\"productSetPurgeConfig\":{\"type\":\"ProductSetPurgeConfig\",\"id\":2},\"deleteOrphanProducts\":{\"type\":\"bool\",\"id\":3},\"parent\":{\"type\":\"string\",\"id\":1,\"options\":{\"(google.api.field_behavior)\":\"REQUIRED\",\"(google.api.resource_reference).type\":\"locations.googleapis.com/Location\"}},\"force\":{\"type\":\"bool\",\"id\":4}}},\"TextAnnotation\":{\"fields\":{\"pages\":{\"rule\":\"repeated\",\"type\":\"Page\",\"id\":1},\"text\":{\"type\":\"string\",\"id\":2}},\"nested\":{\"DetectedLanguage\":{\"fields\":{\"languageCode\":{\"type\":\"string\",\"id\":1},\"confidence\":{\"type\":\"float\",\"id\":2}}},\"DetectedBreak\":{\"fields\":{\"type\":{\"type\":\"BreakType\",\"id\":1},\"isPrefix\":{\"type\":\"bool\",\"id\":2}},\"nested\":{\"BreakType\":{\"values\":{\"UNKNOWN\":0,\"SPACE\":1,\"SURE_SPACE\":2,\"EOL_SURE_SPACE\":3,\"HYPHEN\":4,\"LINE_BREAK\":5}}}},\"TextProperty\":{\"fields\":{\"detectedLanguages\":{\"rule\":\"repeated\",\"type\":\"DetectedLanguage\",\"id\":1},\"detectedBreak\":{\"type\":\"DetectedBreak\",\"id\":2}}}}},\"Page\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"width\":{\"type\":\"int32\",\"id\":2},\"height\":{\"type\":\"int32\",\"id\":3},\"blocks\":{\"rule\":\"repeated\",\"type\":\"Block\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}}},\"Block\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"paragraphs\":{\"rule\":\"repeated\",\"type\":\"Paragraph\",\"id\":3},\"blockType\":{\"type\":\"BlockType\",\"id\":4},\"confidence\":{\"type\":\"float\",\"id\":5}},\"nested\":{\"BlockType\":{\"values\":{\"UNKNOWN\":0,\"TEXT\":1,\"TABLE\":2,\"PICTURE\":3,\"RULER\":4,\"BARCODE\":5}}}},\"Paragraph\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"words\":{\"rule\":\"repeated\",\"type\":\"Word\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Word\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"symbols\":{\"rule\":\"repeated\",\"type\":\"Symbol\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"Symbol\":{\"fields\":{\"property\":{\"type\":\"TextAnnotation.TextProperty\",\"id\":1},\"boundingBox\":{\"type\":\"BoundingPoly\",\"id\":2},\"text\":{\"type\":\"string\",\"id\":3},\"confidence\":{\"type\":\"float\",\"id\":4}}},\"WebDetection\":{\"fields\":{\"webEntities\":{\"rule\":\"repeated\",\"type\":\"WebEntity\",\"id\":1},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":2},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":3},\"pagesWithMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebPage\",\"id\":4},\"visuallySimilarImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":6},\"bestGuessLabels\":{\"rule\":\"repeated\",\"type\":\"WebLabel\",\"id\":8}},\"nested\":{\"WebEntity\":{\"fields\":{\"entityId\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"description\":{\"type\":\"string\",\"id\":3}}},\"WebImage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2}}},\"WebPage\":{\"fields\":{\"url\":{\"type\":\"string\",\"id\":1},\"score\":{\"type\":\"float\",\"id\":2},\"pageTitle\":{\"type\":\"string\",\"id\":3},\"fullMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":4},\"partialMatchingImages\":{\"rule\":\"repeated\",\"type\":\"WebImage\",\"id\":5}}},\"WebLabel\":{\"fields\":{\"label\":{\"type\":\"string\",\"id\":1},\"languageCode\":{\"type\":\"string\",\"id\":2}}}}}}}}}}},\"api\":{\"options\":{\"go_package\":\"google.golang.org/genproto/googleapis/api/annotations;annotations\",\"java_multiple_files\":true,\"java_outer_classname\":\"ResourceProto\",\"java_package\":\"com.google.api\",\"objc_class_prefix\":\"GAPI\",\"cc_enable_arenas\":true},\"nested\":{\"http\":{\"type\":\"HttpRule\",\"id\":72295728,\"extend\":\"google.protobuf.MethodOptions\"},\"Http\":{\"fields\":{\"rules\":{\"rule\":\"repeated\",\"type\":\"HttpRule\",\"id\":1},\"fullyDecodeReservedExpansion\":{\"type\":\"bool\",\"id\":2}}},\"HttpRule\":{\"oneofs\":{\"pattern\":{\"oneof\":[\"get\",\"put\",\"post\",\"delete\",\"patch\",\"custom\"]}},\"fields\":{\"selector\":{\"type\":\"string\",\"id\":1},\"get\":{\"type\":\"string\",\"id\":2},\"put\":{\"type\":\"string\",\"id\":3},\"post\":{\"type\":\"string\",\"id\":4},\"delete\":{\"type\":\"string\",\"id\":5},\"patch\":{\"type\":\"string\",\"id\":6},\"custom\":{\"type\":\"CustomHttpPattern\",\"id\":8},\"body\":{\"type\":\"string\",\"id\":7},\"responseBody\":{\"type\":\"string\",\"id\":12},\"additionalBindings\":{\"rule\":\"repeated\",\"type\":\"HttpRule\",\"id\":11}}},\"CustomHttpPattern\":{\"fields\":{\"kind\":{\"type\":\"string\",\"id\":1},\"path\":{\"type\":\"string\",\"id\":2}}},\"methodSignature\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":1051,\"extend\":\"google.protobuf.MethodOptions\"},\"defaultHost\":{\"type\":\"string\",\"id\":1049,\"extend\":\"google.protobuf.ServiceOptions\"},\"oauthScopes\":{\"type\":\"string\",\"id\":1050,\"extend\":\"google.protobuf.ServiceOptions\"},\"CommonLanguageSettings\":{\"fields\":{\"referenceDocsUri\":{\"type\":\"string\",\"id\":1,\"options\":{\"deprecated\":true}},\"destinations\":{\"rule\":\"repeated\",\"type\":\"ClientLibraryDestination\",\"id\":2}}},\"ClientLibrarySettings\":{\"fields\":{\"version\":{\"type\":\"string\",\"id\":1},\"launchStage\":{\"type\":\"LaunchStage\",\"id\":2},\"restNumericEnums\":{\"type\":\"bool\",\"id\":3},\"javaSettings\":{\"type\":\"JavaSettings\",\"id\":21},\"cppSettings\":{\"type\":\"CppSettings\",\"id\":22},\"phpSettings\":{\"type\":\"PhpSettings\",\"id\":23},\"pythonSettings\":{\"type\":\"PythonSettings\",\"id\":24},\"nodeSettings\":{\"type\":\"NodeSettings\",\"id\":25},\"dotnetSettings\":{\"type\":\"DotnetSettings\",\"id\":26},\"rubySettings\":{\"type\":\"RubySettings\",\"id\":27},\"goSettings\":{\"type\":\"GoSettings\",\"id\":28}}},\"Publishing\":{\"fields\":{\"methodSettings\":{\"rule\":\"repeated\",\"type\":\"MethodSettings\",\"id\":2},\"newIssueUri\":{\"type\":\"string\",\"id\":101},\"documentationUri\":{\"type\":\"string\",\"id\":102},\"apiShortName\":{\"type\":\"string\",\"id\":103},\"githubLabel\":{\"type\":\"string\",\"id\":104},\"codeownerGithubTeams\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":105},\"docTagPrefix\":{\"type\":\"string\",\"id\":106},\"organization\":{\"type\":\"ClientLibraryOrganization\",\"id\":107},\"librarySettings\":{\"rule\":\"repeated\",\"type\":\"ClientLibrarySettings\",\"id\":109},\"protoReferenceDocumentationUri\":{\"type\":\"string\",\"id\":110}}},\"JavaSettings\":{\"fields\":{\"libraryPackage\":{\"type\":\"string\",\"id\":1},\"serviceClassNames\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":2},\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":3}}},\"CppSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"PhpSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"PythonSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"NodeSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"DotnetSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1},\"renamedServices\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":2},\"renamedResources\":{\"keyType\":\"string\",\"type\":\"string\",\"id\":3},\"ignoredResources\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":4},\"forcedNamespaceAliases\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":5},\"handwrittenSignatures\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":6}}},\"RubySettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"GoSettings\":{\"fields\":{\"common\":{\"type\":\"CommonLanguageSettings\",\"id\":1}}},\"MethodSettings\":{\"fields\":{\"selector\":{\"type\":\"string\",\"id\":1},\"longRunning\":{\"type\":\"LongRunning\",\"id\":2},\"autoPopulatedFields\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":3}},\"nested\":{\"LongRunning\":{\"fields\":{\"initialPollDelay\":{\"type\":\"google.protobuf.Duration\",\"id\":1},\"pollDelayMultiplier\":{\"type\":\"float\",\"id\":2},\"maxPollDelay\":{\"type\":\"google.protobuf.Duration\",\"id\":3},\"totalPollTimeout\":{\"type\":\"google.protobuf.Duration\",\"id\":4}}}}},\"ClientLibraryOrganization\":{\"values\":{\"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED\":0,\"CLOUD\":1,\"ADS\":2,\"PHOTOS\":3,\"STREET_VIEW\":4,\"SHOPPING\":5,\"GEO\":6,\"GENERATIVE_AI\":7}},\"ClientLibraryDestination\":{\"values\":{\"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED\":0,\"GITHUB\":10,\"PACKAGE_MANAGER\":20}},\"LaunchStage\":{\"values\":{\"LAUNCH_STAGE_UNSPECIFIED\":0,\"UNIMPLEMENTED\":6,\"PRELAUNCH\":7,\"EARLY_ACCESS\":1,\"ALPHA\":2,\"BETA\":3,\"GA\":4,\"DEPRECATED\":5}},\"fieldBehavior\":{\"rule\":\"repeated\",\"type\":\"google.api.FieldBehavior\",\"id\":1052,\"extend\":\"google.protobuf.FieldOptions\"},\"FieldBehavior\":{\"values\":{\"FIELD_BEHAVIOR_UNSPECIFIED\":0,\"OPTIONAL\":1,\"REQUIRED\":2,\"OUTPUT_ONLY\":3,\"INPUT_ONLY\":4,\"IMMUTABLE\":5,\"UNORDERED_LIST\":6,\"NON_EMPTY_DEFAULT\":7,\"IDENTIFIER\":8}},\"resourceReference\":{\"type\":\"google.api.ResourceReference\",\"id\":1055,\"extend\":\"google.protobuf.FieldOptions\"},\"resourceDefinition\":{\"rule\":\"repeated\",\"type\":\"google.api.ResourceDescriptor\",\"id\":1053,\"extend\":\"google.protobuf.FileOptions\"},\"resource\":{\"type\":\"google.api.ResourceDescriptor\",\"id\":1053,\"extend\":\"google.protobuf.MessageOptions\"},\"ResourceDescriptor\":{\"fields\":{\"type\":{\"type\":\"string\",\"id\":1},\"pattern\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":2},\"nameField\":{\"type\":\"string\",\"id\":3},\"history\":{\"type\":\"History\",\"id\":4},\"plural\":{\"type\":\"string\",\"id\":5},\"singular\":{\"type\":\"string\",\"id\":6},\"style\":{\"rule\":\"repeated\",\"type\":\"Style\",\"id\":10}},\"nested\":{\"History\":{\"values\":{\"HISTORY_UNSPECIFIED\":0,\"ORIGINALLY_SINGLE_PATTERN\":1,\"FUTURE_MULTI_PATTERN\":2}},\"Style\":{\"values\":{\"STYLE_UNSPECIFIED\":0,\"DECLARATIVE_FRIENDLY\":1}}}},\"ResourceReference\":{\"fields\":{\"type\":{\"type\":\"string\",\"id\":1},\"childType\":{\"type\":\"string\",\"id\":2}}}}},\"protobuf\":{\"options\":{\"go_package\":\"google.golang.org/protobuf/types/descriptorpb\",\"java_package\":\"com.google.protobuf\",\"java_outer_classname\":\"DescriptorProtos\",\"csharp_namespace\":\"Google.Protobuf.Reflection\",\"objc_class_prefix\":\"GPB\",\"cc_enable_arenas\":true,\"optimize_for\":\"SPEED\"},\"nested\":{\"FileDescriptorSet\":{\"fields\":{\"file\":{\"rule\":\"repeated\",\"type\":\"FileDescriptorProto\",\"id\":1}}},\"Edition\":{\"values\":{\"EDITION_UNKNOWN\":0,\"EDITION_PROTO2\":998,\"EDITION_PROTO3\":999,\"EDITION_2023\":1000,\"EDITION_2024\":1001,\"EDITION_1_TEST_ONLY\":1,\"EDITION_2_TEST_ONLY\":2,\"EDITION_99997_TEST_ONLY\":99997,\"EDITION_99998_TEST_ONLY\":99998,\"EDITION_99999_TEST_ONLY\":99999,\"EDITION_MAX\":2147483647}},\"FileDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"package\":{\"type\":\"string\",\"id\":2},\"dependency\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":3},\"publicDependency\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":10,\"options\":{\"packed\":false}},\"weakDependency\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":11,\"options\":{\"packed\":false}},\"messageType\":{\"rule\":\"repeated\",\"type\":\"DescriptorProto\",\"id\":4},\"enumType\":{\"rule\":\"repeated\",\"type\":\"EnumDescriptorProto\",\"id\":5},\"service\":{\"rule\":\"repeated\",\"type\":\"ServiceDescriptorProto\",\"id\":6},\"extension\":{\"rule\":\"repeated\",\"type\":\"FieldDescriptorProto\",\"id\":7},\"options\":{\"type\":\"FileOptions\",\"id\":8},\"sourceCodeInfo\":{\"type\":\"SourceCodeInfo\",\"id\":9},\"syntax\":{\"type\":\"string\",\"id\":12},\"edition\":{\"type\":\"Edition\",\"id\":14}}},\"DescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"field\":{\"rule\":\"repeated\",\"type\":\"FieldDescriptorProto\",\"id\":2},\"extension\":{\"rule\":\"repeated\",\"type\":\"FieldDescriptorProto\",\"id\":6},\"nestedType\":{\"rule\":\"repeated\",\"type\":\"DescriptorProto\",\"id\":3},\"enumType\":{\"rule\":\"repeated\",\"type\":\"EnumDescriptorProto\",\"id\":4},\"extensionRange\":{\"rule\":\"repeated\",\"type\":\"ExtensionRange\",\"id\":5},\"oneofDecl\":{\"rule\":\"repeated\",\"type\":\"OneofDescriptorProto\",\"id\":8},\"options\":{\"type\":\"MessageOptions\",\"id\":7},\"reservedRange\":{\"rule\":\"repeated\",\"type\":\"ReservedRange\",\"id\":9},\"reservedName\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":10}},\"nested\":{\"ExtensionRange\":{\"fields\":{\"start\":{\"type\":\"int32\",\"id\":1},\"end\":{\"type\":\"int32\",\"id\":2},\"options\":{\"type\":\"ExtensionRangeOptions\",\"id\":3}}},\"ReservedRange\":{\"fields\":{\"start\":{\"type\":\"int32\",\"id\":1},\"end\":{\"type\":\"int32\",\"id\":2}}}}},\"ExtensionRangeOptions\":{\"fields\":{\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999},\"declaration\":{\"rule\":\"repeated\",\"type\":\"Declaration\",\"id\":2,\"options\":{\"retention\":\"RETENTION_SOURCE\"}},\"features\":{\"type\":\"FeatureSet\",\"id\":50},\"verification\":{\"type\":\"VerificationState\",\"id\":3,\"options\":{\"default\":\"UNVERIFIED\",\"retention\":\"RETENTION_SOURCE\"}}},\"extensions\":[[1000,536870911]],\"nested\":{\"Declaration\":{\"fields\":{\"number\":{\"type\":\"int32\",\"id\":1},\"fullName\":{\"type\":\"string\",\"id\":2},\"type\":{\"type\":\"string\",\"id\":3},\"reserved\":{\"type\":\"bool\",\"id\":5},\"repeated\":{\"type\":\"bool\",\"id\":6}},\"reserved\":[[4,4]]},\"VerificationState\":{\"values\":{\"DECLARATION\":0,\"UNVERIFIED\":1}}}},\"FieldDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"number\":{\"type\":\"int32\",\"id\":3},\"label\":{\"type\":\"Label\",\"id\":4},\"type\":{\"type\":\"Type\",\"id\":5},\"typeName\":{\"type\":\"string\",\"id\":6},\"extendee\":{\"type\":\"string\",\"id\":2},\"defaultValue\":{\"type\":\"string\",\"id\":7},\"oneofIndex\":{\"type\":\"int32\",\"id\":9},\"jsonName\":{\"type\":\"string\",\"id\":10},\"options\":{\"type\":\"FieldOptions\",\"id\":8},\"proto3Optional\":{\"type\":\"bool\",\"id\":17}},\"nested\":{\"Type\":{\"values\":{\"TYPE_DOUBLE\":1,\"TYPE_FLOAT\":2,\"TYPE_INT64\":3,\"TYPE_UINT64\":4,\"TYPE_INT32\":5,\"TYPE_FIXED64\":6,\"TYPE_FIXED32\":7,\"TYPE_BOOL\":8,\"TYPE_STRING\":9,\"TYPE_GROUP\":10,\"TYPE_MESSAGE\":11,\"TYPE_BYTES\":12,\"TYPE_UINT32\":13,\"TYPE_ENUM\":14,\"TYPE_SFIXED32\":15,\"TYPE_SFIXED64\":16,\"TYPE_SINT32\":17,\"TYPE_SINT64\":18}},\"Label\":{\"values\":{\"LABEL_OPTIONAL\":1,\"LABEL_REPEATED\":3,\"LABEL_REQUIRED\":2}}}},\"OneofDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"options\":{\"type\":\"OneofOptions\",\"id\":2}}},\"EnumDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"value\":{\"rule\":\"repeated\",\"type\":\"EnumValueDescriptorProto\",\"id\":2},\"options\":{\"type\":\"EnumOptions\",\"id\":3},\"reservedRange\":{\"rule\":\"repeated\",\"type\":\"EnumReservedRange\",\"id\":4},\"reservedName\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":5}},\"nested\":{\"EnumReservedRange\":{\"fields\":{\"start\":{\"type\":\"int32\",\"id\":1},\"end\":{\"type\":\"int32\",\"id\":2}}}}},\"EnumValueDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"number\":{\"type\":\"int32\",\"id\":2},\"options\":{\"type\":\"EnumValueOptions\",\"id\":3}}},\"ServiceDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"method\":{\"rule\":\"repeated\",\"type\":\"MethodDescriptorProto\",\"id\":2},\"options\":{\"type\":\"ServiceOptions\",\"id\":3}}},\"MethodDescriptorProto\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"inputType\":{\"type\":\"string\",\"id\":2},\"outputType\":{\"type\":\"string\",\"id\":3},\"options\":{\"type\":\"MethodOptions\",\"id\":4},\"clientStreaming\":{\"type\":\"bool\",\"id\":5,\"options\":{\"default\":false}},\"serverStreaming\":{\"type\":\"bool\",\"id\":6,\"options\":{\"default\":false}}}},\"FileOptions\":{\"fields\":{\"javaPackage\":{\"type\":\"string\",\"id\":1},\"javaOuterClassname\":{\"type\":\"string\",\"id\":8},\"javaMultipleFiles\":{\"type\":\"bool\",\"id\":10,\"options\":{\"default\":false}},\"javaGenerateEqualsAndHash\":{\"type\":\"bool\",\"id\":20,\"options\":{\"deprecated\":true}},\"javaStringCheckUtf8\":{\"type\":\"bool\",\"id\":27,\"options\":{\"default\":false}},\"optimizeFor\":{\"type\":\"OptimizeMode\",\"id\":9,\"options\":{\"default\":\"SPEED\"}},\"goPackage\":{\"type\":\"string\",\"id\":11},\"ccGenericServices\":{\"type\":\"bool\",\"id\":16,\"options\":{\"default\":false}},\"javaGenericServices\":{\"type\":\"bool\",\"id\":17,\"options\":{\"default\":false}},\"pyGenericServices\":{\"type\":\"bool\",\"id\":18,\"options\":{\"default\":false}},\"deprecated\":{\"type\":\"bool\",\"id\":23,\"options\":{\"default\":false}},\"ccEnableArenas\":{\"type\":\"bool\",\"id\":31,\"options\":{\"default\":true}},\"objcClassPrefix\":{\"type\":\"string\",\"id\":36},\"csharpNamespace\":{\"type\":\"string\",\"id\":37},\"swiftPrefix\":{\"type\":\"string\",\"id\":39},\"phpClassPrefix\":{\"type\":\"string\",\"id\":40},\"phpNamespace\":{\"type\":\"string\",\"id\":41},\"phpMetadataNamespace\":{\"type\":\"string\",\"id\":44},\"rubyPackage\":{\"type\":\"string\",\"id\":45},\"features\":{\"type\":\"FeatureSet\",\"id\":50},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]],\"reserved\":[[42,42],[38,38]],\"nested\":{\"OptimizeMode\":{\"values\":{\"SPEED\":1,\"CODE_SIZE\":2,\"LITE_RUNTIME\":3}}}},\"MessageOptions\":{\"fields\":{\"messageSetWireFormat\":{\"type\":\"bool\",\"id\":1,\"options\":{\"default\":false}},\"noStandardDescriptorAccessor\":{\"type\":\"bool\",\"id\":2,\"options\":{\"default\":false}},\"deprecated\":{\"type\":\"bool\",\"id\":3,\"options\":{\"default\":false}},\"mapEntry\":{\"type\":\"bool\",\"id\":7},\"deprecatedLegacyJsonFieldConflicts\":{\"type\":\"bool\",\"id\":11,\"options\":{\"deprecated\":true}},\"features\":{\"type\":\"FeatureSet\",\"id\":12},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]],\"reserved\":[[4,4],[5,5],[6,6],[8,8],[9,9]]},\"FieldOptions\":{\"fields\":{\"ctype\":{\"type\":\"CType\",\"id\":1,\"options\":{\"default\":\"STRING\"}},\"packed\":{\"type\":\"bool\",\"id\":2},\"jstype\":{\"type\":\"JSType\",\"id\":6,\"options\":{\"default\":\"JS_NORMAL\"}},\"lazy\":{\"type\":\"bool\",\"id\":5,\"options\":{\"default\":false}},\"unverifiedLazy\":{\"type\":\"bool\",\"id\":15,\"options\":{\"default\":false}},\"deprecated\":{\"type\":\"bool\",\"id\":3,\"options\":{\"default\":false}},\"weak\":{\"type\":\"bool\",\"id\":10,\"options\":{\"default\":false}},\"debugRedact\":{\"type\":\"bool\",\"id\":16,\"options\":{\"default\":false}},\"retention\":{\"type\":\"OptionRetention\",\"id\":17},\"targets\":{\"rule\":\"repeated\",\"type\":\"OptionTargetType\",\"id\":19,\"options\":{\"packed\":false}},\"editionDefaults\":{\"rule\":\"repeated\",\"type\":\"EditionDefault\",\"id\":20},\"features\":{\"type\":\"FeatureSet\",\"id\":21},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]],\"reserved\":[[4,4],[18,18]],\"nested\":{\"CType\":{\"values\":{\"STRING\":0,\"CORD\":1,\"STRING_PIECE\":2}},\"JSType\":{\"values\":{\"JS_NORMAL\":0,\"JS_STRING\":1,\"JS_NUMBER\":2}},\"OptionRetention\":{\"values\":{\"RETENTION_UNKNOWN\":0,\"RETENTION_RUNTIME\":1,\"RETENTION_SOURCE\":2}},\"OptionTargetType\":{\"values\":{\"TARGET_TYPE_UNKNOWN\":0,\"TARGET_TYPE_FILE\":1,\"TARGET_TYPE_EXTENSION_RANGE\":2,\"TARGET_TYPE_MESSAGE\":3,\"TARGET_TYPE_FIELD\":4,\"TARGET_TYPE_ONEOF\":5,\"TARGET_TYPE_ENUM\":6,\"TARGET_TYPE_ENUM_ENTRY\":7,\"TARGET_TYPE_SERVICE\":8,\"TARGET_TYPE_METHOD\":9}},\"EditionDefault\":{\"fields\":{\"edition\":{\"type\":\"Edition\",\"id\":3},\"value\":{\"type\":\"string\",\"id\":2}}}}},\"OneofOptions\":{\"fields\":{\"features\":{\"type\":\"FeatureSet\",\"id\":1},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]]},\"EnumOptions\":{\"fields\":{\"allowAlias\":{\"type\":\"bool\",\"id\":2},\"deprecated\":{\"type\":\"bool\",\"id\":3,\"options\":{\"default\":false}},\"deprecatedLegacyJsonFieldConflicts\":{\"type\":\"bool\",\"id\":6,\"options\":{\"deprecated\":true}},\"features\":{\"type\":\"FeatureSet\",\"id\":7},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]],\"reserved\":[[5,5]]},\"EnumValueOptions\":{\"fields\":{\"deprecated\":{\"type\":\"bool\",\"id\":1,\"options\":{\"default\":false}},\"features\":{\"type\":\"FeatureSet\",\"id\":2},\"debugRedact\":{\"type\":\"bool\",\"id\":3,\"options\":{\"default\":false}},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]]},\"ServiceOptions\":{\"fields\":{\"features\":{\"type\":\"FeatureSet\",\"id\":34},\"deprecated\":{\"type\":\"bool\",\"id\":33,\"options\":{\"default\":false}},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]]},\"MethodOptions\":{\"fields\":{\"deprecated\":{\"type\":\"bool\",\"id\":33,\"options\":{\"default\":false}},\"idempotencyLevel\":{\"type\":\"IdempotencyLevel\",\"id\":34,\"options\":{\"default\":\"IDEMPOTENCY_UNKNOWN\"}},\"features\":{\"type\":\"FeatureSet\",\"id\":35},\"uninterpretedOption\":{\"rule\":\"repeated\",\"type\":\"UninterpretedOption\",\"id\":999}},\"extensions\":[[1000,536870911]],\"nested\":{\"IdempotencyLevel\":{\"values\":{\"IDEMPOTENCY_UNKNOWN\":0,\"NO_SIDE_EFFECTS\":1,\"IDEMPOTENT\":2}}}},\"UninterpretedOption\":{\"fields\":{\"name\":{\"rule\":\"repeated\",\"type\":\"NamePart\",\"id\":2},\"identifierValue\":{\"type\":\"string\",\"id\":3},\"positiveIntValue\":{\"type\":\"uint64\",\"id\":4},\"negativeIntValue\":{\"type\":\"int64\",\"id\":5},\"doubleValue\":{\"type\":\"double\",\"id\":6},\"stringValue\":{\"type\":\"bytes\",\"id\":7},\"aggregateValue\":{\"type\":\"string\",\"id\":8}},\"nested\":{\"NamePart\":{\"fields\":{\"namePart\":{\"rule\":\"required\",\"type\":\"string\",\"id\":1},\"isExtension\":{\"rule\":\"required\",\"type\":\"bool\",\"id\":2}}}}},\"FeatureSet\":{\"fields\":{\"fieldPresence\":{\"type\":\"FieldPresence\",\"id\":1,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_2023\",\"edition_defaults.value\":\"EXPLICIT\"}},\"enumType\":{\"type\":\"EnumType\",\"id\":2,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_PROTO3\",\"edition_defaults.value\":\"OPEN\"}},\"repeatedFieldEncoding\":{\"type\":\"RepeatedFieldEncoding\",\"id\":3,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_PROTO3\",\"edition_defaults.value\":\"PACKED\"}},\"utf8Validation\":{\"type\":\"Utf8Validation\",\"id\":4,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_PROTO3\",\"edition_defaults.value\":\"VERIFY\"}},\"messageEncoding\":{\"type\":\"MessageEncoding\",\"id\":5,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_PROTO2\",\"edition_defaults.value\":\"LENGTH_PREFIXED\"}},\"jsonFormat\":{\"type\":\"JsonFormat\",\"id\":6,\"options\":{\"retention\":\"RETENTION_RUNTIME\",\"targets\":\"TARGET_TYPE_FILE\",\"edition_defaults.edition\":\"EDITION_PROTO3\",\"edition_defaults.value\":\"ALLOW\"}}},\"extensions\":[[1000,1000],[1001,1001],[9995,9999]],\"reserved\":[[999,999]],\"nested\":{\"FieldPresence\":{\"values\":{\"FIELD_PRESENCE_UNKNOWN\":0,\"EXPLICIT\":1,\"IMPLICIT\":2,\"LEGACY_REQUIRED\":3}},\"EnumType\":{\"values\":{\"ENUM_TYPE_UNKNOWN\":0,\"OPEN\":1,\"CLOSED\":2}},\"RepeatedFieldEncoding\":{\"values\":{\"REPEATED_FIELD_ENCODING_UNKNOWN\":0,\"PACKED\":1,\"EXPANDED\":2}},\"Utf8Validation\":{\"values\":{\"UTF8_VALIDATION_UNKNOWN\":0,\"VERIFY\":2,\"NONE\":3}},\"MessageEncoding\":{\"values\":{\"MESSAGE_ENCODING_UNKNOWN\":0,\"LENGTH_PREFIXED\":1,\"DELIMITED\":2}},\"JsonFormat\":{\"values\":{\"JSON_FORMAT_UNKNOWN\":0,\"ALLOW\":1,\"LEGACY_BEST_EFFORT\":2}}}},\"FeatureSetDefaults\":{\"fields\":{\"defaults\":{\"rule\":\"repeated\",\"type\":\"FeatureSetEditionDefault\",\"id\":1},\"minimumEdition\":{\"type\":\"Edition\",\"id\":4},\"maximumEdition\":{\"type\":\"Edition\",\"id\":5}},\"nested\":{\"FeatureSetEditionDefault\":{\"fields\":{\"edition\":{\"type\":\"Edition\",\"id\":3},\"features\":{\"type\":\"FeatureSet\",\"id\":2}}}}},\"SourceCodeInfo\":{\"fields\":{\"location\":{\"rule\":\"repeated\",\"type\":\"Location\",\"id\":1}},\"nested\":{\"Location\":{\"fields\":{\"path\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":1},\"span\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":2},\"leadingComments\":{\"type\":\"string\",\"id\":3},\"trailingComments\":{\"type\":\"string\",\"id\":4},\"leadingDetachedComments\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":6}}}}},\"GeneratedCodeInfo\":{\"fields\":{\"annotation\":{\"rule\":\"repeated\",\"type\":\"Annotation\",\"id\":1}},\"nested\":{\"Annotation\":{\"fields\":{\"path\":{\"rule\":\"repeated\",\"type\":\"int32\",\"id\":1},\"sourceFile\":{\"type\":\"string\",\"id\":2},\"begin\":{\"type\":\"int32\",\"id\":3},\"end\":{\"type\":\"int32\",\"id\":4},\"semantic\":{\"type\":\"Semantic\",\"id\":5}},\"nested\":{\"Semantic\":{\"values\":{\"NONE\":0,\"SET\":1,\"ALIAS\":2}}}}}},\"Duration\":{\"fields\":{\"seconds\":{\"type\":\"int64\",\"id\":1},\"nanos\":{\"type\":\"int32\",\"id\":2}}},\"Any\":{\"fields\":{\"type_url\":{\"type\":\"string\",\"id\":1},\"value\":{\"type\":\"bytes\",\"id\":2}}},\"Empty\":{\"fields\":{}},\"FieldMask\":{\"fields\":{\"paths\":{\"rule\":\"repeated\",\"type\":\"string\",\"id\":1}}},\"Timestamp\":{\"fields\":{\"seconds\":{\"type\":\"int64\",\"id\":1},\"nanos\":{\"type\":\"int32\",\"id\":2}}},\"DoubleValue\":{\"fields\":{\"value\":{\"type\":\"double\",\"id\":1}}},\"FloatValue\":{\"fields\":{\"value\":{\"type\":\"float\",\"id\":1}}},\"Int64Value\":{\"fields\":{\"value\":{\"type\":\"int64\",\"id\":1}}},\"UInt64Value\":{\"fields\":{\"value\":{\"type\":\"uint64\",\"id\":1}}},\"Int32Value\":{\"fields\":{\"value\":{\"type\":\"int32\",\"id\":1}}},\"UInt32Value\":{\"fields\":{\"value\":{\"type\":\"uint32\",\"id\":1}}},\"BoolValue\":{\"fields\":{\"value\":{\"type\":\"bool\",\"id\":1}}},\"StringValue\":{\"fields\":{\"value\":{\"type\":\"string\",\"id\":1}}},\"BytesValue\":{\"fields\":{\"value\":{\"type\":\"bytes\",\"id\":1}}}}},\"longrunning\":{\"options\":{\"cc_enable_arenas\":true,\"csharp_namespace\":\"Google.LongRunning\",\"go_package\":\"cloud.google.com/go/longrunning/autogen/longrunningpb;longrunningpb\",\"java_multiple_files\":true,\"java_outer_classname\":\"OperationsProto\",\"java_package\":\"com.google.longrunning\",\"php_namespace\":\"Google\\\\LongRunning\"},\"nested\":{\"operationInfo\":{\"type\":\"google.longrunning.OperationInfo\",\"id\":1049,\"extend\":\"google.protobuf.MethodOptions\"},\"Operations\":{\"options\":{\"(google.api.default_host)\":\"longrunning.googleapis.com\"},\"methods\":{\"ListOperations\":{\"requestType\":\"ListOperationsRequest\",\"responseType\":\"ListOperationsResponse\",\"options\":{\"(google.api.http).get\":\"/v1/{name=operations}\",\"(google.api.method_signature)\":\"name,filter\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=operations}\"}},{\"(google.api.method_signature)\":\"name,filter\"}]},\"GetOperation\":{\"requestType\":\"GetOperationRequest\",\"responseType\":\"Operation\",\"options\":{\"(google.api.http).get\":\"/v1/{name=operations/**}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"get\":\"/v1/{name=operations/**}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"DeleteOperation\":{\"requestType\":\"DeleteOperationRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).delete\":\"/v1/{name=operations/**}\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"delete\":\"/v1/{name=operations/**}\"}},{\"(google.api.method_signature)\":\"name\"}]},\"CancelOperation\":{\"requestType\":\"CancelOperationRequest\",\"responseType\":\"google.protobuf.Empty\",\"options\":{\"(google.api.http).post\":\"/v1/{name=operations/**}:cancel\",\"(google.api.http).body\":\"*\",\"(google.api.method_signature)\":\"name\"},\"parsedOptions\":[{\"(google.api.http)\":{\"post\":\"/v1/{name=operations/**}:cancel\",\"body\":\"*\"}},{\"(google.api.method_signature)\":\"name\"}]},\"WaitOperation\":{\"requestType\":\"WaitOperationRequest\",\"responseType\":\"Operation\"}}},\"Operation\":{\"oneofs\":{\"result\":{\"oneof\":[\"error\",\"response\"]}},\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"metadata\":{\"type\":\"google.protobuf.Any\",\"id\":2},\"done\":{\"type\":\"bool\",\"id\":3},\"error\":{\"type\":\"google.rpc.Status\",\"id\":4},\"response\":{\"type\":\"google.protobuf.Any\",\"id\":5}}},\"GetOperationRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1}}},\"ListOperationsRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":4},\"filter\":{\"type\":\"string\",\"id\":1},\"pageSize\":{\"type\":\"int32\",\"id\":2},\"pageToken\":{\"type\":\"string\",\"id\":3}}},\"ListOperationsResponse\":{\"fields\":{\"operations\":{\"rule\":\"repeated\",\"type\":\"Operation\",\"id\":1},\"nextPageToken\":{\"type\":\"string\",\"id\":2}}},\"CancelOperationRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1}}},\"DeleteOperationRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1}}},\"WaitOperationRequest\":{\"fields\":{\"name\":{\"type\":\"string\",\"id\":1},\"timeout\":{\"type\":\"google.protobuf.Duration\",\"id\":2}}},\"OperationInfo\":{\"fields\":{\"responseType\":{\"type\":\"string\",\"id\":1},\"metadataType\":{\"type\":\"string\",\"id\":2}}}}},\"rpc\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"google.golang.org/genproto/googleapis/rpc/status;status\",\"java_multiple_files\":true,\"java_outer_classname\":\"StatusProto\",\"java_package\":\"com.google.rpc\",\"objc_class_prefix\":\"RPC\"},\"nested\":{\"Status\":{\"fields\":{\"code\":{\"type\":\"int32\",\"id\":1},\"message\":{\"type\":\"string\",\"id\":2},\"details\":{\"rule\":\"repeated\",\"type\":\"google.protobuf.Any\",\"id\":3}}}}},\"type\":{\"options\":{\"cc_enable_arenas\":true,\"go_package\":\"google.golang.org/genproto/googleapis/type/latlng;latlng\",\"java_multiple_files\":true,\"java_outer_classname\":\"LatLngProto\",\"java_package\":\"com.google.type\",\"objc_class_prefix\":\"GTP\"},\"nested\":{\"Color\":{\"fields\":{\"red\":{\"type\":\"float\",\"id\":1},\"green\":{\"type\":\"float\",\"id\":2},\"blue\":{\"type\":\"float\",\"id\":3},\"alpha\":{\"type\":\"google.protobuf.FloatValue\",\"id\":4}}},\"LatLng\":{\"fields\":{\"latitude\":{\"type\":\"double\",\"id\":1},\"longitude\":{\"type\":\"double\",\"id\":2}}}}}}}}}"));}}),

};